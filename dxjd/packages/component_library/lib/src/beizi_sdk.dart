import 'dart:io';

import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/mainController.dart';
import 'package:dxjd/tab_container_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:home/home.dart';
import 'package:home_repository/home_repository.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:get/get.dart';
import 'package:user_repository/user_repository.dart';

typedef Callback = void Function(double height);
final valueNotifier = ValueNotifier<double>(100.0);

class BeiziAdPlugin {
  static BeiziAdPlugin? _instance;

  BeiziAdPlugin._internal();

  static BeiziAdPlugin get instance {
    _instance ??= BeiziAdPlugin._internal();
    return _instance!;
  }

  /// 通道名，需和iOS、android端保持一致
  final MethodChannel _channel = const MethodChannel('xw.dxjk.beizi_ad');
  AdvertisList advertisementList = AdvertisList();
  AdvertisList splashAdvertisementList = AdvertisList();
//  SDK是否初始化
  bool isInit = false;
//  播放中
  bool advertIsPlay = false;
//  计时规则禁止
  bool isTimingBan = false;
//  激励类型
  int? motivateType;
// 禁止开屏广告
  bool advertIsBanPlay = false;
//  切换限制时间
  int minutes = 1;
//  初始化成功下一步广告类型 1:开屏 2:激励视频 3:插屏 4信息流
  int advertisementType = 1;
//  解锁点位图的产品ID
  String pointProductID = '0';
// 信息流广告获取是否成功
  bool informationAdSuccess = false;
  //信息流广告刷新时间
  int informationRefreshTime = 15;
  //是否关闭信息流广告
  Rx<bool> isCloseInformation = false.obs;
  // 是否预加载开屏广告 如果预加载后已经展示设置为false;
  bool isPreLoadSplashAd = false;

  //禁止冷启动过程
  // bool isBanCoolStartProcess =false;
//  原生通道监听
  void init() {
    /// 设置原生调用Flutter时的回调
    _channel.setMethodCallHandler((call) async {
      debugPrint("flutter收到新事件${call.method}参数为------${call.arguments}");

      switch (call.method) {
        case "setupSuccess":
          {
            debugPrint("广告初始化成功");
            isInit = true;
            initSdkNext();
            break;
          }
        case "setupFailed":
          {
            debugPrint("广告初始化失败");
            if (MainController.isFirstOpen) {
              Get.offAndToNamed('/home');
            }
            break;
          }
        case "splashAdLoadSuccess":
          {
            debugPrint("开屏广告开始");
            break;
          }
        case "splashAdOnShow":
          {
            // Get.offAndToNamed('/home');
            debugPrint("开屏广告开始");
            // isBanCoolStartProcess = true;
            // TabContainerScreenState.hideOverlay();
            setAdvertisementStarting();
            break;
          }
        case "splashAdLoadFailed":
          {
            debugPrint("开屏广告加载失败");
            // if(BeiziAdPlugin.instance.advertIsBanPlay&&BeiziAdPlugin.instance.isBanCoolStartProcess){
            //   TabContainerScreenState.hideOverlay();
            // }
            setAdvertisementStop();
            if (MainController.isFirstOpen) {
              Get.offAndToNamed('/home');
            }
            break;
          }
        case "splashAdOnClose":
          {
            debugPrint("开屏广告结束");
            setAdvertisementStop();
            if (MainController.isFirstOpen) {
              Get.offAndToNamed('/home');
            }
            break;
          }
        case "rewardVideoAdBeRewarded":
          {
            //激励视频达成奖励
            setVipStartTime();
            break;
          }
        case "rewardVideoAdOnClose":
          {
            //激励视频结束
            EasyLoading.dismiss();
            setAdvertisementStop();
            break;
          }
        case "rewardVideoAdOnShow":
          {
            // "激励视频开始"
            debugPrint("激励视频开始");
            setAdvertisementStarting();
            break;
          }
        case "rewardVideoAdLoadFailed":
          {
            // 激励视频失败
            EasyLoading.dismiss();
            break;
          }
        case "rewardVideoAdShowFailed":
          {
            // 激励视频失败
            EasyLoading.dismiss();
            break;
          }
        case "rewardVideoAdLoadSuccess":
          {
            // 激励加载成功
            EasyLoading.dismiss();
            break;
          }
        case "interstitialAdLoadSuccess":
          {
            // 插屏广告加载成功
            break;
          }
        case "interstitialAdOnShow":
          {
            // 插屏广告显示成功
            setAdvertisementStarting();
            break;
          }
        case "interstitialAdLoadFailed":
          {
            // 插屏广告失败
            break;
          }
        case "interstitialAdShowFailed":
          {
            // 插屏广告失败
            break;
          }
        case "interstitialAdOnClose":
          {
            // 插屏广告结束
            setAdvertisementStop();
            break;
          }
        case "nativeAdLoadSuccess":
          {
            // 信息流广告加载成功
            informationAdSuccess = true;
          }
        case "nativeAdLoadFail":
          {
            // 信息流广告加载失败
          }
        case "nativeAdRenderSuccess":
          {
            // 信息流广告渲染成功
          }
        case "nativeAdDidClose":
          {
            // 信息流广告关闭
          }
        case "nativeAdOnTapClose":
          {
            // 信息流广告点击关闭
            setIsInformationClose = true;
            if (Get.isRegistered<ExaminationController>(
                tag: ExaminationPageState.key.toString())) {
              ExaminationController controller = Get.find<ExaminationController>(
                  tag: ExaminationPageState.key.toString());
              // controller.refreshAdTimerPause();
            }
          }
        default:
          break;
      }
    });
  }

  set setIsInformationClose(bool isCloseInformation) {
    this.isCloseInformation.value = isCloseInformation;
  }

  set setIsPreLoadSplashAd(bool isPreLoadSplashAd){
    this.isPreLoadSplashAd = isPreLoadSplashAd;
  }

//  广告结束
  setAdvertisementStop() {
    advertIsPlay = false;
  }

  //广告进行中
  setAdvertisementStarting() {
    advertIsPlay = true;
  }

//  统计需要的数据
  Map countData = {};
  //数据注入 每次调用广告前需要注入
  // void injectData(Map<String, dynamic> data) {}

  //获取开屏广告数据
  ListData? getSplashData() {
    List<ListData>? result = splashAdvertisementList.list
        ?.where((element) => element.type == 1)
        .toList();
    if (result?.isEmpty ?? false) {
      return null;
    } else {
      return result?[0];
    }
  }

  //获取插屏广告数据
  ListData? getInsertData() {
    List<ListData>? result = advertisementList.list
        ?.where(
          (element) => element.type == 3,
        )
        .toList();
    if (result?.isEmpty ?? false) {
      return null;
    } else {
      return result?[0];
    }
  }

  //获取信息流广告数据
  ListData? getInformationData() {
    List<ListData>? result = advertisementList.list
        ?.where(
          (element) => element.type == 6,
        )
        .toList();
    if (result?.isEmpty ?? false) {
      return null;
    } else {
      return result?[0];
    }
  }

  //初始化信息流数据或刷新广告
  initInformationOrRefresh() {
    if (informationAdSuccess) {
      refreshInformationMethod(getInformationData());
    } else {
      getInformationMethod(getInformationData());
    }
  }

  //设置时间
  setMinutes() {
    if (splashAdvertisementList.minutesBeiZi == null) {
      minutes = splashAdvertisementList.minutes ?? 1;
    } else {
      minutes = splashAdvertisementList.minutesBeiZi!;
    }
  }

  // type 1:科目二3d练车，2:科目三灯光模拟，3:科目三-考场点位图 4 科目三3d练车
  //获取激励广告数据
  ListData? getAdvertisData(int type) {
    List<ListData>? list =
        advertisementList.list?.where((element) => element.type == 4).toList();
    if (list == null || list.isEmpty) {
      return null;
    }
    String name = "";
    motivateType = type;
    if (type == 1) {
      name = "dxjd-vip2-3dlc";
    } else if (type == 2) {
      name = "dxjd-vip3-dgmn";
    } else if (type == 3) {
      name = "dxjd-vip3-kcdwt";
    } else if (type == 4) {
      name = "dxjd-vip3-3dlc";
    }
    List<ListData>? result =
        list.where((element) => element.sign == name).toList();
    if (result.isEmpty) {
      return null;
    } else {
      return result[0];
    }
  }

  //设置获取权益时间
  setVipStartTime() async {
    // 获取当前时间
    DateTime now = DateTime.now();
    // 获取当前时间的时间戳（秒级）
    int timestampInSeconds = now.millisecondsSinceEpoch ~/ 1000;
    HomeRepository homeRepository = Get.find<MainController>().homeRepository;
    UserRepository userRepository = Get.find<MainController>().userRepository;
    if (motivateType == 1) {
      await PreferencesService()
          .setInt('23dliancheExperienceStartTime', timestampInSeconds);
      Get.find<SectionTwoController>(tag: ExaminationPageState.key.toString(),).getCountdown();
      showGetVipDialog("科二3D练车免费时长");
    } else if (motivateType == 2) {
      await PreferencesService()
          .setInt('dengguangmoniExperienceStartTime', timestampInSeconds);
      Get.find<SectionThreeController>(tag: ExaminationPageState.key.toString(),).getCountdown();
      showGetVipDialog("科三灯光模拟免费时长");
    } else if (motivateType == 3) {
      try {
        // 开通点位图vip
        await homeRepository.openSubjectThreeExaminationPoint(
            await userRepository.getUserToken() ?? "", pointProductID);
        Future.delayed(Duration(seconds: 1), () async {
          // 刷新vip开通状态
          await Get.find<SectionThreeController>(tag: ExaminationPageState.key.toString(),).getPointVipOpenConfig();
          if (userRepository.pointVipMap[pointProductID] ?? false) {
            showGetVipDialog("科三考场线路点位图", isPointVip: true);
            // 刷新页面
            if (Get.isRegistered<SubjectThreeCatalogController>()) {
              Get.find<SubjectThreeCatalogController>().update();
            }
            Get.find<SectionThreeController>(tag: ExaminationPageState.key.toString(),).update();
            if (Get.isRegistered<DetailPointMapController>()) {
              Get.find<DetailPointMapController>().update();
            }
          } else {
            Toast.show('开通失败');
          }
        });
      } catch (e) {
        Toast.show("开通点位图vip失败");
      }
    } else if (motivateType == 4) {
      PreferencesService()
          .setInt('33dliancheExperienceStartTime', timestampInSeconds);
      showGetVipDialog("科三3D练车免费时长");
    }
    homeRepository.countAdNum(await userRepository.getUserToken() ?? "");
  }

//  权益弹窗
  showGetVipDialog(String content, {isPointVip = false}) {
    showDialog(
        context: Get.context!,
        builder: (c) {
          return Dialog(
            backgroundColor: Colors.transparent,
            insetPadding: REdgeInsets.symmetric(horizontal: 28),
            child: Stack(
              children: [
                Container(
                  margin: isPointVip
                      ? REdgeInsets.only(top: 48.h)
                      : EdgeInsets.zero,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.r)),
                  padding: REdgeInsets.symmetric(horizontal: 35, vertical: 24),
                  width: double.infinity,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      const Text(
                        '解锁成功',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 33.0),
                      Text(
                        isPointVip ? "已获得一张" : '已获得30分钟',
                        style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.privacy_dialog_titile_color,
                            fontWeight: FontWeight.w400),
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      Text(
                        content,
                        style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.privacy_dialog_titile_color),
                      ),
                      SizedBox(height: 48.h),
                      isPointVip
                          ? SizedBox()
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: <Widget>[
                                SizedBox(
                                  width: 112.w,
                                  child: TextButton(
                                    style: ElevatedButton.styleFrom(
                                      foregroundColor: Colors.black,
                                      backgroundColor: Colors.white, // 文字颜色
                                      side: const BorderSide(
                                          color: Colors.grey), // 边框颜色
                                    ),
                                    child: const Text('取消'),
                                    onPressed: () {
                                      Get.back();
                                    },
                                  ),
                                ),
                                SizedBox(
                                  width: 112.w,
                                  child: TextButton(
                                    style: ElevatedButton.styleFrom(
                                      foregroundColor: Colors.white,
                                      backgroundColor: Colors.blue, // 文字颜色
                                    ),
                                    child: const Text('去使用'),
                                    onPressed: () {
                                      Get.back();
                                      // 这里可以添加去使用的逻辑
                                    },
                                  ),
                                ),
                              ],
                            ),
                    ],
                  ),
                ),
                Positioned(
                  right: 0.w,
                  top: 0.h,
                  child: GestureDetector(
                    // 关闭按钮
                    child: Image.asset(
                      'assets/home_img/icon_guanbi.png',
                      width: 32.w,
                      height: 32.w,
                    ),
                    onTap: () {
                      Get.back();
                    },
                  ),
                ),
              ],
            ),
          );
        });
  }

  /// sdk初始化方法
  Future<String?> adSdkSetup() async {
    if (Platform.isIOS) {
      debugPrint('ios --------------');
    } else {
      debugPrint('android --------------');
    }
    final String? version = await _channel.invokeMethod(
        'cjSdkSetup', {"configId": Platform.isIOS ? "54698" : "54697"});
    return version;
  }

//  初始化sdk
  initSdk() async {
    init();
    adSdkSetup();
  }

//  初始化下一步
  initSdkNext() async {
    if (advertisementType == 1) {
      if (splashAdvertisementList.list == null) {
        MainController mainController = Get.find<MainController>();
        await mainController.homeRepository.getSlashAdvertisingConfig();
      }
      ListData? list = BeiziAdPlugin.instance.getSplashData();
      getSplashAdMethod(list);
    } else {
      if (advertisementList.list == null && MainController.isExistToken) {
        MainController mainController = Get.find<MainController>();
        await mainController.homeRepository.getAdvertisingConfig(
            await mainController.userRepository.getUserToken() ?? "");
      }
      if (advertisementType == 2) {
        ListData? list = getAdvertisData(motivateType!);
        getRewardVideoMethod(list);
      } else if (advertisementType == 3) {
        ListData? list = getInsertData();
        getInterstitialAdMethod(list);
      } else if (advertisementType == 6) {
        initInformationOrRefresh();
      }
    }
  }

  /// 开屏
  Future<String> getSplashAdMethod(ListData? splashData,
      {bool isFirstSlash = true}) async {
    if (!isInit) {
      await initSdk();
      advertisementType = 1;
      return "重新初始化sdk";
    }
    if (advertIsPlay || advertIsBanPlay) {
      advertIsBanPlay = false;
      return "广告正在播放中";
    }
    if ((splashData?.adId ?? "").isEmpty) {
      // Toast.show("广告加载失败");
      if (MainController.isFirstOpen) {
        Get.offAndToNamed('/home');
      }
      return "广告开屏id为空";
    }
    setIsPreLoadSplashAd = false;
    final state = await _channel.invokeMethod(
        'cjLoadAndShowSplashMethod', {"advertId": splashData?.adId});
    return state;
  }

  /// 开屏预加载
  Future<String> getPreSplashAdMethod(ListData? splashData) async {
    if (!isInit) {
      await initSdk();
      advertisementType = 1;
      return "重新初始化sdk";
    }
    if (advertIsPlay) {
      return "广告正在播放中";
    }
    if ((splashData?.adId ?? "").isEmpty) {
      // Toast.show("广告加载失败");
      if (MainController.isFirstOpen) {
        Get.offAndToNamed('/home');
      }
      return "广告开屏id为空";
    }
    setIsPreLoadSplashAd = true;
    final state = await _channel.invokeMethod(
        'cjPreLoadSplashMethod', {"advertId": splashData?.adId});
    return state;
  }

  /// 激励视频
  Future getRewardVideoMethod(ListData? rewardVideoData) async {
    if (!isInit) {
      await initSdk();
      advertisementType = 2;
      return '重新初始化sdk';
    }
    if (isTimingBan) {
      EasyLoading.dismiss();
      return "广告禁止播放";
    }
    if ((rewardVideoData?.adId ?? "").isEmpty) {
      EasyLoading.dismiss();
      // Toast.show("广告加载失败");
      return "广告开屏id为空";
    }
    // ignore: avoid_print
    final result = await _channel.invokeMethod(
        'cjLoadAndShowRewardVideoMethod', {"advertId": rewardVideoData!.adId});
    // ignore: avoid_print
    return result;
  }

  // 获取默认激励视频广告位ID
  // getDefaultRewardId() {
  //   if (Platform.isIOS) {
  //     if (motivateType == 1) {
  //       return "1d1904feea638526";
  //     } else if (motivateType == 2) {
  //       return "06e904026f27e68e";
  //     } else if (motivateType == 3) {
  //       return "505ed762db3d1c41";
  //     } else {
  //       Toast.show("广告初始化失败");
  //     }
  //   } else {
  //     if (motivateType == 1) {
  //       return "c0a5a4e54b27bb19";
  //     } else if (motivateType == 2) {
  //       return "694653540c5b0ad8";
  //     } else if (motivateType == 3) {
  //       return "b152090985cc9abc";
  //     } else {
  //       Toast.show("广告初始化失败");
  //     }
  //   }
  // }

  // Future<Map> preLoadReward() async {
  //   // ignore: avoid_print
  //   final Map result = await _channel.invokeMethod(
  //       'preReward', {"advertId": testAdvertModel.getRewardVideoId()});
  //   // ignore: avoid_print
  //   return result;
  // }
  //
  // /// 全屏视频
  // Future<int> getFullscreenVideoMethod() async {
  //   final int result = await _channel.invokeMethod(
  //       'cjLoadAndShowFullscreenVideoAdMethod',
  //       {"advertId": testAdvertModel.getFullscreenVideoId()});
  //   return result;
  // }

  /// 插屏
  Future<String> getInterstitialAdMethod(ListData? insertVideoData) async {
    if (!isInit) {
      await initSdk();
      advertisementType = 3;
      return '重新初始化sdk';
    }
    if (isTimingBan) {
      return "广告禁止播放";
    }
    if ((insertVideoData?.adId ?? "").isEmpty) {
      // Toast.show("广告加载失败");
      return "广告开屏id为空";
    }
    final result = await _channel.invokeMethod(
        'cjLoadAndShowInterstitialAdMethod',
        {"advertId": insertVideoData!.adId});

    /// 接收一个数组或者字典作为参数传递给原生端
    return "插屏广告加载成功";
  }

  /// 信息流获取
  Future<String> getInformationMethod(ListData? informationData) async {
    if (!isInit) {
      await initSdk();
      advertisementType = 6;
      return '重新初始化sdk';
    }
    if (isTimingBan) {
      return "广告禁止播放";
    }
    if ((informationData?.adId ?? "").isEmpty) {
      // Toast.show("广告加载失败");
      return "广告开屏id为空";
    }
    final result = await _channel
        .invokeMethod('cjLoadAndShowBannerMethod', {"advertId": informationData!.adId});

    /// 接收一个数组或者字典作为参数传递给原生端
    return "banner广告加载成功";
  }

  /// 信息流刷新
  Future<String> refreshInformationMethod(ListData? informationData) async {
    if (!isInit) {
      await initSdk();
      advertisementType = 6;
      return '重新初始化sdk';
    }
    if (isTimingBan) {
      return "广告禁止播放";
    }
    if ((informationData?.adId ?? "").isEmpty) {
      // Toast.show("广告加载失败");
      return "广告开屏id为空";
    }
    final result = await _channel
        .invokeMethod('cjRefreshAndShowBannerMethod');

    /// 接收一个数组或者字典作为参数传递给原生端
    return "banner广告加载成功";
  }
}

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/bottom_tabbar_controller.dart';
import 'package:dxjd/mainController.dart';
import 'package:dxjd/tab_container_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:home/src/home/<USER>/get_the_certificate_controller.dart';
import 'package:home/src/home/<USER>/section_four_controller.dart';
import 'package:home/src/home/<USER>/section_one_controller.dart';
import 'package:home_repository/home_repository.dart';
import 'package:mine/mine.dart';
import 'package:pausable_timer/pausable_timer.dart';
import 'package:push_repository/push_repository.dart';
import 'package:quiz/quiz.dart';
import 'package:timing_repository/timing_repository.dart';
import 'package:tools/tools.dart';
import 'package:user_repository/user_repository.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:login/login.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class ExaminationController extends GetxController
    with GetTickerProviderStateMixin {
  TabController? tabController;
  final List tabs = ["科一", "科二", "科三", "科四", "拿证"];
  // final List tabs2 = ["刷题刷学时", "真实考场", "考场实操", "刷题刷学时", "新手上路"];
  // bool isToken=false;

  // banner刷新时间
  static int bannerRefreshTime = 6;
  MethodChannel channel = const MethodChannel('xw.dxjk.share');
  int currentIndex = 0;
  Map selectCityMap = {};
  String selectCity = '广州市';
  StreamSubscription<UserAccount?>? userSubject;
//  题库更新展示动画
  bool isShowQuestionUpdate = false;
  // int next = -1;
  // //-1 停止  0开始  1拖动进行中
  // int isStartScroll = -1;
  double xPosition = 10;
  double yPosition = 0;
  UserAccount? userAccount;
  List<BannerDataDm>? bannerList;
  bool isGetLastTabIndex = false;
  late HomeRepository homeRepository;
  late UserRepository userRepository;
  late TimingRepository timingRepository;
  late PushRepository pushRepository;
  late MainController _mainController;
  bool isChangeCity = false;
  static bool initBandLocation = true;
  Timer? fuchuangdialog;
  static String? currentMarketUrl;
  var remark = "";
  bool isFirstEnterApp = false;
//  是否在绑定驾校页面
  bool isEnterHomeStudySelect = false;
  Map couponMap = {};
  @override
  Map dialogMap = {};
  Map floatMap = {};
  //获取已结业用户是否切换车型题库
  String? isChangeCarType;
  //获取已结业用户是否切换城市题库
  String? isChangeCityType;

//  信息流广告刷新控制器
//   PausableTimer? informationRefreshTimer;

// //  广告刷新暂停
//   void refreshAdTimerPause(){
//     informationRefreshTimer?.pause();
//   }
//
// //  广告刷新开启
//   void refreshAdTimerStart(){
//     informationRefreshTimer?.start();
//   }

  ///获取弹窗配置信息
  getDialogConfiguration(List<int> type, int typ) async {
    if (typ == 2) {
      try {
        dialogMap = await homeRepository.getDialogConfig(type: type);
      } catch (e) {}
      if (dialogMap.isNotEmpty) {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          showDialog(
            context: Get.context!,
            barrierDismissible: false, // 点击空白区域不可关闭
            builder: (BuildContext context) {
              return HomeDialog(
                imageUrl: dialogMap['Url'],
                timingRepository: timingRepository,
                dialogMap: dialogMap,
              );
            },
          );
        });
      }
    } else {
      try {
        floatMap = await homeRepository.getDialogConfig(type: type);
        update();
      } catch (e) {
        debugPrint("$e");
      }
    }
  }

  ///展示新人优惠弹窗
  showNewUserDialog(int surplusTime) {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      showDialog(
        context: Get.context!,
        barrierDismissible: false, // 点击空白区域不可关闭
        builder: (BuildContext context) {
          return NewUserDialog(
            surplusTime: surplusTime,
            userRepository: userRepository,
          );
        },
      );
    });
  }

  ///改变车型题库类型
  changeTopicType() async {
    isChangeCarType = await PreferencesService().getString('useTopicType');
    update();
  }
  ///改变城市题库类型
  changeCityType() async {
    final temp = await PreferencesService().getInt('useDivision');
    if(temp==null||temp==0){
      isChangeCityType=null;
      return;
    }else {
      isChangeCityType = cityMap[temp.toString().substring(0, 4)];
    }
    update();
  }
  ///展示弹窗
  showDialogInfo() async {
    getDialogConfiguration([1], 1);
    _receiveCoupon();
    VideoShortManager.get().init();
  }

  ///获取设备信息
  Future<String> getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    print('设备制造厂商: ${androidInfo.manufacturer}');
    return androidInfo.manufacturer;
  }

  ///获取版本信息
  Future<String> getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;
    print('应用版本号: $version');
    return version;
  }

  ///查询服务器版本号
  checkAppVersion() async {
    String channel = "";
    String appVersion = "";
    if (Platform.isAndroid) {
      channel = (await getDeviceInfo()).toUpperCase();
    } else {
      channel = "APPLE";
    }
    appVersion = await getAppVersion();
    try {
      Map map = await homeRepository.checkAppVersion(
          channel: channel, appVersion: appVersion);
      print('服务器最新版本号---------------${map['AppVersion']}');
      print('${int.parse(appVersion.replaceAll(".", ""))}');
      currentMarketUrl = map['AppUrl'];
      if (int.parse(map['AppVersion'].replaceAll(".", "")) >
          int.parse(appVersion.replaceAll(".", ""))) {
        showUpdateDialog(
            map['AppVersion'], map['Remark'], map["Type"] == 1, map['AppUrl']);
      } else {
        appVersionCheckAfterInit();
      }
    } catch (e) {
      currentMarketUrl = _getMartUrl(channel);
      print("error:$e");
      appVersionCheckAfterInit();
    }
  }

//  加载首页原生信息流
  loadInformationAd() {
    // 如果全是vip则不需要定时器去刷新广告
    if (userRepository.vipProductMap.vip1 &&
        userRepository.vipProductMap.vip2 &&
        userRepository.vipProductMap.vip3 &&
        userRepository.vipProductMap.vip4) {
      return;
    }
    BeiziAdPlugin.instance.initInformationOrRefresh();
    // setInformationRefreshTimer();
  }

  //获取banner轮播时间以及信息流广告刷新时间
  getBannerAndInformationAdRefreshTime() async {
    try {
      Map map = await homeRepository.getBannerAndInformationAdRefreshTime();
      BeiziAdPlugin.instance.informationRefreshTime =
          map['data']['AppBannerTimeInfo']['AdvertisementRefreshTime'];
      bannerRefreshTime = map['data']['AppBannerTimeInfo']['BannerRefreshTime'];
    } catch (e) {}
  }

  // setInformationRefreshTimer() {
  //   informationRefreshTimer = PausableTimer.periodic(
  //       Duration(seconds: BeiziAdPlugin.instance.informationRefreshTime), () {
  //     BeiziAdPlugin.instance.initInformationOrRefresh();
  //   })..start();
  // }

  //获取市场跳转链接
  String _getMartUrl(String channel) {
    switch (channel) {
      case "XIAOMI":
        return "mimarket://details?id=com.dxjk.xw";
      case "HUAWEI":
        return "market://details?id=com.dxjk.xw";
      case "OPPO":
        return "market://details?id=com.dxjk.xw";
      case "VIVO":
        return "market://details?id=com.dxjk.xw";
      case "HONOR":
        return "honormarket://details?id=com.dxjk.xw&mediaChannel=1&secenType=0501";
      case "APPLE":
        return "itms-apps://itunes.apple.com/app/6502805772";
      default:
        return "";
    }
  }

  ///版本更新弹窗
  showUpdateDialog(
      String version, String remake, bool isShowPop, String path) async {
    showDialog(
        context: Get.context!,
        barrierDismissible: false, // 点击空白区域不可关闭
        builder: (BuildContext context) {
          return PopScope(
            canPop: isShowPop,
            child: Dialog(
              insetPadding: REdgeInsets.symmetric(horizontal: 49, vertical: 30),
              surfaceTintColor: Colors.transparent,
              backgroundColor: Colors.transparent, // 背景透明
              child: SizedBox(
                width: double.infinity,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    assImg2(
                        img: 'tanchuang_app_update',
                        w: double.infinity,
                        fit: BoxFit.fitWidth),
                    Container(
                      width: double.infinity,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(12.r),
                            bottomRight: Radius.circular(12.r)),
                      ),
                      child: Column(
                        children: [
                          ShaderMask(
                            shaderCallback: (Rect bounds) {
                              return const LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [Color(0xFF1789FF), Color(0xFF03B0FE)],
                              ).createShader(Offset.zero & bounds.size);
                            },
                            blendMode: BlendMode.srcATop,
                            child: Text(
                              "V$version",
                              style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'PingFangSC-Bold'),
                            ),
                          ),
                          Html(
                            data: remake,
                            shrinkWrap: true,
                          ),
                          SizedBox(height: 15.h),
                          InkWell(
                            onTap: () {
                              if (Platform.isAndroid) {
                                launchUrl(Uri.parse(path));
                              } else {
                                launchUrl(Uri.parse(path));
                              }
                            },
                            child: Container(
                              margin: REdgeInsets.symmetric(horizontal: 26),
                              child: assImg2(
                                  img: 'app_tanchuang_buttn',
                                  fit: BoxFit.fitWidth),
                            ),
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          InkWell(
                            onTap: () {
                              Navigator.of(context).pop();
                              appVersionCheckAfterInit();
                            },
                            child: isShowPop
                                ? Text(
                                    "暂不更新",
                                    style: TextStyle(
                                        fontSize: 13.sp,
                                        color: const Color(0xFFC7C7C7),
                                        fontFamily: 'PingFangSC-Regular'),
                                  )
                                : const SizedBox(),
                          ),
                          SizedBox(
                            height: 11.h,
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          );
        });
  }

  ///版本检查之后操作
  appVersionCheckAfterInit() async {
    // 注册激光推送
    MainController mainController = Get.find<MainController>();
    MainController.isExistToken = await mainController.getUsToken();
    await getBannerAndInformationAdRefreshTime();
    loadInformationAd();
    if (MainController.isFirstOpen) {
      channel.invokeMethod('initUm');
      mainController.initPush();
      // 初始化传感器
      IPlatform.get().initSensor();
      buryingPointInit();
    }
    initQuestionUpdate();
    MainController.isFirstOpen = false;
  }

  //题库更新
  initQuestionUpdate() {
    // 初始化题库
    IHome.get().init();
  }

  isShowQuestionUpdateCallBack(bool isShow) {
    isShowQuestionUpdate = isShow;
    update();
  }

  ///隐私弹窗
  showPriceDialog() async {
    showDialog(
        context: Get.context!,
        barrierDismissible: false, // 点击空白区域不可关闭
        builder: (BuildContext context) {
          return PopScope(
            canPop: false,
            onPopInvoked: (bool didPop) async {
              if (didPop) {
                return; // really exit
              } else {
                if (!isFirstEnterApp) {
                  Navigator.of(context).pop();
                }
              }
            },
            child: Dialog(
                insetPadding: REdgeInsets.all(0),
                surfaceTintColor: Colors.transparent,
                backgroundColor: Colors.transparent, // 背景透明
                child: Container(
                    margin: REdgeInsets.symmetric(horizontal: 26.w),
                    padding:
                        REdgeInsets.symmetric(vertical: 30.h, horizontal: 24.w),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(Radius.circular(12.r))),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '个人信息保护指引',
                          style: TextStyle(
                              color: const Color(0xff1a1a1a),
                              fontSize: 18.sp,
                              fontFamily: 'PingFangSC-Medium'),
                        ),
                        SizedBox(height: 24.h),
                        Text(
                          '大象驾到非常注重您的隐私保护和个人信息保护，在您使用大象驾到前，请认真阅读以下条款: ',
                          style: TextStyle(
                              fontSize: 14.0.sp,
                              color: const Color(0xFF4E4E4E),
                              fontFamily: 'PingFangSC-Regular'),
                        ),
                        RichText(
                          text: TextSpan(
                            style: TextStyle(
                                fontSize: 14.0.sp,
                                color: const Color(0xFF4E4E4E)),
                            children: <TextSpan>[
                              TextSpan(
                                text: '《隐私政策》',
                                style: const TextStyle(color: Colors.blue),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    // 处理用户协议点击事件
                                    Get.toNamed(AppRoutes.webView, arguments: {
                                      'url':
                                          'https://dxjk.daxiangjd.com/agreement/?tag=privacy_agreement',
                                      'title': '隐私政策'
                                    });
                                  },
                              ),
                              const TextSpan(text: ' 和 '),
                              TextSpan(
                                text: '《用户协议》',
                                style: const TextStyle(color: Colors.blue),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    // 处理隐私政策点击事件
                                    Get.toNamed(AppRoutes.webView, arguments: {
                                      'url':
                                          'https://dxjk.daxiangjd.com/agreement/?tag=user_agreement',
                                      'title': '用户协议'
                                    });
                                  },
                              ),
                              const TextSpan(
                                  text: '如同意以上条款，请点击“同意并继续”以便我们能开始为您服务'),
                            ],
                          ),
                        ),
                        SizedBox(height: 20.h),
                        GestureDetector(
                          onTap: () async {
                            Get.back();
                            await checkAppVersion();
                            PreferencesService().setBool('fist_run', false);
                            MainController.isFirstEnterApp = false;
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: const Color(0xFF268FF7),
                              borderRadius: BorderRadius.circular(20.0.r),
                            ),
                            padding: const EdgeInsets.all(12.0),
                            child: Center(
                              child: Text(
                                '同意并继续',
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16.sp,
                                    fontFamily: 'PingFangSC-Medium'),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 20.h),
                        GestureDetector(
                          onTap: () {
                            // 按钮点击时的操作
                            exit(0);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20.r),
                            ),
                            padding: const EdgeInsets.all(12.0),
                            child: Center(
                              child: Text(
                                '不同意并退出',
                                style: TextStyle(
                                    fontSize: 14.sp,
                                    color: const Color(0xFFAAAAAA),
                                    fontFamily: 'PingFangSC-Medium'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ))),
          );
        });
  }

  _changeCity(var value) async {
    if (value != null) {
      selectCityMap = value;
      // debugPrint("-------value:$value");
      if (selectCityMap['division'].isNotEmpty) {
        isChangeCity = true;
        selectCity = selectCityMap['name'];
        await _updateStudentTrainTypeAndCity(
            userAccount!.topicType!, int.parse(selectCityMap['division']));
        isShowQuestionUpdate = true;
        update();
        Future.delayed(const Duration(seconds: 1), () {
          isShowQuestionUpdate = false;
          update();
        });
        // userRepository.setCityAndDivision(_selectCity,_selectCityMap['division']);
      }
    }
    update();
  }

  Widget _confirmQuestionBankChangeDialog(
      BuildContext currentContext, var value) {
    return Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 28),
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.0),
              color: Colors.white,
            ),
            padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      '您当前的题库城市是',
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'PingFangSC-Medium',
                          color: AppColors.privacy_dialog_titile_color),
                    ),
                    const SizedBox(width: 6),
                    Stack(
                      children: [
                        Positioned(
                          bottom: 0,
                          child: Container(
                            height: 4.h,
                            width: 65.w,
                            color: const Color(0xFFFFCB63),
                          ),
                        ),
                        Text(
                          selectCity,
                          style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'PingFangSC-Semibold',
                              color: AppColors.other_login_text),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 23),
                const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '城市不同，考试题库可能存在差异',
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'PingFangSC-Regular',
                          color: AppColors.privacy_dialog_titile_color,
                        ),
                      ),
                      SizedBox(width: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '是否，',
                            style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'PingFangSC-Regular',
                              color: AppColors.privacy_dialog_titile_color,
                            ),
                          ),
                          Text(
                            '继续刷题？',
                            style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'PingFangSC-Semibold',
                              color: AppColors.privacy_dialog_titile_color,
                            ),
                          ),
                        ],
                      )
                    ]),
                const SizedBox(height: 20),
                Row(
                  children: [
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: Colors.white,
                          border: Border.all(
                            color: AppColors.AAAAAA,
                            width: 1,
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 9),
                        child: const Text(
                          '继续刷题',
                          style: TextStyle(
                              color: AppColors.privacy_dialog_titile_color,
                              fontSize: 16,
                              fontFamily: 'PingFangSC-Medium'),
                        ),
                      ),
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: () {
                        Get.back();
                        if (userAccount?.reqisterDivision != null) {
                          if (selectCityMap['division'].isNotEmpty) {
                            isChangeCity = true;
                            selectCity = cityMap[userAccount!.reqisterDivision
                                .toString()
                                .substring(0, 4)];
                            _updateStudentTrainTypeAndCity(
                                userAccount!.topicType!,
                                userAccount!.reqisterDivision!);
                          }
                          Get.offAndToNamed(AppRoutes.home);
                          update();
                        }
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: Colors.white,
                          border: Border.all(
                            color: AppColors.AAAAAA,
                            width: 1,
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 9),
                        child: const Text(
                          '切换到我的城市',
                          style: TextStyle(
                              color: AppColors.privacy_dialog_titile_color,
                              fontSize: 16,
                              fontFamily: 'PingFangSC-Medium'),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            )));
  }

  //获取当前时间戳
  int getNowTime() {
    // 获取当前时间
    DateTime now = DateTime.now();

    // 将时间精确到分钟（去掉秒和毫秒）
    DateTime timeToMinute =
        DateTime(now.year, now.month, now.day, now.hour, now.minute);

    // 将 DateTime 转换为时间戳（以秒为单位）
    int timestamp = timeToMinute.millisecondsSinceEpoch ~/ 1000;

    return timestamp;
  }

  /// 判断传入的时间戳与当前时间是否相差超过多少小时
  bool isTimeDifferenceOverHowHours(int timestamp, int endTime) {
    // 获取当前时间并精确到分钟
    DateTime now = DateTime.now();
    DateTime nowToMinute =
        DateTime(now.year, now.month, now.day, now.hour, now.minute);

    // 将传入的时间戳转换为 DateTime 对象并精确到分钟
    DateTime timeToMinute =
        DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    timeToMinute = DateTime(timeToMinute.year, timeToMinute.month,
        timeToMinute.day, timeToMinute.hour, timeToMinute.minute);

    // 计算时间差
    Duration difference = nowToMinute.difference(timeToMinute);

    // 判断是否超过24小时
    return difference.inHours.abs() > endTime;
  }

  @override
  void onInit() async {
    // 获取仓库
    _mainController = Get.find<MainController>();
    homeRepository = _mainController.homeRepository;
    userRepository = _mainController.userRepository;
    timingRepository = _mainController.timingRepository;
    pushRepository = _mainController.pushRepository;
    print(' ExaminationPage ExaminationController create');
    // 获取上一次index
    int? index = await PreferencesService().getInt('lastTabIndex');
    tabController = TabController(
        length: tabs.length, vsync: this, initialIndex: index ?? 0);
    currentIndex = index ?? 0;
    yPosition = MediaQuery.of(Get.context!).size.height * 0.7;
    // 获取主页banner
    // _getHomeBanner();
    // 是否获取上一次tab
    isGetLastTabIndex = true;
    isChangeCarType = await PreferencesService().getString('useTopicType');
    int? division = await PreferencesService().getInt('useDivision');
    try {
      isChangeCityType = cityMap[division.toString().substring(0, 4)];
    }catch(e){
      isChangeCityType = null;
    }
    update();
    super.onInit();
  }

  @override
  void onReady() async {
    // 是否是第一次启动app
    isFirstEnterApp = await PreferencesService().getBool('fist_run') ?? true;
    if (isFirstEnterApp) {
      showPriceDialog();
    } else {
      // 是否是第一次进入打开
      if (MainController.isFirstOpen) {
        await checkAppVersion();
      } else {
        // 刷新题库
        if (MainController.isExistToken) {
          String channel = "";
          if (Platform.isAndroid) {
            channel = (await getDeviceInfo()).toUpperCase();
          } else {
            channel = "APPLE";
          }
          appVersionCheckAfterInit();
        }
      }
    }
    if (MainController.isExistToken) {
      try {
        homeRepository.getUnReadMessage();
      } catch (e) {
        debugPrint("----getUnReadMessage---error:$e");
      }
      try {
        // 重新获取vip信息?
        await userRepository.setVipProduct();
        update();
      } catch (e) {
        debugPrint("-------error1:$e");
      }
    }
    // 判断是否获取到广告配置
    if (BeiziAdPlugin.instance.advertisementList.list == null) {
      homeRepository
          .getAdvertisingConfig(await userRepository.getUserToken() ?? "");
    }
    // 设置用户流(退出登录触发)
    userSubject ??=
        userRepository.getUserAccount().listen((userAccountNew) async {
      if (userAccountNew?.reqisterDivision != userAccount?.reqisterDivision) {
        if (userAccountNew?.reqisterDivision != 0 && userAccountNew != null) {
          // 设置选择的城市为注册的城市
          //判断是否在改变题库
          if (!isChangeCity) {
            selectCity = cityMap[
                userAccountNew.reqisterDivision.toString().substring(0, 4)];
          }
          isChangeCity = false;
        }
      }
      userAccount = userAccountNew;
      debugPrint("-------userAccountNew:$userAccountNew");
      update();
    });
    if (!isEnterHomeStudySelect && MainController.isExistToken) {
      showDialogInfo();
    }
    super.onReady();
  }

  @override
  void onClose() {
    fuchuangdialog?.cancel();
    tabController?.dispose();
    userSubject?.cancel();
    // informationRefreshTimer?.cancel();
    print('ExaminationPage ExaminationController onclose"');
    super.onClose();
  }

  // 切换tabbar触发回调
  changeTabBarIndexCallBack() {
    initBandLocation = false;
    stopCurrentBannerScroll();
    startAndStopBannerScroll(tabController!.index + 1,false,isReset: true);
    if (currentIndex == 4 && tabController!.index != 4) {
      if (Get.isRegistered<GetTheCertificateController>()) {
        GetTheCertificateController controller =
            Get.find<GetTheCertificateController>();
        controller.controller.pause();
      }
    }
    if (currentIndex != 4 && tabController!.index == 4) {
      if (Get.isRegistered<GetTheCertificateController>()) {
        GetTheCertificateController controller =
            Get.find<GetTheCertificateController>();
        controller.controller.play();
      }
    }
    BeiziAdPlugin.instance.setIsInformationClose = false;
    currentIndex = tabController!.index;
    homeRepository.setSubjectIndex(currentIndex);
    // 存储上一次tabIndex索引
    PreferencesService().setInt('lastTabIndex', currentIndex);
    update();
    if (currentIndex != 4) {
      if (Get.isRegistered<MineController>()) {
        Get.find<MineController>().pageController.jumpToPage(currentIndex + 1);
      }
    }
  }

  void goChangeQuestionBankPage() {
    Get.toNamed(AppRoutes.cityListSelectPage)?.then((value) async {
      _changeCity(value);
    });
  }

  isFirstDoTopic() async {
    if (selectCityMap.isEmpty) {
      return;
    }
    if (selectCityMap['divisin'] == userAccount?.reqisterDivision) {
      return;
    }
    int? time = await PreferencesService().getInt('fist_change_city_time');
    int nowTime = getNowTime();
    if (time == null || isTimeDifferenceOverHowHours(nowTime, 24)) {
      showDialog(
        context: Get.context!,
        builder: (BuildContext context) =>
            _confirmQuestionBankChangeDialog(context, selectCityMap),
      );
      await PreferencesService().setInt('fist_change_city_time', getNowTime());
    }
  }

  //初始化埋点单例
  buryingPointInit() async {
    BuryingPointUtils.instance
        .init(hRepository: homeRepository, uRepository: userRepository);
    //上传埋点
    BuryingPointUtils.instance.updatePointDetect();
  }

  //刷新城市和车型题库
  _updateStudentTrainTypeAndCity(String topicType, int division) async {
    try {
      if (userAccount?.isBind == 1) {
        ITools.get().reqisterDivision = division;
        // 切换题库
        String code = division.toString().substring(0, 4);
        List<String> cityid = <String>[code.substring(0, 2), code.toString()];
        await ITrain.get().quesRefresh(true, cityIds: cityid);
        update();
      } else {
        // 绑定驾校
        userRepository.updateStudentTrainType(topicType, division);
      }
    } catch (e) {
      if (e is DomainException) {
        Toast.show(e.message);
      } else {
        Toast.show(e.toString());
      }
    }
  }

  //获取优惠卷
  _receiveCoupon() async {
    try {
      UserAccount? userAccountInfo =
          await Get.find<MainController>().userRepository.userAccountDM;
      if (userAccountInfo != null) {
        couponMap = await homeRepository.receiveCoupon(userAccountInfo!.sid!);
        showDialog(
          context: Get.context!,
          barrierDismissible: false, // 点击空白区域不可关闭
          builder: (BuildContext context) {
            return CouponDialog(
              couponMap: couponMap,
            );
          },
        );
        if (Get.isRegistered<BottomTabBarController>(
          tag: TabContainerScreenState.key.toString(),
        )) {
          Get.find<BottomTabBarController>(
                  tag: TabContainerScreenState.key.toString())
              .changeMineRedPoint();
        }
      }
    } catch (e) {
      // showDialog(
      //   context: Get.context!,
      //   barrierDismissible: false, // 点击空白区域不可关闭
      //   builder: (BuildContext context) {
      //     return CouponDialog(
      //       couponMap: {
      //         "CouponId": "1231",
      //         "CouponName": "VIP",
      //         "Discount": 20,
      //         "GoodIds": ["string"],
      //         "ValidTime": 1
      //       },
      //     );
      //   },
      // );
      String? time = await PreferencesService().getString('showDialogTime');
      String? uid = (await userRepository.userAccountDM)?.uid;
      if (time == null) {
        fiveSecondShowDialog();
        PreferencesService()
            .setString('showDialogTime', jsonEncode({uid: getNowTime()}));
      } else {
        Map map = jsonDecode(time);
        if (map[uid] == null) return;
        if (isTimeDifferenceOverHowHours(map[uid], 6)) {
          fiveSecondShowDialog();
          map[uid] = getNowTime();
          PreferencesService().setString('showDialogTime', jsonEncode(map));
        }
      }
    }
  }

//  延迟五秒的弹窗
  void fiveSecondShowDialog() {
    fuchuangdialog = Timer(const Duration(seconds: 5), () {
      PreferencesService().getBool('newUserPrice').then((value) {
        if (value == null) {
          final nowTime = DateTime.now().millisecondsSinceEpoch;
          final surplusTime = nowTime - (userAccount?.registerDate ?? 0);
          if (userAccount?.registerDate != null) {
            if (surplusTime > 1000 * 60 * 60 * 24 * 3) {
              getDialogConfiguration([2], 2);
            } else {
              showNewUserDialog(
                  ((1000 * 60 * 60 * 24 * 3) - surplusTime) ~/ 1000);
              PreferencesService().setBool('newUserPrice', false);
            }
          }
        } else {
          getDialogConfiguration([2], 2);
        }
      });
    });
  }


//  停止当前banner滚动
  stopCurrentBannerScroll(){
    startAndStopBannerScroll(currentIndex + 1,true);
  }

  //  开始当前banner滚动
  startCurrentBannerScroll(){
    startAndStopBannerScroll(currentIndex + 1,false);
  }


  //banner开始或者滚动滚动
  startAndStopBannerScroll(int subject, bool isStop, {bool isReset = false}) {
     if (subject == 1) {
      stopAndStartSub1BannerScroll(isStop, isReset: isReset);
    } else if (subject == 2) {
      stopAndStartSub2BannerScroll(isStop, isReset: isReset);
    } else if (subject == 3) {
      stopAndStartSub3BannerScroll(isStop, isReset: isReset);
    } else {
      stopAndStartSub4BannerScroll(isStop, isReset: isReset);
    }
  }

  stopAndStartSub1BannerScroll(bool isStop, {bool isReset = false}) {
    print('科目1 ${isStop?"stop":"start"}');
    if (Get.isRegistered<SectionOneController>(
        tag: ExaminationPageState.key.toString())) {
      SectionOneController controller = Get.find<SectionOneController>(
          tag: ExaminationPageState.key.toString());
      if (isStop) {
        controller.bannerIsStartScroll = false;
      } else {
        // banner是否存在视图
        if (controller.bannerIsExitView) {
          controller.bannerIsStartScroll = true;
          // 是否需要重制到广告下标
          if (isReset) {
            if ((controller.bannerList ?? []).isNotEmpty) {
              int index = controller.bannerList!
                  .indexWhere((element) => (element.apcId ?? "").isNotEmpty);
              if(index != -1){
                controller.carouselController.jumpToPage(index);
              }
            }
          }
        }
      }
    }
  }

  stopAndStartSub2BannerScroll(bool isStop, {bool isReset = false}) {
    print('科目2 ${isStop?"stop":"start"}');
    if (Get.isRegistered<SectionTwoController>(
        tag: ExaminationPageState.key.toString())) {
      SectionTwoController controller = Get.find<SectionTwoController>(
          tag: ExaminationPageState.key.toString());
      if (isStop) {
        controller.bannerIsStartScroll = false;
      } else {
        if (controller.bannerIsExitView) {
          controller.bannerIsStartScroll = true;
          if (isReset) {
            if ((controller.bannerList ?? []).isNotEmpty) {
              int index = controller.bannerList!
                  .indexWhere((element) => (element.apcId ?? "").isNotEmpty);
              if(index != -1){
                controller.carouselController.jumpToPage(index);
              }
            }
          }
        }
      }
    }
  }

  stopAndStartSub3BannerScroll(bool isStop, {bool isReset = false}) {
    print('科目3 ${isStop?"stop":"start"}');
    if (Get.isRegistered<SectionThreeController>(
        tag: ExaminationPageState.key.toString())) {
      SectionThreeController controller = Get.find<SectionThreeController>(
          tag: ExaminationPageState.key.toString());
      if (isStop) {
        controller.bannerIsStartScroll = false;
      } else {
        if (controller.bannerIsExitView) {
          controller.bannerIsStartScroll = true;
          if (isReset) {
            if ((controller.bannerList ?? []).isNotEmpty) {
              int index = controller.bannerList!
                  .indexWhere((element) => (element.apcId ?? "").isNotEmpty);
              if(index != -1){
                controller.carouselController.jumpToPage(index);
              }
            }
          }
        }
      }
    }
  }

  stopAndStartSub4BannerScroll(bool isStop, {bool isReset = false}) {
    print('科目4 ${isStop?"stop":"start"}');
    if (Get.isRegistered<SectionFourController>(
        tag: ExaminationPageState.key.toString())) {
      SectionFourController controller = Get.find<SectionFourController>(
          tag: ExaminationPageState.key.toString());
      if (isStop) {
        controller.bannerIsStartScroll = false;
      } else {
        if (controller.bannerIsExitView) {
          controller.bannerIsStartScroll = true;
          if (isReset) {
            if ((controller.bannerList ?? []).isNotEmpty) {
              int index = controller.bannerList!
                  .indexWhere((element) => (element.apcId ?? "").isNotEmpty);
              if(index != -1){
                controller.carouselController.jumpToPage(index);
              }
            }
          }
        }
      }
    }
  }
}

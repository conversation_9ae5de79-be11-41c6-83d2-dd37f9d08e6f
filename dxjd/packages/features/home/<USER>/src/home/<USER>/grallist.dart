import 'dart:convert';
import 'dart:io';

import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:flutter/material.dart';
import 'package:home/src/home/<USER>';
import 'package:mop/mop.dart';

import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:login/login.dart';
import 'package:timing_repository/timing_repository.dart';

import '../../../home.dart';

class GalleryList extends StatefulWidget {
  GalleryList(
      {super.key,
      this.bannerListDm,
      this.timingRepository,
      this.queryParameters,
      this.goWebView,
      required this.carouselController,
      this.isTabHome = false,
      required this.subject,required this.autoPlay});
  List<BannerDataDm>? bannerListDm;
  TimingRepository? timingRepository;
  CarouselController carouselController;
  bool autoPlay;
  Map<String, dynamic>? queryParameters;
  Function(String url, String title)? goWebView;
  bool isTabHome;
  int subject;
  @override
  State<GalleryList> createState() => _GalleryListState();
}

class _GalleryListState extends State<GalleryList> {
  int currentIndex = 0;
  double carouselWidth = 200 - 24;
  double carouselHeight = 100.h;
  late CarouselController carouselController;
  var bannerList = [
    // "https://img.win3000.com/m00/4f/9d/a9ccfe250e6455b47e8b864198b51eff.jpg",
    // "https://i.hd-r.cn/8cead68b004cb8563716e8834d76b4d9.png",
    "https://dx-stg.oss-cn-shenzhen.aliyuncs.com/app/resources/dxjd-home-banner-default.png",
    "https://dx-stg.oss-cn-shenzhen.aliyuncs.com/app/resources/dxjd-home-banner-default.png",
    // "https://t7.baidu.com/it/u=1956604245,3662848045&fm=193&f=GIF"
  ];

  @override
  void initState() {
    carouselController  = widget.carouselController;
    // TODO: implement initState
    super.initState();
  }
  @override
  Widget build(context) {
    return Container(
        height: carouselHeight,
        // padding: const EdgeInsets.only(top: 4),
        decoration: const BoxDecoration(
            // color: Colors.white,
            // borderRadius: BorderRadius.only(
            //     topLeft: Radius.circular(8), topRight: Radius.circular(8)),
            ),
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            FlutterCarousel(
              options: CarouselOptions(
                height: carouselHeight,
                controller: widget.carouselController,
                onPageChanged: (index, reason) {
                  print("banner${widget.subject}改变中");
                  setState(() {
                    currentIndex = index;
                  });
                },
                autoPlay: widget.autoPlay,
                autoPlayInterval:
                    Duration(seconds: ExaminationController.bannerRefreshTime),
                disableCenter: true,
                viewportFraction: 1.0,
                enableInfiniteScroll: true,
                showIndicator: false,
              ),
              items: widget.bannerListDm == null
                  ? bannerList.map(
                      (item) {
                        return GestureDetector(
                          onTap: () => debugPrint("跳转路由"),
                          child: Container(
                            margin: const EdgeInsets.fromLTRB(11, 0, 11, 0),
                            child: ClipRRect(
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(6)),
                                child: networkImg(
                                  fit: BoxFit.fill,
                                  img: item,
                                )),
                          ),
                        );
                      },
                    ).toList()
                  : (widget.bannerListDm ?? [])
                      .map((e) => GestureDetector(
                            onTap: () {
                              if (widget.isTabHome) {
                                BuryingPointUtils.instance.addPoint(
                                    source:
                                        Platform.isAndroid ? "android" : "ios",
                                    buryingPointList: BuryingPointList(
                                      eventType: 4,
                                      entranceType: 1,
                                      action: 1,
                                      browseDuration: 0,
                                      businessId: e.id,
                                    ));
                                if (e.redirectType == 2) {
                                  Map map = jsonDecode(e.redirectApp!);
                                  // Mop.instance
                                  //     .openApplet(, path: );
                                  JumpSmallProgramUtils.jump(
                                      e.redirectPath!, map['AppId']);
                                } else if (e.redirectType == 3) {
                                  showDialog(
                                    context: Get.context!,
                                    barrierDismissible: false, // 点击空白区域不可关闭
                                    builder: (BuildContext context) {
                                      return Dialog(
                                        child: U3dConfirmDialog(
                                          content: '即将打开微信',
                                          cancelText: '取消',
                                          confirmText: '确认',
                                          onCancel: () {
                                            Get.back();
                                          },
                                          onConfirm: () {
                                            Get.back();
                                            var map =
                                                jsonDecode(e.redirectApp!);
                                            widget.timingRepository!
                                                .openFluwxMiniProgram(
                                              e.redirectPath!,
                                              map['AppId'],
                                            );
                                          },
                                        ),
                                      );
                                    },
                                  );
                                } else if (e.redirectType == 5) {
                                  Get.toNamed(AppRoutes.webView, arguments: {
                                    'url': e.redirectPath,
                                    'title': e.title
                                  });
                                }
                              }
                            },
                            child: Container(
                              margin: const EdgeInsets.fromLTRB(11, 0, 11, 0),
                              child: ClipRRect(
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(10)),
                                  child: (e.apcId ?? "").isNotEmpty
                                      ? InformationBannerWidget(
                                          subject: widget.subject)
                                      : networkImg(
                                          fit: BoxFit.fill,
                                          img: e.url,
                                        )),
                            ),
                          ))
                      .toList(),
            ),
            Positioned(
              bottom: 3,
              child: Row(
                children: List.generate(
                    widget.bannerListDm?.length ?? bannerList.length,
                    (index) => Container(
                          margin: REdgeInsets.only(right: 2),
                          height: 2.h,
                          width: currentIndex == index ? 11.w : 6.w,
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(2)),
                        )),
              ),
            )
          ],
        ));
  }
}

Widget theoryCatalogGalleryList() {
  double carouselHeight = 160;
  var bannerList = [
    "banner_shipin_lilunshipin",
  ];
  return Container(
      height: 120,
      // padding: const EdgeInsets.only(top: 4),
      decoration: const BoxDecoration(
        color: Colors.white,
        // borderRadius: BorderRadius.only(
        //     topLeft: Radius.circular(8), topRight: Radius.circular(8)),
      ),
      child: FlutterCarousel(
          options: CarouselOptions(
              height: carouselHeight,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 8),
              disableCenter: true,
              viewportFraction: 1.0,
              enableInfiniteScroll: true,
              showIndicator: false),
          items: bannerList.map(
            (item) {
              return GestureDetector(
                onTap: () => debugPrint("跳转路由"),
                child: Container(
                  margin: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: ClipRRect(
                      borderRadius: const BorderRadius.all(Radius.circular(6)),
                      child: assImg2(
                        fit: BoxFit.fill,
                        img: item,
                      )),
                ),
              );
            },
          ).toList()));
}

Widget questionListBanner(List<TestDataChild> dataList) {
  return Container(
      // height: 120,
      padding: const EdgeInsets.only(top: 4),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8), topRight: Radius.circular(8)),
      ),
      child: FlutterCarousel(
        options: CarouselOptions(
            height: 120,
            autoPlay: true,
            autoPlayInterval: const Duration(seconds: 8),
            disableCenter: true,
            viewportFraction: 1.0,
            enableInfiniteScroll: true,
            showIndicator: false),
        items: dataList.map(
          (testDataChild) {
            return Container(
              // height: 119.h,
              // width: 290.w,
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: AppColors.F1F8FF,
                borderRadius: BorderRadius.circular(8.r),
              ),
              // margin: EdgeInsets.symmetric(vertical: 12.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        height: 30.h,
                        width: 30.w,
                        decoration: BoxDecoration(
                          color: AppColors.FFCB63,
                          borderRadius: BorderRadius.circular(50.r),
                        ),
                        child: Center(
                            child: app10spWhiteText('头像${testDataChild.img}')),
                      ).paddingOnly(right: 16.w),
                      app14spA1Text(testDataChild.name),
                    ],
                  ).paddingOnly(bottom: 12.h),
                  Expanded(
                      child: app12spC4E4E4EText(testDataChild.content,
                          textAlign: TextAlign.start)),
                ],
              ),
            );
          },
        ).toList(),
      ));
}

Widget questionListBanner2(List<TestDataChild> dataList, {onPageChanged}) {
  CarouselController _controller = CarouselController();
  return Container(
      // height: 120,
      padding: const EdgeInsets.only(top: 4),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8), topRight: Radius.circular(8)),
      ),
      child: FlutterCarousel.builder(
        itemCount: dataList.length,
        itemBuilder: (context, index, realIndex) {
          return GestureDetector(
            onTap: () {
              debugPrint("realIndex点击了第$realIndex个item");
              debugPrint("index点击了第$index个item");
            },
            child: Container(
              // height: 119.h,
              // width: 290.w,
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: AppColors.F1F8FF,
                borderRadius: BorderRadius.circular(8.r),
              ),
              // margin: EdgeInsets.symmetric(vertical: 12.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        height: 30.h,
                        width: 30.w,
                        decoration: BoxDecoration(
                          color: AppColors.FFCB63,
                          borderRadius: BorderRadius.circular(50.r),
                        ),
                        child: Center(
                            child:
                                app10spWhiteText('头像${dataList[index].img}')),
                      ).paddingOnly(right: 16.w),
                      app14spA1Text(dataList[index].name),
                    ],
                  ).paddingOnly(bottom: 12.h),
                  Expanded(
                      child: app12spC4E4E4EText(dataList[index].content,
                          textAlign: TextAlign.start)),
                ],
              ),
            ),
          );
        },
        options: CarouselOptions(
            height: 120,
            autoPlay: true,
            onPageChanged: onPageChanged,
            controller: _controller,
            autoPlayInterval: const Duration(seconds: 8),
            disableCenter: true,
            viewportFraction: 1.0,
            enableInfiniteScroll: true,
            showIndicator: false),
      ));
}

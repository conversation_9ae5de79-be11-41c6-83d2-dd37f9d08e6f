import 'dart:async';
import 'dart:convert';

import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/mainController.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:home_repository/home_repository.dart';
import 'package:timing_repository/timing_repository.dart';
import 'package:tools/tools.dart';
import 'package:user_repository/user_repository.dart';
import 'package:key_value_storage/key_value_storage.dart';
import '../custom_widget/common_view.dart';

StreamSubscription? _ibus;

class SectionThreeController extends GetxController
    with GetTickerProviderStateMixin {
  late final HomeRepository homeRepository;
  late final TimingRepository timingRepository;
  late final UserRepository userRepository;
  int currentIndex = 0;
  int currentPointIndex = 0;
  ScrollController scrollController = ScrollController();
   TabController? tabController;
   TabController? tabPointController;
  TheoryVideoListDm? theoryVideoListDm;
  ExaminationRoomRouteListDm? examinationRoomRouteListDm; //考场选择data
  ExaminationRoomRouteListDm? examinationRoomRoadRouteListDm; //考场选择data
  bool isLoading = true;
  Map subjectThreeTimeTable = {};
  Map<int, List<ExaminationRoomRouteListElementDm>>
      examinationRoomRouteListMap = {};
  Map<int, List<ExaminationRoomRouteListElementDm>>
      examinationRoomRoadRouteListMap = {};
  List<String> cityDistrict = [];
  List<Map<String, dynamic>> cityMap = [];
  List<Map<String, dynamic>> cityRoadMap = [];
  late StreamSubscription<UserAccount?> _userSubject;
  UserAccount? userAccount;
  static bool isInit = false;
  ThreeSubjectsPoint? threeSubjectsPoint;
  late StreamSubscription<CityInfo?> _cityInfoSubscription;
  bool isShowCount = false;
  int countDownMinutes = 0;
  int countDownSeconds = 0;
  CarouselController carouselController = CarouselController();
  //banner是否开启自动滚动
  bool bannerIsStartScroll = true;
  //banner 是否在可见区域
  bool bannerIsExitView = true;
  List<BannerDataDm>? bannerList;

  _requestLocation() async {
    LocationInfo? locationInfo;
    if (!ExaminationController.initBandLocation) {
      locationInfo = await AMapUtils().startLocation(Get.context!);
    }
    if (locationInfo != null) {
      getExaminationRoomsData(
          division: stringToIntConverter(locationInfo.arCode),
          city: locationInfo.city);
    } else {
      getExaminationRoomsData(division: int.parse(homeRepository.cityInfo.cityCode),city: homeRepository.cityInfo.cityName);
      debugPrint("====location==用户拒绝获取位置权限");
    }
  }

  getPointVipOpenConfig() async {
    try {
      userRepository.pointVipMap =
          (await homeRepository.getSubjectThreeExaminationPointVipMap(
        stringToIntConverter(homeRepository.cityInfo.cityCode),
      ));
      update();
    } catch (e) {

    }
  }

  ///获取科三banner
  _getSubjectThreeBanner() async {
    final String token = await userRepository.getUserToken() ?? "";
    if (token.isEmpty) {
      try {
        var bannerList = await homeRepository.getHomeNoTokenBanner();
        this.bannerList = bannerList;
        update();
      } catch (e) {}
    } else {
      List<int> vipSubject = [];
      if (userRepository.getSubVip(3)) {
        vipSubject.add(3);
      }
      try {
        var bannerList = await homeRepository.getHomeBanner(
            district: int.parse(homeRepository.cityInfo.cityCode ?? "440100"),
            subject: [3],
            vipSubject: vipSubject);
        this.bannerList = bannerList;
        update();
      } catch (e) {
        print("error:$e");
      }
    }
  }

  ///获取考场路线数据
  Future<void> getExaminationData(
      {division,
      String? city,
      bool isCallBack = false,
      isSelect = false}) async {
    try {
      final examinationRooms = await homeRepository.getExaminationRoomRoute(
          subject: 3,
          index: 1,
          rows: 200,
          division: division ?? 440100,
          isSelect: isSelect);

      examinationRoomRouteListDm = examinationRooms;
      await _removeDuplicateCity(
        examinationRoomRouteListDm?.listDM,
        city ?? '广州市',
        false,
      );
      tabController = TabController(length: cityMap.length, vsync: this);
      if (isCallBack) {
        if (examinationRoomRouteListDm != null) {
          homeRepository.setExmMenuAndListData(
              cityMap, examinationRoomRouteListMap);
        }
      }
      update();
    } catch (e) {
      debugPrint("=============$e");
      if (e is DomainException) {
        if (e.code == 9 && e.message == "没有相关数据") {
          examinationRoomRouteListDm = null;
          cityMap = [];
          examinationRoomRouteListMap = {};
          update();
          if (isCallBack) {
            homeRepository.setExmMenuAndListData(
                cityMap, examinationRoomRouteListMap);
          }
        }
      } else {
        Toast.show(e.toString());
      }
    }
  }

  ///获取路考数据
  Future<void> getExaminationRoomsRoadData(
      {division,
      String? city,
      bool isCallBack = false,
      isSelect = false}) async {
    try {
      final examinationRoadRooms =
          await homeRepository.getExaminationRoadRoomRoute(
        subject: 3,
        index: 1,
        rows: 200,
        division: division ?? 440100,
      );
      examinationRoomRoadRouteListDm = examinationRoadRooms;
      await _removeDuplicateCity(
        examinationRoomRoadRouteListDm?.listDM,
        city ?? '广州市',
        true,
      );
      if (isCallBack) {
        if (examinationRoadRooms != null) {
          homeRepository.setExmRoadMenuAndListData(
              cityRoadMap, examinationRoomRoadRouteListMap);
        }
      }
    } catch (e) {
      debugPrint("=============$e");
      if (e is DomainException) {
        if (e.code == 9 && e.message == "没有相关数据") {
          examinationRoomRoadRouteListDm = null;
          cityRoadMap = [];
          examinationRoomRoadRouteListMap = {};
          if (isCallBack) {
            await homeRepository.updateCityInfo(division);
            homeRepository.setExmRoadMenuAndListData(
                cityRoadMap, examinationRoomRoadRouteListMap);
          }
          update();
        }
      } else {
        Toast.show(e.toString());
      }
    }
  }

  ///获取点位图信息
  Future<void> getPointMapData(
      {division,
      String? city,
      bool isCallBack = false,
      isSelect = false}) async {
    try {
      threeSubjectsPoint = await homeRepository
          .getSubjectThreeExaminationPoint(division ?? 440100);
      tabPointController = TabController(
          length: threeSubjectsPoint?.list?.length ?? 0, vsync: this);
      if (isCallBack) {
        homeRepository.setThreeSubjectsPoint(threeSubjectsPoint!);
      }
      update();
    } catch (e) {
      debugPrint("=$e");
      if (e is DomainException) {
        if (e.code == 9 && e.message == "没有相关数据") {
          threeSubjectsPoint = ThreeSubjectsPoint();
          if (isCallBack) {
            homeRepository.setThreeSubjectsPoint(threeSubjectsPoint!);
          }
          update();
        }
      } else {
        Toast.show(e.toString());
      }
    }
  }

  ///获取所有数据
  Future<void> getExaminationRoomsData(
      {division,
      String? city,
      bool isCallBack = false,
      isSelect = false}) async {
    if (!MainController.isExistToken) {
      tabController = TabController(length: cityMap.length, vsync: this);
      return;
    }
    await getExaminationData(division: division,city: city,isCallBack: isCallBack,isSelect: isSelect);
    await getExaminationRoomsRoadData(division: division,city: city,isCallBack: isCallBack,isSelect: isSelect);
    await getPointMapData(division: division,city: city,isCallBack: isCallBack,isSelect: isSelect);
    if(isCallBack){
      await homeRepository.updateCityInfo(division);
    }
    await getPointVipOpenConfig();
    await getCountdown();
  }

  Future<void> _removeDuplicateCity(
    List<ExaminationRoomRouteListElementDm>? lists,
    city,
    bool isRoad,
  ) async {
    List<Map<String, dynamic>> _cMap = [];
    Map<int, List<ExaminationRoomRouteListElementDm>> map = {};
    map[0] = [];
    if (lists != null) {
      List<String> cityDistrictUnique = [];
      //通过district行政区划排序
      lists.sort((a, b) => a.district.compareTo(b.district));
      for (var list in lists) {
        //将所有考场数据插入到第一个列表中
        map[0]!.add(list);
        //将所有考场的行政区划添加到list中
        cityDistrictUnique.add(list.district.toString());
        if(list.district!=0){
          if (map.containsKey(list.district)) {
            map[list.district]!.add(list);
          } else {
            map[list.district] = [list];
          }
        }
      }
      //去重
      cityDistrict = cityDistrictUnique.toSet().toList();
      //通过行政区划获取对应的城市名称并添加到map里
      final res = await rootBundle.loadString('assets/data/area.json');
      Map<String, dynamic> cityMapList = json.decode(res);
      List list = cityMapList['china'];
      for (var item in list) {
        for (var value in cityDistrict) {
          if (value == item['code']) {
            // cityName.add(item['name']);
            _cMap.add({
              'cityName': item['name'],
              'city': city,
              'division': value,
            });
          }
        }
      }
      // for (var cMapItem in _cMap) {
      //   map[int.parse(cMapItem['division'])] = [];
      //   for (var listItem in lists) {
      //     if (listItem.district.toString() == cMapItem['division']) {
      //       map[listItem.district]?.add(listItem);
      //     }
      //   }
      // }
      //插入全部按钮
      _cMap.insert(0, {'cityName': '全部', 'city': city, 'division': '0'});
      if (isRoad) {
        examinationRoomRoadRouteListMap = map;
        cityRoadMap = _cMap;
      } else {
        examinationRoomRouteListMap = map;
        cityMap = _cMap;
      }
      debugPrint("====_cityMap==${_cMap.toString()}");
    }
  }

  getSubjectThreePracticalVideo() async {
    if (!MainController.isExistToken) {
      return;
    }
    try {
      var value =
          await homeRepository.getSubjectThreePracticalVideo(subject: 3);
      if (value != null) {
        theoryVideoListDm = value;
        update();
      }
    } catch (e) {
      theoryVideoListDm = null;
      update();
      if (e is DomainException) {
        Toast.show(e.message);
      } else {
        Toast.show(e.toString());
      }
      rethrow;
    }
  }

  //跳转目录
  goCatalogPage(int index) {
    homeRepository.setExmMenuAndListData(cityMap, examinationRoomRouteListMap);
    homeRepository.setExmRoadMenuAndListData(
        cityRoadMap, examinationRoomRoadRouteListMap);
    homeRepository.setThreeSubjectsPoint(threeSubjectsPoint??ThreeSubjectsPoint());
    Get.toNamed(AppRoutes.subjectThreeCatalogPage,
        arguments: {'selectIndex': index});
  }

  _getAccountInfo() async {
    userAccount = await userRepository.userAccountDM;
    update();
  }

  @override
  void onClose() {
    print('ExaminationPage SectionThreeController onClose');
    _userSubject.cancel();
    tabController?.dispose();
    tabPointController?.dispose();
    scrollController.dispose();
    _cityInfoSubscription.cancel();
    IEventBus.get().unregister(_ibus);
    super.onClose();
  }

  @override
  void onReady() async {
    print('ExaminationPage SectionThreeController create');
    userRepository = Get.find<MainController>().userRepository;
    homeRepository = Get.find<MainController>().homeRepository;
    timingRepository = Get.find<MainController>().timingRepository;
    //设置城市信息
    await homeRepository.setCityInfo();
    _cityInfoSubscription = homeRepository.getCityInfo().listen((event) {
      debugPrint(
          "====cityInfo==${event.toString()}----${event != null}----${event?.cityCode}");
      if (event != null) {
        // getExaminationRoomsData(
        //     division: int.parse(event.cityCode), city: event.cityName);
      } else {
        _requestLocation();
      }
    });
    // 订阅事件
    _ibus = IEventBus.get().register<IEvent>((event) {
      if (event.type == JkKey.EVENT_REFRESH_MY_RECORD) {
        if (event.type == JkKey.EVENT_HOURS_UPDATE) {
          update();
        }
      }
    });
    _getAccountInfo();
    _userSubject = userRepository.getUserAccount().listen((userAccountNew) {
      if (userAccountNew != null) {
        userAccount = userAccountNew;
        getPointVipOpenConfig();
        // debugPrint('userAccountNew:${userAccountNew.name}');
        update();
      }
    });
    await onRefresh();
    isInit = true;
    super.onReady();
  }

  getCountdown() async {
    isShowCount = await userRepository.timeComparison(
        await PreferencesService().getInt('dengguangmoniExperienceStartTime'),
        'dengguangmoniExperienceStartTime');
    if (isShowCount) {
      int? tmp =
          await PreferencesService().getInt('dengguangmoniExperienceStartTime');
      final DateTime pastDateTime =
          DateTime.fromMillisecondsSinceEpoch((tmp ?? 0) * 1000, isUtc: true)
              .toLocal();
      final DateTime nowDateTime = DateTime.fromMillisecondsSinceEpoch(
              await userRepository.getServerTime(),
              isUtc: true)
          .toLocal();
      // 计算两者之间的差异
      final Duration difference = nowDateTime.difference(pastDateTime);
      countDownSeconds = (1800 - difference.inSeconds) % 60;
      countDownMinutes = (1800 - difference.inSeconds) ~/ 60;
      Timer.periodic(const Duration(seconds: 1), (timer) {
        countDownSeconds--;
        if (countDownSeconds < 0) {
          countDownSeconds = 59;
          countDownMinutes--;
        }
        if (countDownMinutes < 0) {
          isShowCount = false;
          timer.cancel();
        }
        update();
      });
    } else {
      update();
    }
  }

  onRefresh() async {
    try {
      _getSubjectThreeBanner();
      await getSubjectThreePracticalVideo();
    } catch (e) {

    }
    try{
      await getExaminationRoomsData(
        city: homeRepository.cityInfo.cityName,
        division: stringToIntConverter(homeRepository.cityInfo.cityCode),
      );
      isLoading = false;
      update();
    } catch (e) {
      isLoading = false;
      update();
    }
  }

  void changePointTabIndex(int index) {
    currentPointIndex = index;
    update();
  }

  void changeTabIndex(int index) {
    currentIndex = index;
    update();
  }

  @override
  void onInit() {
    scrollController.addListener(() {
      if(scrollController.offset >= 100.h){
        if(bannerIsStartScroll){
          carouselController.stopAutoPlay();
          bannerIsStartScroll = false;
          bannerIsExitView = false;
          update();
        }
      }else{
        if(!bannerIsStartScroll){
          carouselController.startAutoPlay();
          bannerIsStartScroll = true;
          bannerIsExitView = true;
          update();
        }
      }
    });
    super.onInit();
  }
}

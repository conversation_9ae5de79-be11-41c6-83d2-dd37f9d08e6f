import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/mainController.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:easy_rich_text/easy_rich_text.dart';

import 'package:flukit/flukit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/src/home/<USER>';
import 'package:home/src/home/<USER>/section_three_controller.dart';
import 'package:home_repository/home_repository.dart';
import 'package:login/login.dart';
import 'package:timing_repository/timing_repository.dart';
import 'package:user_repository/user_repository.dart';
import 'package:tools/tools.dart';

import '../custom_widget/common_view.dart';
import '../custom_widget/grallist.dart';

class SectionThreePage extends StatelessWidget {
  SectionThreePage({
    super.key,
    required this.homeRepository,
    required this.gotoSubjectThreePracticalVideoDetail,
    required this.timingRepository,
    required this.userRepository,
    required this.subThreeVip45DetailVideoCallback,
  });
  final Function(
          ExaminationRoomRouteListElementDm? examinationRoomRouteListElementDm)
      subThreeVip45DetailVideoCallback;
  final HomeRepository homeRepository;
  final TimingRepository timingRepository;
  final UserRepository userRepository;
  final void Function(
      {TheoryVideoListDm? theoryVideoListDM,
      int index,
      String vid}) gotoSubjectThreePracticalVideoDetail;
  //科三学时历程信息
  // _getSubjectThreeTimeTable() async {
  //   try {
  //     final result =await homeRepository.getSubjectOneTimeTable(subject: 3);
  //     if(result!=null) {
  //       setState(() {
  //         subjectThreeTimeTable = result;
  //       });
  //     }
  //   }catch(e){
  //     if(e is DomainException){
  //       Toast.show(e.message);
  //     }else{
  //       Toast.show("网络错误，请稍后重试");
  //     }
  //   }
  // }
  ScrollController scrollController = ScrollController();
  @override
  Widget build(BuildContext context) {
    return GetBuilder<SectionThreeController>(
        // assignId: true,
        // autoRemove: false,
        init: SectionThreeController(),
        tag: ExaminationPageState.key.toString(),
        builder: (logic) {
          return !logic.isLoading
              ? Container(
                  // color: Color(0xffF5F7F9),
                  decoration: const BoxDecoration(
                      // color: Color(0xffF5F7F9),
                      gradient: LinearGradient(
                          colors: [Color(0xffFFFFFF), Color(0xffF5F7F9)],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter)),
                  child: EasyRefresh.builder(
                      // controller: logic.easyRefreshController,
                      header: const CupertinoHeader(triggerOffset: 20),
                      scrollController: logic.scrollController,
                      triggerAxis: Axis.vertical,
                      onRefresh: () {
                        logic.onRefresh();
                      },
                      childBuilder: (context, physics) {
                        return CustomScrollView(
                          physics: physics,
                          controller: logic.scrollController,
                          slivers: [
                            sliverPaddingToBoxAdapter(
                              padding: EdgeInsets.only(top: 0.h, bottom: 11.h),
                              child: GalleryList(
                                  carouselController: logic.carouselController,
                                  bannerListDm: logic.bannerList,
                                  autoPlay: logic.bannerIsStartScroll,
                                  timingRepository: timingRepository,
                                  subject: 3,
                                  isTabHome: true),
                            ),
                            _buildSubjectThreeYinDao(logic),
                            _buildExaminationRoomSubThree(logic),
                            _buildRoadTest(logic),
                            _buildPointMapSubThree(logic),
                            buildStudyHouse(3, timingRepository),
                            _buildSimulatedExercises(logic),
                            _buildExplanationProject(logic)
                          ],
                        );
                      }),
                )
              : const LoadingGifWidget();
        });
  }

  //新版-科目三-引导
  Widget _buildSubjectThreeYinDao(SectionThreeController logic) {
    return sliverPaddingToBoxAdapter(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          meIconButton2(
              icon: "sub3_zhenshikaochangmoni",
              text: "考场模拟",
              color: const Color(0xff555555),
              fontSize: 13.sp,
              onTap: () {
                if (!MainController.isLoginIntercept()) {
                  return;
                }
                BuryingPointUtils.instance.addPoint(
                    buryingPointList: BuryingPointList(
                        eventType: 3,
                        entranceType: 18,
                        action: 1,
                        browseDuration: 0));
                logic.goCatalogPage(3);
                // widget.timingRepository.openTimingMiniProgram();
                // JumpUtils.get().jumpFolder(JkKey.SUBONE, "error");
              }),
          meIconButton2(
              icon: "sub3_kaochangdianweitu",
              text: "考场点位",
              color: const Color(0xff555555),
              fontSize: 13.sp,
              onTap: () {
                if (!MainController.isLoginIntercept()) {
                  return;
                }
                BuryingPointUtils.instance.addPoint(
                    buryingPointList: BuryingPointList(
                        eventType: 3,
                        entranceType: 19,
                        action: 1,
                        browseDuration: 0));
                logic.goCatalogPage(2);
                // widget.timingRepository.openTimingMiniProgram();
                // JumpUtils.get().jumpFolder(JkKey.SUBONE, "error");
              }),
          meIconButton2(
              icon: "sub3_dengguangmoni",
              text: "灯光模拟",
              color: const Color(0xff555555),
              fontSize: 13.sp,
              onTap: () async {
                if (!MainController.isLoginIntercept()) {
                  return;
                }
                BuryingPointUtils.instance.addPoint(
                    buryingPointList: BuryingPointList(
                        eventType: 3,
                        entranceType: 20,
                        action: 1,
                        browseDuration: 0));
                if (await userRepository.isActivateVip(4)) {
                  IPlatform.get().jumpLightSimulation('light', '');
                } else {
                  timingRepository.goBuyTypeVip(4);
                }
                // widget.timingRepository.openTimingMiniProgram();
                // JumpUtils.get().jumpFolder(JkKey.SUBONE, "error");
              }),
          meIconButton2(
              icon: "sub3_dainikankaochang",
              text: "带你看考场",
              color: const Color(0xff555555),
              fontSize: 13.sp,
              onTap: () {
                if (!MainController.isLoginIntercept()) {
                  return;
                }
                BuryingPointUtils.instance.addPoint(
                    buryingPointList: BuryingPointList(
                        eventType: 3,
                        entranceType: 21,
                        action: 1,
                        browseDuration: 0));
                logic.goCatalogPage(0);
              }),
        ],
      ).paddingOnly(bottom: 16.h),
    );
  }

  //去看考场
  Widget _buildExaminationRoomSubThree(SectionThreeController logic) {
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 11.w),
      sliver: SliverToBoxAdapter(
        child: Container(
          height: 207.h,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(10.r),
            //   image: DecorationImage(
            // fit: BoxFit.fill,
            // image: assetImg2(img: 'bg_kesan_kaochang'),)
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 41.h,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xff329EF4), Color(0xff9AEAFF)],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10.r),
                      topRight: Radius.circular(10.r)),
                ),
                child: Row(
                  children: [
                    logic.userAccount?.platSchoolName == null
                        ? Expanded(
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: GestureDetector(
                                  onTap: () {
                                    if (!MainController.isLoginIntercept()) {
                                      return;
                                    }
                                    Get.toNamed(AppRoutes.homeStudyTypeSelect);
                                  },
                                  child: app16spFFFText('去绑定驾校',
                                      fontFamily: 'PingFangSC-Semibold')
                                  // assImg2(img: 'subt_goto_bing_jiaxiao', w: 94.w,h: 17.h),
                                  ),
                            ),
                          )
                        : Expanded(
                            child: app16spAC5000Text(
                                    logic.userAccount?.platSchoolName,
                                    color: AppColors.white,
                                    fontFamily: 'PingFangSC-Semibold',
                                    overflow: TextOverflow.ellipsis)
                                .paddingOnly(left: 16.w),
                          ),
                    SizedBox(width: 16.w),
                    GestureDetector(
                      onTap: () {
                        if (!MainController.isLoginIntercept()) {
                          return;
                        }
                        logic.goCatalogPage(3);
                      },
                      child: assImg2(img: 'subt_qiehuankaoc', w: 86.w, h: 25.h)
                          .paddingOnly(right: 14.w),
                    ),
                  ],
                ).paddingOnly(left: 11.w, top: 0.h, bottom: 0.h),
              ),
              Flexible(
                  fit: FlexFit.loose,
                  child: logic.cityMap.isEmpty
                      ? Center(
                          child: assImg2(
                              img: 'kesan_zanwushuju', w: 162.w, h: 132.h),
                        )
                      : Column(
                          children: [
                            SizedBox(height: 15.h),
                            _buildExaminationRoomTabBar(logic),
                            Expanded(
                              child: TabBarView(
                                controller: logic.tabController,
                                children: logic.examinationRoomRouteListDm ==
                                        null
                                    ? []
                                    : logic.examinationRoomRouteListMap.values
                                        .map((e) {
                                        return Container(
                                          height: 126.h,
                                          width: 192.w,
                                          decoration: BoxDecoration(
                                            // color: AppColors.white,
                                            borderRadius:
                                                BorderRadius.circular(8.r),
                                          ),
                                          child: ListView.builder(
                                              itemCount: e.length,
                                              scrollDirection: Axis.horizontal,
                                              itemBuilder: (_, index) {
                                                return _kaoCItem(e, index);
                                              }),
                                          // ClipRRect(
                                          //     clipBehavior: Clip.antiAlias,
                                          //     borderRadius: BorderRadius.circular(8.r),
                                          //     child: assImg2(img: 'bg_kesan_shipin_selected',fit: BoxFit.contain)
                                          // ),
                                        ).paddingOnly(left: 10.w);
                                      }).toList(),
                              ),
                            )
                          ],
                        ).paddingSymmetric(horizontal: 1.w)),
            ],
          ),
        ).paddingOnly(bottom: 11.h),
      ),
    );
  }

  //考场点位图
  Widget _buildPointMapSubThree(SectionThreeController logic) {
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 11.w),
      sliver: SliverToBoxAdapter(
        child: Container(
          height: 207.h,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(10.r),
            //     image: DecorationImage(
            //   fit: BoxFit.fill,
            //   image: assetImg2(img: 'bg_kesan_kaochang'),
            // )
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 41.h,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xff4FB1FF), Color(0xff93E9FF)],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10.r),
                      topRight: Radius.circular(10.r)),
                ),
                child: Row(
                  children: [
                    logic.userAccount?.platSchoolName == null
                        ? Expanded(
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: GestureDetector(
                                  onTap: () {
                                    if (!MainController.isLoginIntercept()) {
                                      return;
                                    }
                                    Get.toNamed(AppRoutes.homeStudyTypeSelect);
                                  },
                                  child: app16spFFFText('去绑定驾校',
                                      fontFamily: 'PingFangSC-Semibold')
                                  // assImg2(img: 'subt_goto_bing_jiaxiao', w: 94.w,h: 17.h),
                                  ),
                            ),
                          )
                        : Expanded(
                            child: app16spAC5000Text(
                                    logic.userAccount?.platSchoolName,
                                    color: AppColors.white,
                                    fontFamily: 'PingFangSC-Semibold',
                                    overflow: TextOverflow.ellipsis)
                                .paddingOnly(left: 16.w)),
                    GestureDetector(
                      onTap: () {
                        if (!MainController.isLoginIntercept()) {
                          return;
                        }
                        logic.goCatalogPage(2);
                      },
                      child: assImg2(img: 'subt_qiehuankaoc', w: 86.w, h: 25.h)
                          .paddingOnly(right: 14.w),
                    ),
                  ],
                ).paddingOnly(left: 11.w, top: 0.h, bottom: 0.h),
              ),
              Flexible(
                  fit: FlexFit.loose,
                  child: (logic.threeSubjectsPoint?.list ?? []).isEmpty
                      ? Center(
                          child: assImg2(
                              img: 'kesan_zanwushuju', w: 162.w, h: 132.h),
                        )
                      : Column(
                          children: [
                            SizedBox(height: 15.h),
                            _buildExaminationPointTabBar(logic),
                            Expanded(
                              child: TabBarView(
                                  controller: logic.tabPointController,
                                  children: logic.threeSubjectsPoint?.list ==
                                          null
                                      ? []
                                      : List.generate(
                                          logic
                                              .threeSubjectsPoint!.list!.length,
                                          (index) => Container(
                                                height: 126.h,
                                                width: 192.w,
                                                decoration: BoxDecoration(
                                                  // color: AppColors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          8.r),
                                                ),
                                                child: ListView.builder(
                                                    itemCount: logic
                                                        .threeSubjectsPoint!
                                                        .list![index]
                                                        .routMapRoutes
                                                        ?.length,
                                                    scrollDirection:
                                                        Axis.horizontal,
                                                    itemBuilder: (_, sIndex) {
                                                      return _pointCItem(
                                                          logic,
                                                          logic
                                                              .threeSubjectsPoint!
                                                              .list![index]
                                                              .routMapRoutes!,
                                                          index,
                                                          sIndex);
                                                    }),
                                              ).paddingOnly(left: 10.w))),
                            )
                          ],
                        ).paddingSymmetric(horizontal: 1.w)),
            ],
          ),
        ).paddingOnly(bottom: 6.h),
      ),
    );
  }

  bool getIsBuy(String productId) {
    return userRepository.isPointVip(
        homeRepository.cityInfo.cityCode, productId);
  }

  //点位图列表item
  Widget _pointCItem(SectionThreeController logic, List<RoutMapRoutes> exList,
      int index, int sIndex) {
    bool isBuy = getIsBuy(exList[sIndex].productId ?? "");
    return InkWell(
      onTap: () async {
        if (!MainController.isLoginIntercept()) {
          return;
        }
        homeRepository.setThreeSubjectsPoint(logic.threeSubjectsPoint!);
        Get.toNamed(AppRoutes.detailPointMapPage,
            arguments: {"selectedIndex": index, 'selectedSIndex': sIndex});
      },
      child: Stack(
        children: [
          ClipRRect(
                  clipBehavior: Clip.antiAlias,
                  borderRadius: BorderRadius.circular(10.r),
                  child: isBuy
                      ? networkImg(
                          img: exList[sIndex].cover,
                          w: 170.w,
                          h: 100.h,
                          fit: BoxFit.cover,
                        )
                      : assImg2(
                          img: 'point_defuat_new',
                          w: 170.w,
                          h: 100.h,
                          fit: BoxFit.fill))
              .paddingOnly(bottom: 1.h, right: 16.w),
          Positioned(
              child: isBuy
                  ? const SizedBox()
                  : Stack(
                      alignment: Alignment.center,
                      children: [
                        Container(
                          width: 170.w,
                          height: 100.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            color: AppColors.D8D8D8.withOpacity(0.1),
                          ),
                        ),
                        Positioned(
                            child: assImg2(
                                img: 'icon_xianlutu_weijiesuo',
                                w: 24.w,
                                h: 24.h))
                      ],
                    ))
        ],
      ),
    );
  }

  //考场列表item
  Widget _kaoCItem(List<ExaminationRoomRouteListElementDm> exList, int index) {
    return InkWell(
      onTap: () async {
        if (!MainController.isLoginIntercept()) {
          return;
        }
        if (await userRepository.isActivateVip(1,
            division: homeRepository.cityInfo!.cityCode)) {
          homeRepository.setExaminationRoomRouteListElementDmList(
              exList, index);
          subThreeVip45DetailVideoCallback.call(exList[index]);
        } else {
          timingRepository.goBuyTypeVip(1);
        }
      },
      child: ClipRRect(
              clipBehavior: Clip.antiAlias,
              borderRadius: BorderRadius.circular(8.r),
              child: networkImg(img: exList[index].cover, fit: BoxFit.fill))
          .paddingOnly(bottom: 16.h, right: 16.w),
    );
  }

  //路考模拟
  Widget _buildRoadTest(SectionThreeController logic) {
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 11.w, vertical: 1.h),
      sliver: SliverToBoxAdapter(
        child: Container(
          height: 184.33.h,
          margin: REdgeInsets.only(bottom: 10.h),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              gradient: const LinearGradient(
                colors: [
                  Color(0xffACE0FF),
                  Color(0xffE7F8FF),
                  Color(0xffB2E2FF)
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              )
              //     image: DecorationImage(
              //   fit: BoxFit.fill,
              //   image: assetImg2(img: 'bg_kesan_zhenshilukao'),
              // )
              ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  app16spAC5000Text('真实路考模拟',
                          color: Colors.black,
                          fontFamily: 'PingFangSC-Semibold')
                      .paddingOnly(left: 16.w),
                  // Spacer(),
                  // app12spAC5000Text('切换考场'),
                  // assImg2(img: 'icon _more',w: 14.w).paddingOnly(right: 14.w),
                ],
              ).paddingOnly(top: 16.h, bottom: 16.h),
              Row(
                children: [
                  Container(
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Stack(
                        children: [
                          ClipRRect(
                              clipBehavior: Clip.antiAlias,
                              borderRadius: BorderRadius.circular(8.r),
                              child: Image.asset(
                                'assets/home_img/zhenshilukao.gif',
                                width: 170.w,
                                height: 100.h,
                                fit: BoxFit.fill,
                              )),
                          // Positioned(
                          //   // top: -2.h,
                          //   // left: -3.w,
                          //   child: assImg2(
                          //       img: 'img_kaochangluxian',
                          //       w: 60.w,
                          //       h: 24.w,
                          //       fit: BoxFit.fill),
                          // ),
                        ],
                      )).paddingOnly(left: 16.w, right: 16.w),
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        app16spAC5000Text('看视频，再模考',
                            color: Colors.black,
                            fontFamily: 'PingFangSC-Medium'),
                        app16spAC5000Text('双管齐下没烦恼',
                            color: Colors.black,
                            fontFamily: 'PingFangSC-Medium'),
                        SizedBox(height: 19.h),
                        GestureDetector(
                            onTap: () async {
                              if (!MainController.isLoginIntercept()) {
                                return;
                              }
                              if (await userRepository.isActivateVip(2,
                                  division:
                                      homeRepository.cityInfo!.cityCode)) {
                                if (logic.examinationRoomRouteListDm != null) {
                                  logic.goCatalogPage(1);
                                }
                              } else {
                                timingRepository.goBuyTypeVip(2);
                              }
                            },
                            child: assImg2(
                                img: 'subt_lijistudy', w: 86.w, h: 25.h)),
                        // gotoSeeExaminationRoomBtn(
                        //     text: '立即学习',
                        //     fontFamily: 'PingFangSC-Semibold',
                        //     onTap: () async {
                        //       if (!MainController.isLoginIntercept()) {
                        //         return;
                        //       }
                        //       if (await userRepository.isActivateVip(2,
                        //           division: homeRepository.cityInfo!.cityCode)) {
                        //         if (logic.examinationRoomRouteListDm != null) {
                        //           logic.goCatalogPage(1);
                        //         }
                        //       } else {
                        //         timingRepository.goBuyTypeVip(2);
                        //       }
                        //     }).marginOnly(
                        //   left: 16.w,
                        //   top: 16.h,
                        // ),
                      ])
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  //考试项目讲解
  Widget _buildExplanationProject(SectionThreeController logic) {
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 11.h),
      sliver: SliverToBoxAdapter(
          child: logic.theoryVideoListDm == null
              ? const SizedBox()
              : white8radiusContainer(
                  height: 190.h,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      logic.theoryVideoListDm == null
                          ? const SizedBox()
                          : app16spA1Text('考试项目讲解',
                                  fontFamily: 'PingFangSC-Semibold')
                              .paddingOnly(top: 13.h, bottom: 16.h, left: 16.w),
                      Expanded(
                        child: ListView.builder(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          scrollDirection: Axis.horizontal,
                          // physics: const NeverScrollableScrollPhysics(),
                          // gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          //   crossAxisCount: 2,
                          //   childAspectRatio: 152.w / 135.h,
                          // ),
                          itemCount: logic.theoryVideoListDm == null
                              ? 0
                              : logic.theoryVideoListDm!.listDM.length,
                          itemBuilder: (BuildContext context, int index) {
                            return _listItem(logic,
                                    img: logic.theoryVideoListDm!.listDM[index]
                                        .cover[0],
                                    title: logic
                                        .theoryVideoListDm!.listDM[index].title,
                                    practicalVideoId: logic.theoryVideoListDm!
                                        .listDM[index].address,
                                    index: index)
                                .paddingSymmetric(horizontal: 8.w);
                          },
                        ),
                      ),
                    ],
                  ),
                ).paddingOnly(bottom: 3.h, top: 6.h)),
    );
  }

  Widget _listItem(SectionThreeController logic,
      {required img, required int index, title, practicalVideoId}) {
    return GestureDetector(
      onTap: () {
        if (logic.theoryVideoListDm?.listDM[index].labels == null) {
          gotoSubjectThreePracticalVideoDetail(
              theoryVideoListDM: logic.theoryVideoListDm,
              index: index,
              vid: jsonDecode(practicalVideoId)['Data']['vid']);
        } else if (logic.theoryVideoListDm?.listDM[index].labels != null &&
            userRepository.vipProductMap.vip3) {
          gotoSubjectThreePracticalVideoDetail(
              theoryVideoListDM: logic.theoryVideoListDm,
              index: index,
              vid: jsonDecode(practicalVideoId)['Data']['vid']);
        } else {
          timingRepository.goBuyTypeVip(5);
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
              clipBehavior: Clip.antiAlias,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.r),
                  topRight: Radius.circular(8.r),
                  bottomLeft: Radius.circular(8.r),
                  bottomRight: Radius.circular(8.r)),
              child: networkImg(img: img, w: 152.w, h: 105.h, fit: BoxFit.fill)

              // child: assImg2(img: img,w: 152.w,h: 105.h,fit: BoxFit.fill)
              ),
          app14spA1Text(title, fontFamily: 'PingFangSC-Regular')
              .paddingOnly(top: 6.h)
        ],
      ),
    );
  }

  //考场点位图选择tabBar
  PreferredSize _buildExaminationPointTabBar(SectionThreeController logic) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w),
        alignment: Alignment.topLeft,
        child: TabBar(
          tabAlignment: TabAlignment.start,
          isScrollable: true,
          dividerHeight: 0,
          labelStyle: TextStyle(color: AppColors.white, fontSize: 14.sp),
          unselectedLabelStyle:
              TextStyle(color: AppColors.ECAF35, fontSize: 14.sp),
          labelPadding: EdgeInsets.symmetric(horizontal: 5.w),
          // indicatorPadding: EdgeInsets.only(top: 30.h, left: 35.w, right: 35.w),
          indicatorColor: AppColors.other_login_text,
          indicatorSize: TabBarIndicatorSize.tab,
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          physics: const ScrollPhysics(),
          indicator: const BoxDecoration(),
          // indicator: BoxDecoration(
          //   gradient: LinearGradient(colors: [AppColors.other_login_text, AppColors.AC5000]),
          // ),
          // indicator:
          //  const BoxDecoration(
          //     image: DecorationImage(
          //         image:
          //         AssetImage("assets/home_img/bg_kesan_xuanzhekaochang_selected.png"))),
          controller: logic.tabPointController,
          tabs: List.generate(
              (logic.threeSubjectsPoint?.list ?? []).length,
              (index) => customPointTab(logic,
                  text: logic.threeSubjectsPoint?.list![index].title,
                  index: index)),
          onTap: (index) {
            logic.changePointTabIndex(index);
          },
          // tabs: [
          // ],
        ),
      ),
    );
  }

  //考场选择tabBar
  PreferredSize _buildExaminationRoomTabBar(SectionThreeController logic) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w),
        alignment: Alignment.topLeft,
        child: TabBar(
          tabAlignment: TabAlignment.start,
          isScrollable: true,
          dividerHeight: 0,
          labelStyle: TextStyle(color: AppColors.white, fontSize: 14.sp),
          unselectedLabelStyle:
              TextStyle(color: AppColors.ECAF35, fontSize: 14.sp),
          labelPadding: EdgeInsets.symmetric(horizontal: 5.w),
          // indicatorPadding: EdgeInsets.only(top: 30.h, left: 35.w, right: 35.w),
          indicatorColor: AppColors.other_login_text,
          indicatorSize: TabBarIndicatorSize.tab,
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          physics: const ScrollPhysics(),
          indicator: const BoxDecoration(),
          // indicator: BoxDecoration(
          //   gradient: LinearGradient(colors: [AppColors.other_login_text, AppColors.AC5000]),
          // ),
          // indicator:
          //  const BoxDecoration(
          //     image: DecorationImage(
          //         image:
          //         AssetImage("assets/home_img/bg_kesan_xuanzhekaochang_selected.png"))),
          controller: logic.tabController,
          tabs: logic.cityMap.isEmpty
              ? []
              : logic.cityMap
                  .asMap()
                  .entries
                  .map((e) => customTab(logic,
                      text: '${e.value['city']}${e.value['cityName']}考场',
                      index: e.key))
                  .toList(),
          onTap: (index) {
            logic.changeTabIndex(index);
          },
          // tabs: [
          // ],
        ),
      ),
    );
  }

  Widget customPointTab(SectionThreeController logic, {text, index}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      height: logic.currentPointIndex == index ? 25.h : 25.h,
      // width: 110.w,
      decoration: BoxDecoration(
        color: logic.currentPointIndex == index
            ? const Color(0xFF2BA9F4)
            : const Color(0xFFF9F8F8),
        borderRadius: BorderRadius.circular(12.5.r),
      ),
      child: Center(
        child: Text(text,
                style: TextStyle(
                    color: logic.currentPointIndex == index
                        ? AppColors.white
                        : const Color(0xffABABAB),
                    fontSize: logic.currentPointIndex == index ? 14.sp : 14.sp))
            .paddingOnly(bottom: logic.currentPointIndex == index ? 0.h : 0),
      ),
    ).paddingOnly(bottom: 8.h);
  }

  Widget customTab(SectionThreeController logic, {text, index}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      height: logic.currentIndex == index ? 25.h : 25.h,
      // width: 110.w,
      decoration: BoxDecoration(
        color:
            logic.currentIndex == index ? const Color(0xFF2BA9F4) : const Color(0xFFF9F8F8),
        // color: _currentIndex == index? AppColors.AC5000 : AppColors.white,
        borderRadius: BorderRadius.circular(12.5.r),
      ),
      child: Center(
        child: Text(text,
            style: TextStyle(
              color: logic.currentIndex == index
                  ? AppColors.white
                  : const Color(0xffABABAB),
              fontSize: logic.currentIndex == index ? 14.sp : 14.sp,
              fontFamily: logic.currentIndex == index
                  ? 'PingFangSC-Semibold'
                  : 'PingFangSC-Regular',
            )).paddingOnly(bottom: logic.currentIndex == index ? 0.h : 0),
      ),
    ).paddingOnly(bottom: 8.h);
  }

  //模拟练习
  Widget _buildSimulatedExercises(SectionThreeController logic) {
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 11.w, vertical: 6.h),
      sliver: SliverToBoxAdapter(
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 11.w),
              // height: 300.33.h,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(10.r),
                //     image: DecorationImage(
                //   fit: BoxFit.fill,
                //   image: assetImg2(img: 'bg_kesan_monilianxi'),
                // )
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  app16spAC5000Text('模拟练习',
                          color: AppColors.C333333,
                          fontFamily: 'PingFangSC-Semibold')
                      .paddingOnly(top: 11.h),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      assImg2(img: 'img_3d', w: 170.w, h: 100.h),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            app16spA1Text('3D模拟 在线练车',
                                fontFamily: 'PingFangSC-Medium'),
                            SizedBox(height: 11.h),
                            app14spA1Text('避坑不紧张',
                                    color: const Color(0xff868686),
                                    fontFamily: 'PingFangSC-Regular')
                                .paddingOnly(bottom: 30.h),
                            Align(
                              alignment: Alignment.centerRight,
                              child: GestureDetector(
                                onTap: () async {
                                  if (!MainController.isLoginIntercept()) {
                                    return;
                                  }
                                  if (await userRepository.isActivateVip(3)) {
                                    showDialog(
                                      context: Get.context!,
                                      barrierDismissible: false, // 点击空白区域不可关闭
                                      builder: (BuildContext context) {
                                        return Dialog(
                                          child: U3dConfirmDialog(
                                            content: '即将打开3D练车',
                                            cancelText: '取消',
                                            confirmText: '确认',
                                            onCancel: () {
                                              Get.back();
                                            },
                                            onConfirm: () {
                                              Get.back();
                                              IPlatform.get()
                                                  .jumpLightSimulation(
                                                      'subject3', '');
                                            },
                                          ),
                                        );
                                      },
                                    );
                                  } else {
                                    timingRepository.goBuyTypeVip(3);
                                  }
                                },
                                child: Container(
                                  alignment: Alignment.topRight,
                                  height: 25.h,
                                  width: 86.w,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: assetImg2(img: 'subt_lijistudy'),
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                  // child: Center(
                                  //   child: GestureDetector(
                                  //     onTap: () async {
                                  //
                                  //     },
                                  //     child: app14spFFFFText('立即学习',
                                  //         fontFamily: 'PingFangSC-Semibold'),
                                  //   ),
                                  // ),
                                ),
                              ),
                            )
                          ],
                        ).paddingOnly(left: 7.w),
                      )
                    ],
                  ).paddingOnly(top: 13.h, bottom: 13.h),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      assImg2(img: 'img_dengguang', w: 170.w, h: 100.h),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            app16spA1Text('考前灯光模拟练习',
                                fontFamily: 'PingFangSC-Medium'),
                            SizedBox(height: 11.h),
                            app14spA1Text('提前练习更熟练',
                                    color: const Color(0xff868686),
                                    fontFamily: 'PingFangSC-Regular')
                                .paddingOnly(bottom: 30.h),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                logic.isShowCount
                                    ? Row(
                                        children: [
                                          Container(
                                            height: 18.h,
                                            width: 18.w,
                                            // alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                                image: DecorationImage(
                                                    image: assetImg2(
                                                        img: 'mian_bg'))
                                                // borderRadius:
                                                //     BorderRadius.circular(9.r),
                                                // gradient: const LinearGradient(
                                                //     colors: [
                                                //       Color(0xFFFFCB63),
                                                //       Color(0xFFFF5F0C)
                                                //     ],
                                                //     begin: Alignment.topCenter,
                                                //     end: Alignment.bottomCenter),
                                                ),
                                            // child: Text("免",
                                            //     style: TextStyle(
                                            //         fontSize: 12.sp,
                                            //         fontWeight: FontWeight.bold,
                                            //         color: Colors.white)),
                                          ),
                                          const SizedBox(
                                            width: 3,
                                          ),
                                          Text(
                                            '${logic.countDownMinutes}:${logic.countDownSeconds}',
                                            style: TextStyle(
                                                fontWeight: FontWeight.w500,
                                                color: const Color(0xFFE6973D),
                                                fontSize: 14.sp),
                                          ),
                                        ],
                                      )
                                    : const SizedBox(),
                                SizedBox(width: 6.w),
                                GestureDetector(
                                  onTap: () async {
                                    if (!MainController.isLoginIntercept()) {
                                      return;
                                    }
                                    if (await userRepository.isActivateVip(4)) {
                                      showDialog(
                                        context: Get.context!,
                                        barrierDismissible: false, // 点击空白区域不可关闭
                                        builder: (BuildContext context) {
                                          return Dialog(
                                            child: U3dConfirmDialog(
                                              content: '即将打开3D灯光模拟',
                                              cancelText: '取消',
                                              confirmText: '确认',
                                              onCancel: () {
                                                Get.back();
                                              },
                                              onConfirm: () {
                                                Get.back();
                                                IPlatform.get()
                                                    .jumpLightSimulation(
                                                        'light', '');
                                              },
                                            ),
                                          );
                                        },
                                      );
                                    } else {
                                      timingRepository.goBuyTypeVip(4);
                                    }
                                  },
                                  child: Container(
                                    height: 25.h,
                                    width: 86.w,
                                    decoration: BoxDecoration(
                                      image: DecorationImage(
                                        image: assetImg2(img: 'subt_lijistudy'),
                                        fit: BoxFit.fill,
                                      ),
                                    ),
                                    // child: Center(
                                    //   child: GestureDetector(
                                    //     onTap: () async {
                                    //
                                    //     },
                                    //     child: app14spFFFFText('立即学习',
                                    //         fontFamily: 'PingFangSC-Semibold'),
                                    //   ),
                                    // ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ).paddingOnly(left: 7.w),
                      )
                    ],
                  ).paddingOnly(top: 13.h, bottom: 8.h),
                ],
              ),
            ),
            Positioned(
              top: 0.h,
              right: 0.w,
              child: assImg2(img: 'subt_kaoshiyure', w: 76.w, h: 29.h),
            )
          ],
        ),
      ),
    );
  }

  //学时详情
  Widget _buildStudyHouse() {
    return sliverPaddingToBoxAdapter(
        padding: EdgeInsets.all(10.h),
        child: Stack(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Expanded(
                    child: white8radiusContainer(
                  height: 178.h,
                  boxShadow: [
                    BoxShadow(
                        color: AppColors.C000000,
                        offset: Offset(-1.0.h, 3.0.h), //阴影y轴偏移量
                        blurRadius: 2.h, //阴影模糊程度
                        spreadRadius: 2.r //阴影扩散程度
                        )
                  ],
                  child: Row(
                    children: [
                      Expanded(
                          flex: 4,
                          child: Column(
                            // mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              app16spA1Text('我的学时').paddingOnly(
                                  left: 16.w, top: 16.h, bottom: 10.h),
                              Expanded(
                                  child: Stack(
                                alignment: Alignment.center,
                                children: <Widget>[
                                  Positioned(
                                    top: 1.0.h,
                                    child: TurnBox(
                                      turns: 5 / 8,
                                      child: GradientCircularProgressIndicator(
                                          colors: const [
                                            AppColors.other_login_text,
                                            AppColors.other_login_text,
                                          ],
                                          radius: 58.0.r,
                                          stokeWidth: 5.0,
                                          strokeCapRound: false,
                                          backgroundColor: AppColors.E1F1FF,
                                          totalAngle: 1.5 * pi,
                                          value: 0.7),
                                    ),
                                  ),
                                  Positioned(
                                    top: 38.0.h,
                                    child: app20sp268FF7Text("70%",
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Positioned(
                                    bottom: 38.h,
                                    child: app12sp1A1AText(
                                      "监管有效",
                                    ),
                                  ),
                                ],
                              ))
                            ],
                          )),
                      Expanded(
                          flex: 6,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              app14sp268Text('学时历程 >')
                                  .paddingOnly(right: 26.w, top: 16.h),
                              // app12sp1A1AText('共需学习学时：284分钟').paddingOnly(right: 26.w, top: 28.h),
                              _buildStudyHouseItem('共需学习学时', '284', top: 28.h),
                              _buildStudyHouseItem('总培训时长', '284'),
                              _buildStudyHouseItem('监管有效时长', '283')
                            ],
                          ))
                    ],
                  ),
                )),
                GestureDetector(
                  onTap: () {},
                  child: Container(
                    height: 156.h,
                    // padding: EdgeInsets.only(right: 16.w),
                    width: 39.w,
                    decoration: BoxDecoration(
                        image:
                            DecorationImage(image: assetImg2(img: "bg_kefu"))),
                  ),
                ).paddingOnly(right: 8.w)
              ],
            ),
            Positioned(
              bottom: 45.h,
              right: 20.w,
              child: assImg2(img: 'Ip_xueshi_kefu', w: 44.w, h: 48.h),
            )
          ],
        ).paddingOnly(left: 10.w, top: 5.h));
  }

  //学时监管item
  Widget _buildStudyHouseItem(String title, String time, {double? top}) {
    return EasyRichText(
      "$title：$time分钟",
      // strutStyle: StrutStyle(fontSize: 12.sp),
      patternList: [
        EasyRichTextPattern(
            targetString: title,
            style: TextStyle(
                color: AppColors.privacy_dialog_titile_color, fontSize: 12.sp)),
        EasyRichTextPattern(
            targetString: time,
            style: TextStyle(
                color: top == null
                    ? AppColors.other_login_text
                    : AppColors.privacy_dialog_titile_color,
                fontSize: 14.sp)),
        EasyRichTextPattern(
            targetString: "分钟",
            style: TextStyle(
                color: top == null
                    ? AppColors.other_login_text
                    : AppColors.privacy_dialog_titile_color,
                fontSize: 8.sp))
      ],
    ).paddingOnly(top: top ?? 10.h, right: 26.w);
  }
}

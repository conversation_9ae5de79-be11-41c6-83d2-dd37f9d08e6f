import 'dart:async';
import 'dart:convert';

import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:home/src/home/<USER>/common_view.dart';
import 'package:home_repository/home_repository.dart';
import 'package:timing_repository/timing_repository.dart';
import 'package:tools/tools.dart';
import 'package:user_repository/user_repository.dart';
import 'package:key_value_storage/key_value_storage.dart';

StreamSubscription? _ibus;

class SectionTwoController extends GetxController {
  late final HomeRepository homeRepository;
  late final TimingRepository timingRepository;
  late final UserRepository userRepository;
  TheoryVideoListDm? theoryVideoListDm;
  ExaminationRoomRouteListDm? examinationRoomRouteListDm; //考场选择data
  final AMapFlutterLocation _location = AMapFlutterLocation();
  late StreamSubscription<Map<String, dynamic>> _locationSubscription;
  late StreamSubscription<CityInfo?> _cityInfoSubscription;
  Map<int, List<ExaminationRoomRouteListElementDm>>
      examinationRoomRouteListMap = {};
  List<String> cityDistrict = [];
  List<Map<String, dynamic>> cityMap = [];
  bool isShowCount = false;
  int countDownMinutes = 0;
  int countDownSeconds = 0;
  static bool isInit = false;
  Map subjectTwoTimeTable = {};
  CarouselController carouselController = CarouselController();
  List<BannerDataDm>? bannerList;
  ScrollController scrollController = ScrollController();
  //banner是否开启自动滚动
  bool bannerIsStartScroll = true;
  //banner 是否在可见区域
  bool bannerIsExitView = true;
  late StreamSubscription<UserAccount?> _userAccountSubscription;
  UserAccount? userAccount;
  Future<void> getExaminationRoomsData(
      {division,
      String? city,
      bool isSelect = false,
      bool isCallBack = false}) async {
    try {
      final examinationRooms = await homeRepository.getExaminationRoomRoute(
        subject: 2,
        index: 1,
        rows: 20,
        division: division ?? 440100,
        isSelect: isSelect,
      );
      examinationRoomRouteListDm = examinationRooms;

      await _removeDuplicateCity(
          examinationRoomRouteListDm?.listDM, city ?? '广州市');
      if (isCallBack) {
        homeRepository.setExmMenuAndListData(
            cityMap, examinationRoomRouteListMap);
      }
      update();
    } catch (e) {
      debugPrint("=============$e");
      if (e is DomainException) {
        if (e.code == 9 && e.message == "没有相关数据") {
          examinationRoomRouteListDm = null;
          cityMap = [];
          examinationRoomRouteListMap = {};
          if (isCallBack) {
            await homeRepository.updateCityInfo(division);
            homeRepository.setExmMenuAndListData(
                cityMap, examinationRoomRouteListMap);
          }
          update();
        }
      } else {
        Toast.show(e.toString());
      }
    }
  }

  ///获取科二banner
  _getSubjectTwoBanner() async {
    final String token = await userRepository.getUserToken() ?? "";
    if (token.isEmpty) {
      try {
        var bannerList = await homeRepository.getHomeNoTokenBanner();
        this.bannerList = bannerList;
        update();
      } catch (e) {}
    } else {
      List<int> vipSubject = [];
      if (userRepository.getSubVip(2)) {
        vipSubject.add(2);
      }
      try {
        var bannerList = await homeRepository.getHomeBanner(
            district: int.parse(homeRepository.cityInfo.cityCode ?? "440100"),
            subject: [2],
            vipSubject: vipSubject);
        this.bannerList = bannerList;
        update();
      } catch (e) {
        print("error:$e");
      }
    }
  }

  Future<void> _removeDuplicateCity(
      List<ExaminationRoomRouteListElementDm>? lists, city) async {
    cityMap = [];
    examinationRoomRouteListMap = {};
    examinationRoomRouteListMap[0] = [];
    if (lists != null) {
      List<String> cityDistrictUnique = [];
      //通过district行政区划排序
      lists.sort((a, b) => a.district.compareTo(b.district));
      for (var list in lists) {
        //将所有考场数据插入到第一个列表中
        examinationRoomRouteListMap[0]!.add(list);
        //将所有考场的行政区划添加到list中
        cityDistrictUnique.add(list.district.toString());
        if (examinationRoomRouteListMap.containsKey(list.district)) {
          examinationRoomRouteListMap[list.district]!.add(list);
        } else {
          examinationRoomRouteListMap[list.district] = [list];
        }
      }
      //去重
      cityDistrict = cityDistrictUnique.toSet().toList();
      //通过行政区划获取对应的城市名称并添加到map里
      final res = await rootBundle.loadString('assets/data/area.json');
      Map<String, dynamic> cityMapList = json.decode(res);
      List list = cityMapList['china'];
      for (var item in list) {
        for (var value in cityDistrict) {
          if (value == item['code']) {
            // cityName.add(item['name']);
            cityMap.add({
              'cityName': item['name'],
              'city': city,
              'division': value,
            });
          }
        }
      }
      //插入全部按钮
      cityMap.insert(0, {'cityName': '全部', 'city': city, 'division': '0'});
      debugPrint("====_cityMap==${cityMap.toString()}");
    }
  }

  getCountdown() async {
    isShowCount = await userRepository.timeComparison(
        await PreferencesService().getInt('23dliancheExperienceStartTime'),
        '23dliancheExperienceStartTime');
    if (isShowCount) {
      int? tmp =
          await PreferencesService().getInt('23dliancheExperienceStartTime');
      final DateTime pastDateTime =
          DateTime.fromMillisecondsSinceEpoch((tmp ?? 0) * 1000, isUtc: true)
              .toLocal();
      final DateTime nowDateTime = DateTime.fromMillisecondsSinceEpoch(
              await userRepository.getServerTime(),
              isUtc: true)
          .toLocal();
      // 计算两者之间的差异
      final Duration difference = nowDateTime.difference(pastDateTime);
      countDownSeconds = (1800 - difference.inSeconds) % 60;
      countDownMinutes = (1800 - difference.inSeconds) ~/ 60;
      Timer.periodic(const Duration(seconds: 1), (timer) {
        countDownSeconds--;
        if (countDownSeconds < 0) {
          countDownSeconds = 59;
          countDownMinutes--;
        }
        if (countDownMinutes < 0) {
          isShowCount = false;
          timer.cancel();
        }
        update();
      });
    }else{
      update();
    }
  }

  void getSubjectTwoPracticalVideo() async {
    homeRepository.getSubjectTwoPracticalVideo(subject: 2).then((value) {
      print('getSubjectTwoPracticalVideo $value');
      theoryVideoListDm = value;
      update();
    }).onError((error, stackTrace) {
      theoryVideoListDm = null;
      update();
      // if (error is DomainException) {
      //   Toast.show(error.message);
      // } else {
      //   Toast.show(error.toString());
      // }
    });
  }

  _requestLocation() async {
    LocationInfo? locationInfo;
    if (!ExaminationController.initBandLocation) {
      locationInfo = await AMapUtils().startLocation(Get.context!);
    }
    if (locationInfo != null) {
      debugPrint("====requestLocation==${locationInfo.toString()}");
      getExaminationRoomsData(
        division: stringToIntConverter(locationInfo.arCode.toString()),
      );
    } else {
      getExaminationRoomsData(division: 440100);
      debugPrint("====location==定位失败，请检查网络或重试");
    }
  }

  @override
  void onInit() {
    userRepository = Get.find<MainController>().userRepository;
    homeRepository = Get.find<MainController>().homeRepository;
    timingRepository = Get.find<MainController>().timingRepository;
    print('ExaminationPage SectionTwoController create');
    // print('SectionTwoPage object');
    _locationSubscription = _location.onLocationChanged().listen((event) {});
    _cityInfoSubscription = homeRepository.getCityInfo().listen((event) {
      debugPrint(
          "====cityInfo==${event.toString()}----${event != null}----${event?.cityCode}");
      if (event != null) {
        getExaminationRoomsData(
            division: int.parse(event.cityCode), city: event.cityName);
      } else {
        _requestLocation();
      }
    });
    getSubjectTwoPracticalVideo();

    _getSubjectTwoBanner();
    // 订阅事件
    _ibus = IEventBus.get().register<IEvent>((event) {
      if (event.type == JkKey.EVENT_REFRESH_MY_RECORD) {
        if (event.type == JkKey.EVENT_HOURS_UPDATE) {
          update();
        }
      }
    });
    scrollController.addListener(() {
      if(scrollController.offset >= 100.h){
        if(bannerIsStartScroll){
          carouselController.stopAutoPlay();
          bannerIsStartScroll = false;
          bannerIsExitView = false;
          update();
        }
      }else{
        if(!bannerIsStartScroll){
          carouselController.startAutoPlay();
          bannerIsStartScroll = true;
          bannerIsExitView = true;
          update();
        }
      }
    });
    _userAccountSubscription =
        userRepository.getUserAccount().listen((account) {
      if (account != null) {
        userAccount = account;
        update();
      }
    });
    isInit = true;
    super.onInit();
  }

  refreshData() {
    _getSubjectTwoBanner();
    getSubjectTwoPracticalVideo();
    getExaminationRoomsData(
      city: homeRepository.cityInfo.cityName,
      division:
          stringToIntConverter(homeRepository.cityInfo.cityCode ?? '440100'),
    );
    getCountdown();
  }

  @override
  void onReady() {
    getCountdown();
    super.onReady();
  }

  @override
  void onClose() {
    _userAccountSubscription.cancel();
    _locationSubscription.cancel();
    _cityInfoSubscription.cancel();
    scrollController.dispose();
    IEventBus.get().unregister(_ibus);
    print('ExaminationPage SectionTwoController onclose');
    super.onClose();
  }

}

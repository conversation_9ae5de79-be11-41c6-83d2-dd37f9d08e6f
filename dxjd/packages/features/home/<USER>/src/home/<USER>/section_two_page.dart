import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/mainController.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flukit/flukit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/src/home/<USER>';
import 'package:home/src/home/<USER>/section_two_controller.dart';
import 'package:home_repository/home_repository.dart';
import 'package:login/login.dart';
import 'package:timing/timing.dart';
import 'package:tools/tools.dart';
import 'package:timing_repository/timing_repository.dart';
import 'package:user_repository/user_repository.dart';
import '../custom_widget/common_view.dart';
import '../custom_widget/grallist.dart';

class SectionTwoPage extends StatelessWidget {
  SectionTwoPage({
    super.key,
    required this.homeRepository,
    required this.gotoSubjectTwoPracticalVideoDetail,
    required this.switchExaminationRoomCallback,
    required this.timingRepository,
    required this.userRepository,
  });

  final void Function(
      List<Map<String, dynamic>> _cityMap,
      Map<int, List<ExaminationRoomRouteListElementDm>>
          _examinationRoomRouteListMap) switchExaminationRoomCallback;
  final HomeRepository homeRepository;
  final TimingRepository timingRepository;
  final UserRepository userRepository;
  final void Function(
      {TheoryVideoListDm? theoryVideoListDM,
      int index,
      String vid}) gotoSubjectTwoPracticalVideoDetail;
  //科二学时历程信息
  // _getSubjectTwoTimeTable() async {
  //   try {
  //     final result =await widget.homeRepository.getSubjectOneTimeTable(subject: 2);
  //     if(result!=null) {
  //       setState(() {
  //         subjectTwoTimeTable = result;
  //       });
  //     }
  //   }catch(e){
  //     if(e is DomainException){
  //       Toast.show(e.message);
  //     }else{
  //       Toast.show("网络错误，请稍后重试");
  //     }
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SectionTwoController>(
        // assignId: true,
        tag: ExaminationPageState.key.toString(),
        init: SectionTwoController(),
        builder: (logic) {
          return Container(
            // color: Color(0xffF5F7F9),
            decoration: const BoxDecoration(
                // color: Color(0xffF5F7F9),
                gradient: LinearGradient(
                    colors: [Color(0xffFFFFFF), Color(0xffF5F7F9)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter)),
            child: Column(
              children: [
                Expanded(
                  // backgroundColor: AppColors.F9F8F8,
                  child: EasyRefresh(
                    header: const CupertinoHeader(triggerOffset: 20.0),
                    onRefresh: () {
                      logic.refreshData();
                    },
                    scrollController: logic.scrollController,
                    child: CustomScrollView(
                      controller: logic.scrollController,
                      slivers: [
                        sliverPaddingToBoxAdapter(
                          padding: EdgeInsets.only(top: 0.h, bottom: 11.h),
                          child: GalleryList(
                              bannerListDm: logic.bannerList,
                              carouselController: logic.carouselController,
                              subject: 2,
                              autoPlay: logic.bannerIsStartScroll,
                              timingRepository: timingRepository,
                              isTabHome: true),
                        ),
                        _buildSubjectTwoYinDao(logic),
                        _buildExaminationRoom(logic),
                        // _buildStudyHouse(subjectTwoTimeTable),
                        buildStudyHouse(2, timingRepository),
                        _buildSimulatedExercises(logic),
                        _buildExplanationProject(logic)
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        });
  }

  //新版-科目二-引导
  Widget _buildSubjectTwoYinDao(SectionTwoController logic) {
    return sliverPaddingToBoxAdapter(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          meIconButton2(
              icon: "keer_vip",
              text: "科二VIP",
              color: Color(0xff555555),
              fontSize: 13.sp,
              onTap: () {
                if (!MainController.isLoginIntercept()) {
                  return;
                }
                BuryingPointUtils.instance.addPoint(
                    buryingPointList: BuryingPointList(
                        eventType: 3,
                        entranceType: 14,
                        action: 1,
                        browseDuration: 0));
                _jumpSimulatedExercises(logic);
                // widget.timingRepository.openTimingMiniProgram();
                // JumpUtils.get().jumpFolder(JkKey.SUBONE, "error");
              }),
          meIconButton2(
              icon: "subt_my_study_tab",
              text: "我的学时",
              color: Color(0xff555555),
              fontSize: 13.sp,
              onTap: () {
                if (!MainController.isLoginIntercept()) {
                  return;
                }
                BuryingPointUtils.instance.addPoint(
                    buryingPointList: BuryingPointList(
                        eventType: 3,
                        entranceType: 15,
                        action: 1,
                        browseDuration: 0));
                JumpTimings.get().jumpPeriod(2);
                // widget.timingRepository.openTimingMiniProgram();
                // JumpUtils.get().jumpFolder(JkKey.SUBONE, "error");
              }),
          meIconButton2(
              icon: "subt_3d_tab",
              text: "3D模拟",
              color: Color(0xff555555),
              fontSize: 13.sp,
              onTap: () {
                if (!MainController.isLoginIntercept()) {
                  return;
                }
                BuryingPointUtils.instance.addPoint(
                    buryingPointList: BuryingPointList(
                        eventType: 3,
                        entranceType: 16,
                        action: 1,
                        browseDuration: 0));
                _jumpSimulatedExercises(logic);
                // widget.timingRepository.openTimingMiniProgram();
                // JumpUtils.get().jumpFolder(JkKey.SUBONE, "error");
              }),
          meIconButton2(
              icon: "subt_video_jj_tab",
              text: "视频讲解",
              color: Color(0xff555555),
              fontSize: 13.sp,
              onTap: () {
                if (!MainController.isLoginIntercept()) {
                  return;
                }
                BuryingPointUtils.instance.addPoint(
                    buryingPointList: BuryingPointList(
                        eventType: 3,
                        entranceType: 17,
                        action: 1,
                        browseDuration: 0));
                gotoSubjectTwoPracticalVideoDetail(
                    theoryVideoListDM: logic.theoryVideoListDm,
                    index: 0,
                    vid: jsonDecode(logic.theoryVideoListDm!.listDM[0].address)[
                        'Data']['vid']);
                // widget.timingRepository.openTimingMiniProgram();
                // JumpUtils.get().jumpFolder(JkKey.SUBONE, "error");
              }),
        ],
      ).paddingOnly(bottom: 16.h),
    );
  }

  //去看考场
  Widget _buildExaminationRoom(SectionTwoController logic) {
    return sliverPaddingToBoxAdapter(
      padding: EdgeInsets.symmetric(horizontal: 11.w),
      child: Container(
        // height: 184.33.h,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(10.r),
          //     image: DecorationImage(
          //   fit: BoxFit.fill,
          //   image: assetImg2(img: 'bg_keer_kaochang'),
          // )
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                height: 43.h,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xff36A1F5), Color(0xff99EAFF)],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10.r),
                      topRight: Radius.circular(10.r)),
                ),
                child: Row(
                  children: [
                    logic.userAccount?.platSchoolName == null
                        ? Expanded(
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: GestureDetector(
                                  onTap: () {
                                    if (!MainController.isLoginIntercept()) {
                                      return;
                                    }
                                    Get.toNamed(AppRoutes.homeStudyTypeSelect);
                                  },
                                  child: app16spFFFText('去绑定驾校',
                                      fontFamily: 'PingFangSC-Semibold')
                                  // assImg2(img: 'subt_goto_bing_jiaxiao', w: 94.w,h: 17.h),
                                  ),
                            ),
                          )
                        : Expanded(
                            child: app16spAC5000Text(
                                    logic.userAccount?.platSchoolName,
                                    color: AppColors.white,
                                    fontFamily: 'PingFangSC-Semibold',
                                    overflow: TextOverflow.ellipsis)
                                .paddingOnly(left: 16.w),
                          ),
                    SizedBox(width: 16.w),
                    GestureDetector(
                      onTap: () {
                        if (!MainController.isLoginIntercept()) {
                          return;
                        }
                        switchExaminationRoomCallback.call(
                            logic.cityMap, logic.examinationRoomRouteListMap);
                      },
                      child: assImg2(img: 'subt_qiehuankaoc', w: 86.w, h: 25.h)
                          .paddingOnly(right: 14.w),
                    ),
                  ],
                ).paddingOnly(left: 11.w)),
            SizedBox(height: 9.h),
            Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: ClipRRect(
                      clipBehavior: Clip.antiAlias,
                      borderRadius: BorderRadius.circular(8.r),
                      child: Image.asset(
                          "assets/home_img/img_kaochang_kerer.gif",
                          width: 173.w,
                          height: 109.h,
                          fit: BoxFit.cover)),
                ).paddingOnly(left: 16.w),
                Expanded(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        app14sp268Text('提前看考场熟悉点位,上考场才不慌',
                                color: AppColors.C333333,
                                fontFamily: 'PingFangSC-Semibold')
                            .paddingOnly(bottom: 17.h),
                        assImg2(img: 'subt_kaostongguol', w: 138.w, h: 22.h),
                      ]).paddingSymmetric(horizontal: 10.h),
                ),
                // gotoSeeExaminationRoomBtn(
                //   onTap: () {
                //     if (!MainController.isLoginIntercept()) {
                //       return;
                //     }
                //     switchExaminationRoomCallback.call(
                //         logic.cityMap, logic.examinationRoomRouteListMap);
                //   },
                // ).marginOnly(
                //   left: 16.w,
                //   top: 40.h,
                // ),
              ],
            ).paddingOnly(bottom: 13.h),
          ],
        ),
      ),
    );
  }

  //学时详情
  Widget _buildStudyHouse(Map subjectTwoTimeTable) {
    return sliverPaddingToBoxAdapter(
        padding: EdgeInsets.all(10.h),
        child: Stack(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Expanded(
                    child: white8radiusContainer(
                  height: 178.h,
                  boxShadow: [
                    BoxShadow(
                        color: AppColors.C000000,
                        offset: Offset(-1.0.h, 3.0.h), //阴影y轴偏移量
                        blurRadius: 2.h, //阴影模糊程度
                        spreadRadius: 2.r //阴影扩散程度
                        )
                  ],
                  child: Row(
                    children: [
                      Expanded(
                          flex: 4,
                          child: Column(
                            // mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              app16spA1Text('我的学时').paddingOnly(
                                  left: 16.w, top: 16.h, bottom: 10.h),
                              Expanded(
                                  child: Stack(
                                alignment: Alignment.center,
                                children: <Widget>[
                                  Positioned(
                                    top: 1.0.h,
                                    child: TurnBox(
                                      turns: 5 / 8,
                                      child: GradientCircularProgressIndicator(
                                          colors: const [
                                            AppColors.other_login_text,
                                            AppColors.other_login_text,
                                          ],
                                          radius: 58.0.r,
                                          stokeWidth: 5.0,
                                          strokeCapRound: false,
                                          backgroundColor: AppColors.E1F1FF,
                                          totalAngle: 1.5 * pi,
                                          value: 0.7),
                                    ),
                                  ),
                                  Positioned(
                                    top: 38.0.h,
                                    child: app20sp268FF7Text("70%",
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Positioned(
                                    bottom: 38.h,
                                    child: app12sp1A1AText(
                                      "监管有效",
                                    ),
                                  ),
                                ],
                              ))
                            ],
                          )),
                      Expanded(
                          flex: 6,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              app14sp268Text('学时历程 >')
                                  .paddingOnly(right: 26.w, top: 16.h),
                              // app12sp1A1AText('共需学习学时：284分钟').paddingOnly(right: 26.w, top: 28.h),
                              _buildStudyHouseItem('共需学习学时', '284', top: 28.h),
                              _buildStudyHouseItem('总培训时长', '284'),
                              _buildStudyHouseItem('监管有效时长', '283')
                            ],
                          ))
                    ],
                  ),
                )),
                GestureDetector(
                  onTap: () {},
                  child: Container(
                    height: 156.h,
                    // padding: EdgeInsets.only(right: 16.w),
                    width: 39.w,
                    decoration: BoxDecoration(
                        image:
                            DecorationImage(image: assetImg2(img: "bg_kefu"))),
                  ),
                ).paddingOnly(right: 8.w)
              ],
            ),
            Positioned(
              bottom: 45.h,
              right: 20.w,
              child: assImg2(img: 'Ip_xueshi_kefu', w: 44.w, h: 48.h),
            )
          ],
        ).paddingOnly(left: 10.w, top: 10.h));
  }

  //学时监管item
  Widget _buildStudyHouseItem(String title, String time, {double? top}) {
    return EasyRichText(
      "$title：$time分钟",
      // strutStyle: StrutStyle(fontSize: 12.sp),
      patternList: [
        EasyRichTextPattern(
            targetString: title,
            style: TextStyle(
                color: AppColors.privacy_dialog_titile_color, fontSize: 12.sp)),
        EasyRichTextPattern(
            targetString: time,
            style: TextStyle(
                color: top == null
                    ? AppColors.other_login_text
                    : AppColors.privacy_dialog_titile_color,
                fontSize: 14.sp)),
        EasyRichTextPattern(
            targetString: "分钟",
            style: TextStyle(
                color: top == null
                    ? AppColors.other_login_text
                    : AppColors.privacy_dialog_titile_color,
                fontSize: 8.sp))
      ],
    ).paddingOnly(top: top ?? 10.h, right: 26.w);
  }

  //科二模拟练习
  Widget _buildSimulatedExercises(SectionTwoController logic) {
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 11.w, vertical: 6.h),
      sliver: SliverToBoxAdapter(
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 11.w),
              // height: 190.33.h,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(10.r),
                //   image: DecorationImage(
                // fit: BoxFit.fill,
                // image: assetImg2(img: 'bg_keer_monilianxi'),
                //   )
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  app16spAC5000Text('模拟练习',
                          color: AppColors.C333333,
                          fontFamily: 'PingFangSC-Semibold')
                      .paddingOnly(top: 11.h, bottom: 15.h),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      assImg2(img: 'img_3d', w: 170.w, h: 100.h),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            app16spA1Text('3D模拟 在线练车',
                                fontFamily: 'PingFangSC-Medium'),
                            SizedBox(height: 11.h),
                            app14spA1Text('避坑不紧张',
                                    color: Color(0xff868686),
                                    fontFamily: 'PingFangSC-Regular')
                                .paddingOnly(bottom: 30.h),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                logic.isShowCount
                                    ? Row(
                                        children: [
                                          Container(
                                            height: 18.h,
                                            width: 18.h,
                                            decoration: BoxDecoration(
                                                image: DecorationImage(
                                                    image: assetImg2(
                                                        img: 'mian_bg'))
                                                // borderRadius:
                                                // BorderRadius.circular(9.r),
                                                // gradient: const LinearGradient(
                                                //     colors: [
                                                //       Color(0xFFFFCB63),
                                                //       Color(0xFFFF5F0C)
                                                //     ],
                                                //     begin: Alignment.topCenter,
                                                //     end: Alignment.bottomCenter),
                                                ),
                                            // child: Text("免",
                                            //     style: TextStyle(
                                            //         fontSize: 12.sp,
                                            //         fontWeight: FontWeight.bold,
                                            //         color: Colors.white)),
                                          ),
                                          const SizedBox(
                                            width: 3,
                                          ),
                                          Text(
                                            '${logic.countDownMinutes}:${logic.countDownSeconds}',
                                            style: TextStyle(
                                                fontWeight: FontWeight.w500,
                                                color: const Color(0xFFE6973D),
                                                fontSize: 14.sp),
                                          ),
                                          // Text(
                                          //   '25:23',style: TextStyle(
                                          //     fontWeight: FontWeight.w500,
                                          //     color: const Color(0xFFE6973D),fontSize: 14.sp
                                          // ),),
                                        ],
                                      )
                                    : const SizedBox(),
                                SizedBox(width: 3.w),
                                GestureDetector(
                                  onTap: () {
                                    if (!MainController.isLoginIntercept()) {
                                      return;
                                    }
                                    _jumpSimulatedExercises(logic);
                                  },
                                  child: Container(
                                    alignment: Alignment.topRight,
                                    height: 25.h,
                                    width: 86.w,
                                    decoration: BoxDecoration(
                                      image: DecorationImage(
                                        image: assetImg2(img: 'subt_lijistudy'),
                                        fit: BoxFit.fill,
                                      ),
                                    ),
                                    // child: Center(
                                    //   child: GestureDetector(
                                    //     onTap: () {
                                    //
                                    //     },
                                    //     child: SizedBox(),
                                    //   ),
                                    // ),
                                  ),
                                ),
                              ],
                            )
                          ],
                        ).paddingOnly(left: 11.w),
                      )
                    ],
                  ).paddingOnly(top: 1.h, bottom: 8.h),
                ],
              ),
            ),
            Positioned(
              top: 0.h,
              right: 0.w,
              child: assImg2(img: 'subt_kaoshiyure', w: 76.w, h: 29.h),
            )
          ],
        ),
      ),
    );
  }

  _jumpSimulatedExercises(SectionTwoController logic) async {
    if (userRepository.vipProductMap.vip2) {
      showDialog(
        context: Get.context!,
        barrierDismissible: false, // 点击空白区域不可关闭
        builder: (BuildContext context) {
          return Dialog(
            child: U3dConfirmDialog(
              content: '即将打开3D练车',
              cancelText: '取消',
              confirmText: '确认',
              onCancel: () {
                Get.back();
              },
              onConfirm: () {
                Get.back();
                IPlatform.get().jumpLightSimulation('subject2', '');
              },
            ),
          );
        },
      );
    } else {
      if (logic.isShowCount) {
        showDialog(
          context: Get.context!,
          barrierDismissible: false, // 点击空白区域不可关闭
          builder: (BuildContext context) {
            return Dialog(
              child: U3dConfirmDialog(
                content: '即将打开3D练车',
                cancelText: '取消',
                confirmText: '确认',
                onCancel: () {
                  Get.back();
                },
                onConfirm: () {
                  Get.back();
                  IPlatform.get().jumpLightSimulation('subject2', '');
                },
              ),
            );
          },
        );
        return;
      }
      //打开科二vip购买小程序
      // debugPrint("跳转到vip购买页面${userAccount?.reqisterDivision}---${stringToIntConverterProvince(userAccount?.reqisterDivision.toString())}");
      // debugPrint("跳转到vip购买页面${userAccount?.reqisterDivision}---${cityMap[userAccount?.reqisterDivision.toString().substring(0,4)]}");
      int province =
          stringToIntConverterProvince(homeRepository.cityInfo?.cityCode);
      String cityName =
          cityMap[homeRepository.cityInfo?.cityCode.substring(0, 4)];
      final shopPath =
          '/pages/vip/part2/part2?province=$province&city=${homeRepository.cityInfo?.cityCode}&cityname=$cityName&type=3dlianche';
      JumpSmallProgramUtils.jump(shopPath, "fc2259710004813765");
      // Mop.instance.openApplet('fc2259710004813765', path: shopPath);
    }
  }

  //考试项目讲解
  Widget _buildExplanationProject(SectionTwoController logic) {
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 11.h, vertical: 5.h),
      sliver: SliverToBoxAdapter(
          child: logic.theoryVideoListDm == null
              ? const SizedBox()
              : white8radiusContainer(
                  height: 190.h,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      logic.theoryVideoListDm == null
                          ? const SizedBox()
                          : app16spA1Text('考试项目讲解',
                                  fontFamily: 'PingFangSC-Semibold')
                              .paddingOnly(top: 13.h, bottom: 16.h, left: 16.w),
                      Expanded(
                        child: ListView.builder(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          scrollDirection: Axis.horizontal,
                          // gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          //   crossAxisCount: 2,
                          //   childAspectRatio: 152.w / 135.h,
                          // ),
                          itemCount: logic.theoryVideoListDm == null
                              ? 0
                              : logic.theoryVideoListDm!.listDM.length,
                          itemBuilder: (BuildContext context, int index) {
                            return ListItem(logic,
                                    img: logic.theoryVideoListDm!.listDM[index]
                                        .cover[0],
                                    title: logic
                                        .theoryVideoListDm!.listDM[index].title,
                                    practicalVideoId: logic.theoryVideoListDm!
                                        .listDM[index].address,
                                    index: index)
                                .paddingSymmetric(horizontal: 8.w);
                          },
                        ),
                      ),
                    ],
                  ),
                ).paddingOnly(left: 3.w, top: 0.h)),
    );
  }

  Widget ListItem(SectionTwoController logic,
      {required img, required int index, title, practicalVideoId}) {
    return GestureDetector(
      onTap: () {
        // Toast.show("点击了$index --$title");
        gotoSubjectTwoPracticalVideoDetail(
            theoryVideoListDM: logic.theoryVideoListDm,
            index: index,
            vid: jsonDecode(practicalVideoId)['Data']['vid']);
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
              clipBehavior: Clip.antiAlias,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.r),
                  topRight: Radius.circular(8.r),
                  bottomLeft: Radius.circular(8.r),
                  bottomRight: Radius.circular(8.r)),
              child: networkImg(img: img, w: 152.w, h: 105.h, fit: BoxFit.fill)
              // assImg2(img: img,w: 152.w,h: 105.h,fit: BoxFit.fill)
              ),
          app14spA1Text(title, fontFamily: 'PingFangSC-Regular')
              .paddingOnly(top: 6.h)
        ],
      ),
    );
  }
}

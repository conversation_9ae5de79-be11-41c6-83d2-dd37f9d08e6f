import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/src/home/<USER>/common_view.dart';
import 'package:home/src/home/<USER>/sub_three_item_page/subject_three_catalog_controller.dart';
import 'package:login/login.dart';
import 'package:api/api.dart';

class SubjectThreeCatalogPage extends StatelessWidget {
  SubjectThreeCatalogPage({super.key});
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        init: SubjectThreeCatalogController(),
        builder: (logic) {
          return SafeArea(
              child: Scaffold(
                  appBar: baseAppBar2(
                    cityName: logic.homeRepository.cityInfo.cityName,
                    title: '科三考场',
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    onSelect: () {
                      logic.goSelectCity();
                    },
                  ),
                  body: logic.isNeedLoadingData && logic.isLoading
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: [
                                    SizedBox(width: 16.w),
                                    _tabBarItem(logic, 0, '官方', '带你看考场'),
                                    SizedBox(width: 16.w),
                                    _tabBarItem(logic, 1, '真实', '考场模拟'),
                                    SizedBox(width: 16.w),
                                    _tabBarItem(logic, 2, '考场', '考场点位'),
                                    SizedBox(width: 16.w),
                                    _tabBarItem(logic, 3, '本市', '考场视频'),
                                  ],
                                ),
                              ),
                              SizedBox(height: 22.h),
                              Expanded(
                                child: _getView(logic.selectedIndex, logic),
                              )
                            ])));
        });
  }

  Widget _getView(
    int index,
    SubjectThreeCatalogController logic,
  ) {
    if (index == 0) {
      return _showExaminationRoom(logic);
    } else if (index == 1) {
      return _examinationRoadRoomWidget(logic);
    } else if (index == 2) {
      return _pointMapWidget(logic);
    } else if (index == 3) {
      return _examinationRoomWidget(logic);
    } else {
      return Container();
    }
  }

  Widget _tabBarItem(SubjectThreeCatalogController logic, int type,
      String title, String text) {
    return Stack(
      children: [
        InkWell(
          onTap: () {
            logic.changeIndex(type);
          },
          child: Container(
            width: 83.w,
            margin: REdgeInsets.only(top: 16.h),
            padding: REdgeInsets.symmetric(vertical: 5, horizontal: 5),
            decoration: BoxDecoration(
              color: logic.selectedIndex == type
                  ? const Color(0xFFD4ECFF)
                  : AppColors.F6F5F5,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Text(title,
                //     style: TextStyle(
                //         fontSize: 14.sp,
                //         color: logic.selectedIndex == type
                //             ? const Color(0xFF268FF7)
                //             : const Color(0xFF4E4E4E))),
                Text(text,
                    style: TextStyle(
                        fontSize: 14.sp,
                        color: logic.selectedIndex == type
                            ? const Color(0xFF268FF7)
                            : const Color(0xFF4E4E4E))),
              ],
            ),
          ),
        ),
        type == 2
            ? Positioned(
                left: 32.w,
                top: 0.h,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(colors: [
                      Color(0xFFFF715B),
                      Color(0xFFFE2E39),
                    ], begin: Alignment.topLeft, end: Alignment.bottomRight),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(9.r),
                        topRight: Radius.circular(9.r),
                        bottomLeft: Radius.circular(0.r),
                        bottomRight: Radius.circular(9.r)),
                  ),
                  padding:
                      REdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                  child: Text('图解点位',
                      style: TextStyle(
                        fontSize: 8.sp,
                        color: Colors.white,
                      )),
                ),
              )
            : const SizedBox()
      ],
    );
  }

  //带你看考场
  Widget _showExaminationRoom(SubjectThreeCatalogController logic) {
    return logic.bringVIewExaminationModel == null
        ? (logic.isShowLoading
            ? const SizedBox()
            : Column(
                children: [
                  assImg2(
                          img: 'img_quesheng_meiyoushuju',
                          w: 230.w,
                          h: 178.h,
                          fit: BoxFit.fill)
                      .paddingSymmetric(vertical: 60.h),
                  Center(
                    child: app16spA1Text('您定位的城市，考场视频还在上架中，暂不支持购买',
                        textAlign: TextAlign.center),
                  ).paddingSymmetric(horizontal: 75.w),
                ],
              ))
        : Container(
            padding: REdgeInsets.symmetric(horizontal: 16.w),
            child: ListView.builder(
                itemCount: logic.bringVIewExaminationModel?.list?.length ?? 0,
                itemBuilder: (context, index) {
                  BringViewListElement? item =
                      logic.bringVIewExaminationModel?.list?[index];
                  return Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 8),
                        child: Row(
                          children: [
                            Text(
                                '${cityMap[item?.city.toString().substring(0, 4)] ?? '未知城市'}',
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    fontFamily: 'PingFangSC-Bold',
                                    fontWeight: FontWeight.bold,
                                    color:
                                        AppColors.privacy_dialog_titile_color)),
                            SizedBox(width: 10.w),
                            index == 0
                                ? Text("根据您的位置和考场通过率推荐",
                                    style: TextStyle(
                                        fontSize: 12.sp,
                                        fontFamily: 'PingFangSC-Regular',
                                        color: Color(0xFF8A8A8A)))
                                : const SizedBox(),
                          ],
                        ),
                      ),
                      SizedBox(height: 18.h),
                      Container(
                        width: double.infinity,
                        padding: REdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.F9F8F8,
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(item?.title ?? '',
                                style: TextStyle(
                                    fontSize: 16.sp,
                                    fontFamily: 'PingFangSC-Medium',
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF3D3D3D))),
                            SizedBox(
                              height: 6.h,
                            ),
                            Wrap(
                                spacing: 13.w,
                                children: List.generate(
                                    item?.label?.length ?? 0, (sIndex) {
                                  return Container(
                                    padding: REdgeInsets.symmetric(
                                        vertical: 3, horizontal: 7),
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                            color: Color(0xFF2BA9F4), width: 1),
                                        borderRadius:
                                            BorderRadius.circular(10.r)),
                                    child: Text(item?.label?[sIndex] ?? "",
                                        style: TextStyle(
                                            fontSize: 11.sp,
                                            fontFamily: 'PingFangSC-Regular',
                                            color: Color(0xFF2BA9F4))),
                                  );
                                })),
                            SizedBox(
                              height: 14.h,
                            ),
                            SizedBox(
                              height: 114.h,
                              child: ListView.builder(
                                  itemCount: item?.examCatalogInfo?.length ?? 0,
                                  scrollDirection: Axis.horizontal,
                                  itemBuilder: (context, tIndex) {
                                    return Container(
                                      margin: REdgeInsets.only(right: 12),
                                      width: 130.w,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          InkWell(
                                            onTap: () {
                                              logic.goViewExaminationPage(item?.productId, item, tIndex);
                                            },
                                            child: Container(
                                              height: 80.h,
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10.r),
                                                image: DecorationImage(
                                                    image: NetworkImage(item
                                                            ?.examCatalogInfo?[
                                                                tIndex]
                                                            .cover ??
                                                        ""),
                                                    fit: BoxFit.cover),
                                              ),
                                              child: Image.asset(
                                                'assets/home_img/grey_bofang.png',
                                                width: 30.w,
                                                height: 30.h,
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 12.h),
                                          Text(
                                              item?.examCatalogInfo?[tIndex]
                                                      .videoName ??
                                                  "",
                                              style: TextStyle(
                                                  fontSize: 14.sp,
                                                  fontFamily:
                                                      'PingFangSC-Regular',
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  color: Color(0xFF3D3D3D)))
                                        ],
                                      ),
                                    );
                                  }),
                            )
                          ],
                        ),
                      )
                    ],
                  );
                }),
          );
  }

  ///真实路考
  Widget _examinationRoadRoomWidget(SubjectThreeCatalogController logic) {
    return logic.cityRoadMaps.isEmpty
        ? Column(
            children: [
              assImg2(
                      img: 'img_quesheng_meiyoushuju',
                      w: 230.w,
                      h: 178.h,
                      fit: BoxFit.fill)
                  .paddingSymmetric(vertical: 60.h),
              Center(
                child: app16spA1Text('您定位的城市，真实路考线路还在上架中，暂不支持购买',
                    textAlign: TextAlign.center),
              ).paddingSymmetric(horizontal: 75.w),
            ],
          )
        : Container(
            padding: REdgeInsets.symmetric(horizontal: 16.w),
            child: ListView.builder(
                itemCount: logic.cityRoadMaps.length,
                itemBuilder: (context, index) {
                  int key = logic.exmRoadListData.keys.elementAt(index);
                  List<ExaminationRoomRouteListElementDm> value =
                      logic.exmRoadListData[key]!;
                  return Column(
                    children: [
                      Row(
                        children: [
                          Text(logic.cityRoadMaps.elementAt(index)['cityName'],
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      AppColors.privacy_dialog_titile_color)),
                          SizedBox(width: 10.w),
                          index == 0
                              ? Text("根据您的位置推荐",
                                  style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors
                                          .privacy_dialog_titile_color))
                              : const SizedBox(),
                        ],
                      ),
                      SizedBox(height: 18.h),
                      GridView.count(
                        crossAxisCount: 2,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        children: List.generate(value.length, (sIndex) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                onTap: () {
                                  logic.goRoad(logic.exmRoadListData[key],
                                      value[sIndex], sIndex);
                                },
                                child: Container(
                                  width: 167.w,
                                  height: 121.h,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8.r),
                                    image: DecorationImage(
                                        image:
                                            NetworkImage(value[sIndex].cover),
                                        fit: BoxFit.cover),
                                  ),
                                ),
                              ),
                              SizedBox(height: 12.h),
                              Row(
                                children: [
                                  Flexible(
                                    child: Text(value[sIndex].title ?? "",
                                        style: TextStyle(
                                            fontSize: 12.sp,
                                            overflow: TextOverflow.ellipsis,
                                            color: AppColors
                                                .privacy_dialog_titile_color)),
                                  ),
                                ],
                              ), // 路线名称
                            ],
                          );
                        }),
                      )
                    ],
                  );
                }),
          );
  }

  ///考场地图
  Widget _examinationRoomWidget(SubjectThreeCatalogController logic) {
    return logic.cityMaps.isEmpty
        ? Column(
            children: [
              assImg2(
                      img: 'img_quesheng_meiyoushuju',
                      w: 230.w,
                      h: 178.h,
                      fit: BoxFit.fill)
                  .paddingSymmetric(vertical: 60.h),
              Center(
                child: app16spA1Text('您定位的城市，考场视频还在上架中，暂不支持购买',
                    textAlign: TextAlign.center),
              ).paddingSymmetric(horizontal: 75.w),
            ],
          )
        : Container(
            padding: REdgeInsets.symmetric(horizontal: 16.w),
            child: ListView.builder(
                itemCount: logic.cityMaps.length,
                itemBuilder: (context, index) {
                  int key = logic.exmListData.keys.elementAt(index);
                  List<ExaminationRoomRouteListElementDm> value =
                      logic.exmListData[key]!;
                  return Column(
                    children: [
                      Row(
                        children: [
                          Text(logic.cityMaps.elementAt(index)['cityName'],
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      AppColors.privacy_dialog_titile_color)),
                          SizedBox(width: 10.w),
                          index == 0
                              ? Text("根据您的位置推荐",
                                  style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors
                                          .privacy_dialog_titile_color))
                              : const SizedBox(),
                        ],
                      ),
                      SizedBox(height: 18.h),
                      GridView.count(
                        crossAxisCount: 2,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        children: List.generate(value.length, (sIndex) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                onTap: () {
                                  logic.goExaminationRoom(
                                      logic.exmListData[key],
                                      value[sIndex],
                                      sIndex);
                                },
                                child: Container(
                                  width: 167.w,
                                  height: 121.h,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8.r),
                                    image: DecorationImage(
                                        image: NetworkImage(
                                            value[sIndex].cover ?? ""),
                                        fit: BoxFit.cover),
                                  ),
                                ),
                              ),
                              SizedBox(height: 12.h),
                              Row(
                                children: [
                                  Flexible(
                                    child: Text(value[sIndex].title ?? "",
                                        style: TextStyle(
                                            fontSize: 12.sp,
                                            overflow: TextOverflow.ellipsis,
                                            color: AppColors
                                                .privacy_dialog_titile_color)),
                                  ),
                                ],
                              ), // 路线名称
                            ],
                          );
                        }),
                      )
                    ],
                  );
                }),
          );
  }

  //点位图
  Widget _pointMapWidget(SubjectThreeCatalogController logic) {
    return (logic.threeSubjectsPoint?.list ?? []).isEmpty
        ? Column(
            children: [
              assImg2(
                      img: 'img_quesheng_meiyoushuju',
                      w: 230.w,
                      h: 178.h,
                      fit: BoxFit.fill)
                  .paddingSymmetric(vertical: 60.h),
              Center(
                child: app16spA1Text('您定位的城市，考场点位图还在上架中，暂不支持购买',
                    textAlign: TextAlign.center),
              ).paddingSymmetric(horizontal: 75.w),
            ],
          )
        : Container(
            padding: REdgeInsets.symmetric(horizontal: 16.w),
            child: ListView.builder(
                itemCount: logic.threeSubjectsPoint?.list?.length ?? 0,
                itemBuilder: (cotext, index) {
                  ThreeSubjectsPointItem data =
                      logic.threeSubjectsPoint?.list![index] ??
                          ThreeSubjectsPointItem();
                  return Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(data.title ?? "",
                                style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.bold,
                                    overflow: TextOverflow.ellipsis,
                                    color:
                                        AppColors.privacy_dialog_titile_color)),
                          ),
                          SizedBox(width: 10.w),
                          index == 0
                              ? Text("根据您的位置推荐",
                                  style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors
                                          .privacy_dialog_titile_color))
                              : const SizedBox(),
                        ],
                      ),
                      SizedBox(height: 18.h),
                      GridView.count(
                        crossAxisCount: 2,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        children: List.generate(data.routMapRoutes?.length ?? 0,
                            (sIndex) {
                          bool isBuy = logic
                              .getIsBuy(data.routMapRoutes![sIndex].productId!);
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                onTap: () {
                                  if (isBuy) {
                                    Get.toNamed(AppRoutes.detailPointMapPage,
                                        arguments: {
                                          "selectedIndex": index,
                                          'selectedSIndex': sIndex
                                        });
                                  } else {
                                    int province = stringToIntConverterProvince(
                                        logic.homeRepository.cityInfo.cityCode);
                                    String cityName = cityMap[logic
                                        .homeRepository.cityInfo.cityCode
                                        .substring(0, 4)];
                                    JumpSmallProgramUtils.jump(
                                        "/pages/vip/part3/part3?province=$province&city=${logic.homeRepository.cityInfo?.cityCode}&cityname=$cityName&type=kaochangditu&goods=vip3map&pi=${data.routMapRoutes?[sIndex].productId}&mi=${data.id}",
                                        "fc2259710004813765");
                                  }
                                },
                                child: Stack(
                                  children: [
                                    Container(
                                      width: 167.w,
                                      height: 121.h,
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(8.r),
                                        image: isBuy
                                            ? DecorationImage(
                                                image: networkImg2(
                                                    img: data
                                                            .routMapRoutes?[
                                                                sIndex]
                                                            .cover ??
                                                        ""),
                                                fit: BoxFit.cover)
                                            : DecorationImage(
                                                image: assetImg2(
                                                  img: 'point_defuat',
                                                ),
                                              ),
                                      ),
                                    ),
                                    Positioned(
                                        child: isBuy
                                            ? const SizedBox()
                                            : Stack(
                                                alignment: Alignment.center,
                                                children: [
                                                  Container(
                                                    width: 167.w,
                                                    height: 121.h,
                                                    decoration: BoxDecoration(
                                                      color: AppColors.D8D8D8
                                                          .withOpacity(0.8),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8.r),
                                                    ),
                                                  ),
                                                  Positioned(
                                                      child: assImg2(
                                                          img:
                                                              'icon_xianlutu_weijiesuo',
                                                          w: 40.w,
                                                          h: 40.h))
                                                ],
                                              ))
                                  ],
                                ),
                              ),
                              SizedBox(height: 12.h),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Flexible(
                                    child: Text(
                                        data.routMapRoutes?[sIndex].title ?? "",
                                        style: TextStyle(
                                            fontSize: 12.sp,
                                            overflow: TextOverflow.ellipsis,
                                            color: AppColors
                                                .privacy_dialog_titile_color)),
                                  ),
                                  isBuy
                                      ? const SizedBox()
                                      : logic.isShowUnlock
                                          ? InkWell(
                                              onTap: () {
                                                logic.unlock(data
                                                        .routMapRoutes?[sIndex]
                                                        .productId ??
                                                    "");
                                              },
                                              child: Container(
                                                height: 18.h,
                                                width: 18.w,
                                                padding: REdgeInsets.symmetric(
                                                    horizontal: 10,
                                                    vertical: 1),
                                                margin:
                                                    REdgeInsets.only(right: 8),
                                                alignment: Alignment.center,
                                                decoration: BoxDecoration(
                                                    image: DecorationImage(
                                                        image: assetImg2(
                                                            img: 'mian_bg'))
                                                    // borderRadius:
                                                    //     BorderRadius.circular(
                                                    //         9.r),
                                                    // gradient:
                                                    //     const LinearGradient(
                                                    //         colors: [
                                                    //       Color(0xFFFFCB63),
                                                    //       Color(0xFFFF5F0C)
                                                    //     ],
                                                    //         begin: Alignment
                                                    //             .topCenter,
                                                    //         end: Alignment
                                                    //             .bottomCenter),
                                                    ),
                                                // child: Text("免",
                                                //     style: TextStyle(
                                                //         fontSize: 12.sp,
                                                //         fontWeight:
                                                //             FontWeight.bold,
                                                //         color: Colors.white)),
                                              ),
                                            )
                                          : const SizedBox(),
                                ],
                              ), // 路线名称
                            ],
                          );
                        }),
                      )
                    ],
                  );
                }),
          );
  }
}

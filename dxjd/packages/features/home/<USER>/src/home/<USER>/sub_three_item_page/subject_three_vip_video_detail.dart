import 'package:component_library/component_library.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/src/home/<USER>/vip_protocol_check.dart';
import 'package:home_repository/home_repository.dart';
import 'package:login/login.dart';

import '../../../../home.dart';
import '../../custom_widget/grallist.dart';

class SubjectThreeVipVideoDetailScreen extends StatefulWidget {
  const SubjectThreeVipVideoDetailScreen({super.key});

  @override
  State<SubjectThreeVipVideoDetailScreen> createState() =>
      _SubjectThreeVipVideoDetailScreenState();
}

class _SubjectThreeVipVideoDetailScreenState
    extends State<SubjectThreeVipVideoDetailScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isOpenVipCheck = false;
  bool _isVisible = false;
  int _currentItemIndex = 0;
  List<TestData> testData = [];

  late HomeRepository homeRepository;
  @override
  void initState() {
    homeRepository = Get.find<MainController>().homeRepository;
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= 50.h) {
        double offset = (_scrollController.position.pixels - 50.h) / 100;
        if (offset <= 1) {
          setState(() {
            _isVisible = true;
          });
        }
      } else {
        setState(() {
          _isVisible = false;
        });
      }
    });
    super.initState();
    _getUsersProblemList();
  }

  _getUsersProblemList() async {
    for (int i = 7; i < 10; i++) {
      List<TestDataChild> userProblemList = [];
      for (int j = 0; j < 3; j++) {
        userProblemList.add(TestDataChild(
            img: "img$j",
            name: "驾考用户$j",
            content:
                "该路线可以考手动挡和自动挡的车，视频中的操作以手动挡车型为例，自动挡的车型在操作上无需踩离合、无需手动减档，其他考试规则与手动挡基本一致。$j"));
      }
      testData.add(TestData(
          title: "考试和视频路线一样吗$i",
          currentCount: 0,
          count: i,
          userProblemList: userProblemList));
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark),
        leading: IconButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          icon: assImg2(img: 'nav_icon_back_white', w: 24.w, h: 24.h),
        ),
        backgroundColor: Colors.transparent,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Stack(
                children: [
                  // assImg2(img: 'bg_kesan_vip', fit: BoxFit.fitWidth),
                  Positioned(
                    top: 546.h + 30.h,
                    left: 40.w,
                    child: GestureDetector(
                        onTap: () {
                          if (_isOpenVipCheck) {
                            Toast.show("Coming soon");
                          } else {
                            showPrivacyDialog(context).then((value) {
                              if (value == true) {
                                setState(() {
                                  if (!_isOpenVipCheck) {
                                    _isOpenVipCheck = true;
                                  }
                                });
                              }
                            });
                          }
                        },
                        child: assImg2(img: 'btn_kesan_vip', h: 68.h)),
                  ),
                  Positioned(
                    top: 562.h + 20.h,
                    left: 56.w,
                    child: app36spFFFFFText(
                      '68元',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Positioned(
                    top: 923.h + 20.h,
                    left: 42.w,
                    child: assImg2(img: 'img_wu', h: 145.h),
                  ),
                  Positioned(
                    top: 1465.h + 25.h,
                    left: 42.w,
                    child: assImg2(img: 'img_dengguang', h: 145.h),
                  ),

                  Positioned(
                    top: 1716.h + 35.h,
                    left: 32.w,
                    right: 32.w,
                    child: Container(
                      // height: 531.h,
                      constraints: BoxConstraints(maxHeight: 560.h),
                      width: 1.sw,
                      // color: Colors.cyan,
                      child: ListView.builder(
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        physics: BouncingScrollPhysics(),
                        itemBuilder: (_, index) {
                          return _buildUserProblemItem(testData[index])
                              .paddingSymmetric(vertical: 12.h);
                          // return Container();
                        },
                        itemCount: testData.length,
                      ),
                    ).paddingOnly(right: 16.w),
                  )

                  // Positioned(
                  //   // bottom: 0,
                  //     left: 0,
                  //     right: 0,
                  //     child:Container(
                  //       width: 1.sw,
                  //       height: 148.h,
                  //       color: Colors.pink,
                  //     )
                  // )
                ],
              ),
            ),
          ),
          Visibility(
            visible: _isVisible,
            child: Container(
              clipBehavior: Clip.antiAlias,
              height: 178.h,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.all(Radius.circular(8.r)),
                // borderRadius: BorderRadius.only(topLeft: Radius.circular(8.r),topRight: Radius.circular(8.r)),
              ),
              child: Stack(
                children: [
                  Column(
                    children: [
                      SizedBox(height: 20.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Container(
                              height: 41.h,
                              width: 164.w,
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      color: AppColors.C64B2FF, width: 1.dm),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(8.r))),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  app14sp268Text('全市线路'),
                                  app16sp268fText('￥45',
                                      fontWeight: FontWeight.bold)
                                ],
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {},
                            child: Container(
                              height: 41.h,
                              width: 164.w,
                              decoration: BoxDecoration(
                                  color: AppColors.other_login_text,
                                  // border: Border.all(color: AppColors.other_login_text,width: 1.dm),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(8.r))),
                              child: Stack(
                                children: [
                                  Positioned(
                                    right: 0,
                                    bottom: 0,
                                    child: assImg2(
                                        img: 'kemu_selected', h: 19.h, w: 25.w),
                                  ),
                                  Center(
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        app14spFFFFText('科三VIP'),
                                        app16spFFFText('￥68',
                                            fontWeight: FontWeight.bold)
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 16.0.w),
                        height: 50.h,
                        decoration: BoxDecoration(
                          // color: AppColors.privacy_dialog_titile_color,
                          image: DecorationImage(
                              image: assetImg2(img: 'btn_vip_jiage'),
                              fit: BoxFit.fill),
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.baseline,
                              textBaseline: TextBaseline.ideographic,
                              children: [
                                Text('￥ ',
                                    style: TextStyle(
                                        fontSize: 24.sp,
                                        color: AppColors.B5790E)),
                                Text('68',
                                        style: TextStyle(
                                            fontSize: 36.sp,
                                            color: AppColors.B5790E,
                                            fontWeight: FontWeight.bold))
                                    .paddingOnly(right: 8.w),
                                Text('/180天',
                                    style: TextStyle(
                                        fontSize: 12.sp,
                                        color: AppColors.B5790E,
                                        fontWeight: FontWeight.bold)),
                              ],
                            ).paddingOnly(left: 36.w),
                            GestureDetector(
                              onTap: () {
                                if (_isOpenVipCheck) {
                                  Toast.show('开通VIP');
                                } else {
                                  showPrivacyDialog(context).then((value) {
                                    if (value == true) {
                                      setState(() {
                                        if (!_isOpenVipCheck) {
                                          _isOpenVipCheck = true;
                                        }
                                      });
                                    }
                                  });
                                }
                              },
                              child: Container(
                                height: 50.h,
                                width: 141.w,
                                decoration: BoxDecoration(
                                  image: DecorationImage(
                                      image: assetImg2(img: 'btn_vip_goumai'),
                                      fit: BoxFit.fill),
                                ),
                                child: Center(child: app16spFFFText('立即开通')),
                              ),
                            )
                          ],
                        ),
                      ).paddingOnly(top: 16.h, bottom: 16.h),
                      VipProtocolCheckbox(
                          value: _isOpenVipCheck,
                          onChanged: (isChecked) {
                            setState(() {
                              _isOpenVipCheck = isChecked;
                            });
                          }).paddingOnly(left: 16.w)
                    ],
                  ),
                  Positioned(
                    top: 2.h,
                    right: 23.w,
                    child: Container(
                      height: 25.h,
                      width: 164.w,
                      decoration: BoxDecoration(
                          image: DecorationImage(
                              image: assetImg2(img: 'bg_kesan_youhui'))),
                      child: Center(child: app10spWhiteText('含本市考场、真实路考等5大权益')),
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserProblemItem(TestData testData) {
    return Container(
      child: Column(
        children: [
          Row(
            children: [
              Container(
                height: 20.h,
                width: 20.w,
                decoration: BoxDecoration(
                  color: AppColors.FFCB63,
                  borderRadius: BorderRadius.circular(50.r),
                ),
                child: Center(child: app14spFFFFText('Q')),
              ),
              app14spA1Text(testData.title).paddingOnly(left: 8.w)
            ],
          ).paddingOnly(bottom: 12.h),
          Container(
              // height: 140.h,
              constraints: BoxConstraints(maxHeight: 140.h),
              decoration: BoxDecoration(
                // color: AppColors.F1F8FF,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: questionListBanner2(testData.userProblemList,
                  onPageChanged: (index, total) {
                setState(() {
                  testData.currentCount = index;
                });
              })
              // ListView.builder(itemBuilder: (_, index){
              //   return _buildUserProblemItemChild(testData.userProblemList[index]);
              // },
              //   // itemExtent: 119.h,
              //   key: GlobalKey(),
              //   // controller: _scrollControllerI,
              //   // itemExtent: 343.w,
              //   shrinkWrap: true,
              //   itemCount: testData.userProblemList.length,
              //   scrollDirection: Axis.horizontal,),
              ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              app12spC4E4E4EText(
                  '第${testData.currentCount! + 1}/${testData.count}条回答'),
              app12spC4E4E4EText('<< 左滑查看更多').paddingOnly(right: 16.w)
            ],
          ).paddingOnly(top: 12.h)
        ],
      ),
    );
  }

  Widget _buildUserProblemItemChild(TestDataChild testDataChild) {
    return Container(
      // height: 119.h,
      width: 290.w,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppColors.F1F8FF,
        borderRadius: BorderRadius.circular(8.r),
      ),
      // margin: EdgeInsets.symmetric(vertical: 12.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                height: 30.h,
                width: 30.w,
                decoration: BoxDecoration(
                  color: AppColors.FFCB63,
                  borderRadius: BorderRadius.circular(50.r),
                ),
                child: Center(child: app10spWhiteText('头像')),
              ).paddingOnly(right: 16.w),
              app14spA1Text(testDataChild.name),
            ],
          ).paddingOnly(bottom: 12.h),
          Expanded(
              child: app12spC4E4E4EText(testDataChild.content,
                  textAlign: TextAlign.start)),
        ],
      ),
    ).paddingOnly(right: 16.w);
  }
}

import 'package:component_library/component_library.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

Widget app16spA1Text(data,{textAlign,overflow,fontWeight,fontFamily}) {
  return Text(
    data,textAlign: textAlign,overflow: overflow,
    style: TextStyle(
        fontSize: 16.sp,fontWeight: fontWeight, color: AppColors.privacy_dialog_titile_color,fontFamily: fontFamily),
  );
}

Widget app15spFF4D4DText(data,{textAlign,overflow,fontWeight}) {
  return Text(
    data,textAlign: textAlign,overflow: overflow,
    style: TextStyle(
        fontSize: 15.sp,fontWeight: fontWeight, color: AppColors.FF4D4D),
  );
}

Widget app17spA1Text(data,{textAlign,overflow}) {
  return Text(
    data,textAlign: textAlign,overflow: overflow,
    style: TextStyle(
        fontSize: 17.sp, color: AppColors.privacy_dialog_titile_color),
  );
}

Widget app16spFFFText(data,{fontWeight,fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 16.sp, color: AppColors.white,fontWeight: fontWeight,fontFamily: fontFamily),
  );
}

Widget app16sp4e4eText(data, {textAlign,fontFamily}) {
  return Text(
    data,textAlign: textAlign,
    style: TextStyle(fontSize: 16.sp, color: AppColors.login_textfiled_color,fontFamily: fontFamily),
  );
}

Widget app16spAAAText(data, {color,textAlign}) {
  return Text(
    data,textAlign: textAlign,
    style: TextStyle(fontSize: 16.sp, color: color ?? AppColors.AAAAAA),
  );
}


Widget app16spAC5000Text(data, {color,fontFamily,TextOverflow? overflow}) {
  return Text(
    data,
    style: TextStyle(fontSize: 16.sp, color: color ?? AppColors.AC5000,fontFamily: fontFamily,overflow: overflow),
  );
}

Widget app16sp268fText(data,{fontWeight,textAlign,fontFamily}){
  return Text(data,textAlign: textAlign,
    style: TextStyle(fontSize: 16.sp, color: AppColors.other_login_text,fontWeight: fontWeight,fontFamily: fontFamily));
}

Widget app15spE12020Text(data, {color,FontWeight? fontWeight,overflow}) {
  return Text(
    data,overflow: overflow,
    style: TextStyle(
        fontSize: 15.sp, color: color ?? AppColors.E12020,fontWeight: fontWeight),
  );
}

Widget app14spA1Text(data, {color,FontWeight? fontWeight,overflow,String? fontFamily}) {
  return Text(
    data,overflow: overflow,
    style: TextStyle(
        fontSize: 14.sp, color: color ?? AppColors.privacy_dialog_titile_color,fontWeight: fontWeight,fontFamily: fontFamily),
  );
}

Widget app14sp268Text(data, {color,FontWeight? fontWeight,overflow,textAlign,fontFamily,wordSpacing}) {
  return Text(
    data,overflow: overflow,textAlign: textAlign,
    style: TextStyle(
        fontSize: 14.sp, color: color ?? AppColors.other_login_text,fontWeight: fontWeight,fontFamily: fontFamily,wordSpacing: wordSpacing),
  );
}

Widget app14spFFFFText(data, {color,FontWeight? fontWeight,fontFamily}) {
  return Text(
    data,
    style: TextStyle(
        fontSize: 14.sp, color: color ?? AppColors.white,fontWeight: fontWeight,fontFamily: fontFamily),
  );
}

Widget app8spFFFFText(data, {color,FontWeight? fontWeight,fontFamily}) {
  return Text(
    data,
    style: TextStyle(
        fontSize: 8.sp, color: color ?? AppColors.white,fontWeight: fontWeight,fontFamily: fontFamily),
  );
}

Widget app14sp4e4eText(data, {color,fontFamily}) {
  return Text(
    data,
    style: TextStyle(
        fontSize: 14.sp, color: color ?? AppColors.login_textfiled_color,fontFamily: fontFamily),
  );
}

Widget app12sp268FF7Text(data, {color,maxLines,textAlign,fontFamily}) {
  return Text(
    data,
    maxLines: maxLines,textAlign: textAlign,
    style:
        TextStyle(fontSize: 12.sp, color: color ?? AppColors.other_login_text,fontFamily: fontFamily),
  );
}

Widget app12spAAAText(data, {color,textAlign,fontFamily,overflow,height}) {
  return Text(
    data,textAlign: textAlign,
    style: TextStyle(fontSize: 12.sp, color: color ?? AppColors.AAAAAA,fontFamily: fontFamily,overflow: overflow,height: height),
  );
}

Widget app11spAAAText(data, {color,textAlign,fontFamily,overflow}) {
  return Text(
    data,textAlign: textAlign,
    style: TextStyle(fontSize: 11.sp, color: color ?? AppColors.AAAAAA,fontFamily: fontFamily,overflow: overflow),
  );
}

Widget app12spC66666Text(data, {color,textAlign,fontFamily}) {
  return Text(
    data,textAlign: textAlign,
    style: TextStyle(fontSize: 12.sp, color: color ?? AppColors.C666666,fontFamily: fontFamily),
  );
}

Widget app12spFF4D4DText(data, {color,textAlign,fontFamily}) {
  return Text(
    data,textAlign: textAlign,
    style: TextStyle(fontSize: 12.sp, color: color ?? AppColors.FF4D4D,fontFamily: fontFamily),
  );
}

Widget app12spFF6D5EText(data, {color,textAlign=TextAlign.center}) {
  return Text(
    data,
    textAlign: textAlign,
    style: TextStyle(fontSize: 12.sp, color: color ?? AppColors.FF6D5E),
  );
}

Widget app13spC66666Text(data, {color,textAlign,fontFamily}) {
  return Text(
    data,textAlign: textAlign,
    style: TextStyle(fontSize: 13.sp, color: color ?? AppColors.C666666,fontFamily: fontFamily),
  );
}

Widget app12spC64B2FFText(data, {color,textAlign=TextAlign.center,fontFamily}) {
  return Text(
    data,
    textAlign: textAlign,
    style: TextStyle(fontSize: 12.sp, color: color ?? AppColors.C64B2FF,fontFamily: fontFamily),
  );
}

Widget app14spC64B2FFText(data, {color,textAlign=TextAlign.center,fontFamily}) {
  return Text(
    data,
    textAlign: textAlign,
    style: TextStyle(fontSize: 14.sp, color: color ?? AppColors.C64B2FF,fontFamily: fontFamily),
  );
}

Widget app12spC4E4E4EText(data, {color,textAlign=TextAlign.center}) {
  return Text(
    data,
    textAlign: textAlign,
    style: TextStyle(fontSize: 12.sp, color: color ?? AppColors.C4E4E4E),
  );
}

Widget app12spAC5000Text(data, {color,textAlign=TextAlign.center,fontFamily}) {
  return Text(
    data,
    textAlign: textAlign,
    style: TextStyle(fontSize: 12.sp, color: color ?? AppColors.AC5000,fontFamily: fontFamily),
  );
}

Widget app12spC9E500Text(data, {color,textAlign=TextAlign.center,fontFamily}) {
  return Text(
    data,
    textAlign: textAlign,
    style: TextStyle(fontSize: 12.sp, color: color ?? AppColors.C9E5500,fontFamily: fontFamily),
  );
}

Widget app12spC99999Text(data, {color,textAlign=TextAlign.center,fontFamily}) {
  return Text(
    data,
    textAlign: textAlign,
    style: TextStyle(fontSize: 12.sp, color: color ?? AppColors.C999999,fontFamily: fontFamily),
  );
}

Widget app12spFFFFFText(data, {color,textAlign=TextAlign.center,fontFamily}) {
  return Text(
    data,
    textAlign: textAlign,
    style: TextStyle(fontSize: 12.sp, color: color ?? AppColors.white,fontFamily: fontFamily),
  );
}

Widget app12sp1A1AText(data, {color,textAlign=TextAlign.center,fontFamily}) {
  return Text(
    data,
    textAlign: textAlign,
    style: TextStyle(fontSize: 12.sp, color: color ?? AppColors.privacy_dialog_titile_color,fontFamily: fontFamily),
  );
}

Widget app10sp268FF7Text(data, {color,textAlign,fontFamily}) {
  return Text(
    data,textAlign: textAlign,
    style: TextStyle(fontSize: 10.sp, color: color ?? AppColors.other_login_text,fontFamily: fontFamily),
  );
}

Widget app10spFF4D4DText(data, {color,textAlign,fontFamily}) {
  return Text(
    data,textAlign: textAlign,
    style: TextStyle(fontSize: 10.sp, color: color ?? AppColors.FF4D4D,fontFamily: fontFamily),
  );
}

Widget app10spAAAA7Text(data, {color,fontFamily,height}) {
  return Text(
    data,
    style: TextStyle(fontSize: 10.sp, color: color ?? AppColors.AAAAAA,fontFamily: fontFamily,height: height),
  );
}

Widget app10spD76798Text(data, {color,fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 10.sp, color: color ?? AppColors.D76798,fontFamily: fontFamily),
  );
}

Widget app10spWhiteText(data, {color}) {
  return Text(
    data,
    style: TextStyle(fontSize: 10.sp, color: color ?? AppColors.white),
  );
}

Widget app10spA86000Text(data, {color,fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 10.sp, color: color ?? AppColors.A86000,fontFamily: fontFamily),
  );
}

Widget app20sp268FF7Text(data, {color,fontWeight,fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 20.sp, color: color ?? AppColors.other_login_text,fontWeight: fontWeight,fontFamily: fontFamily),
  );
}

Widget app20sp1A1A1A7Text(data, {color,fontWeight,fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 20.sp,fontFamily: fontFamily,fontWeight: fontWeight, color: color ?? AppColors.privacy_dialog_titile_color,overflow: TextOverflow.ellipsis),
  );
}

Widget app20sp33333Text(data, {color,fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 20.sp, color: color ?? AppColors.C333333,fontFamily: fontFamily),
  );
}
Widget app20spD76798Text(data, {color,fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 20.sp, color: color ?? AppColors.D76798,fontFamily: fontFamily),
  );
}

Widget app14sp33333Text(data, {color, fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 14.sp, color: color ?? AppColors.C333333,fontFamily: fontFamily),
  );
}

Widget app24sp4e4eText(data, {color}) {
  return Text(
    data,
    style: TextStyle(fontSize: 24.sp, color: color ?? AppColors.privacy_text_default_color),
  );
}

Widget app24spFF5757Text(data, {color,fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 24.sp, color: color ?? AppColors.FF5757,fontFamily: fontFamily),
  );
}

Widget app14spFF5757Text(data, {color}) {
  return Text(
    data,
    style: TextStyle(fontSize: 14.sp, color: color ?? AppColors.FF5757),
  );
}

Widget app24sp3333Text(data, {color,fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 24.sp, color: color ?? AppColors.C333333,fontFamily: fontFamily),
  );
}

Widget app24spA86000Text(data, {color}) {
  return Text(
    data,
    style: TextStyle(fontSize: 24.sp, color: color ?? AppColors.A86000),
  );
}

Widget app36spFFFFFText(data, {color,fontWeight}) {
  return Text(
    data,
    style: TextStyle(fontSize: 36.sp,fontWeight: fontWeight, color: color ?? AppColors.white),
  );
}


Widget app18spFFFFText(data, {color,fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 18.sp, color: color ?? AppColors.white,fontFamily: fontFamily),
  );
}

Widget app17spFFFFText(data, {color,fontFamily}) {
  return Text(
    data,
    style: TextStyle(fontSize: 17.sp, color: color ?? AppColors.white,fontFamily: fontFamily),
  );
}



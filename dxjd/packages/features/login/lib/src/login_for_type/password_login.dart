import 'dart:io';

import 'package:ali_auth/ali_auth.dart';
import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/mainController.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:login/login.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:user_repository/user_repository.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'common/common_btn.dart';
import 'login_coustom_widget/auth_dialog.dart';
import 'login_coustom_widget/icon_btn.dart';
import 'login_coustom_widget/login_base_container.dart';
import 'login_coustom_widget/privacy_checkbox.dart';

class PassWordLogin extends StatefulWidget {
  const PassWordLogin({
    super.key,
  });

  @override
  State<PassWordLogin> createState() => _PassWordLoginState();
}

class _PassWordLoginState extends State<PassWordLogin> {
  late UserRepository userRepository;

  bool _isCheck = false;
  final TextEditingController _accountC = TextEditingController();
  final TextEditingController _passWordC = TextEditingController();

  _PassWordLoginState();

  @override
  void initState() {
    userRepository = Get.find<MainController>().userRepository;

    ///阿里云一键登录回调监听
    AliAuth.loginListen(onEvent: (event) {
      debugPrint("=============$event");
      _loginListener(event);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: loginContainer(
        context,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(top: 148.h, left: 32.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  app20sp268FF7Text(
                    "欢迎来到大象驾到",fontFamily: 'PingFangSC-Medium'
                  ),
                  loginTitleInd(),
                ],
              ),
            ),
            _buildAccountAndPassWord(),
            _buildPassWordLoginBtn(userRepository).paddingOnly(top: 60.h),
            const Spacer(),
            _buildPrivacyCheckBox(),
            SizedBox(
              height: 51.h,
            ),
            Expanded(child: _buildOtherLogin(context)),
            // )
          ],
        ),
      ),
    );
  }

  //用户名加密码输入textfiled
  Widget _buildAccountAndPassWord() {
    return Padding(
      padding: EdgeInsets.all(
        32.0.w,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          loginAccountTextField(controller: _accountC),
          Divider(height: 1.h, color: AppColors.E6F2FE),
          SizedBox(height: 20.0.h),
          loginAccountTextField(
              controller: _passWordC,
              maxLength: 20,
              obscureText: true,
              regex: false,
              hintText: '请输入密码',
              img: 'icon_denglu_yanzheng'),
          Divider(height: 1.h, color: AppColors.E6F2FE),
          Container(
              margin: EdgeInsets.only(top: 15.h, right: 17.w),
              alignment: Alignment.bottomRight,
              child: InkWell(
                child: app12sp268FF7Text("忘记密码",fontFamily: 'PingFangSC-Regular'),
                onTap: () {
                  Get.toNamed(AppRoutes.forgetPassword);
                  // Navigator.pushNamed(context, "forget_password");
                },
              ))
        ],
      ),
    );
  }

  //密码登陆btn
  Widget _buildPassWordLoginBtn(userRepository) {
    return GestureDetector(
        onTap: () {
          if (_isCheck) {
            if (_accountC.value.text.isEmpty || _accountC.value.text == "") {
              Toast.show("请输入正确账号！");
            } else {
              if (_passWordC.value.text.isEmpty ||
                  _passWordC.value.text.length < 6) {
                Toast.show("密码需6-10位！");
              } else {
                _startPasswordLogin(userRepository, _accountC.value.text,
                    _passWordC.value.text);
                // Toast.show("发起登陆请求！");
              }
            }
          } else {
            //弹窗
            // _showPerDialog(context);
            showPrivacyDialog(context).then((value) {
              if (value == true) {
                setState(() {
                  if (!_isCheck) {
                    _isCheck = true;
                  }
                });
              }
            });
          }
        }.throttleWithTimeout(timeout: 2000),
        child: h46c5AADr25Container(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Center(
              child: app16spFFFText("登录",fontFamily: 'PingFangSC-Medium'),
            )).marginSymmetric(horizontal: 24.w));
  }

  //隐私协议
  Widget _buildPrivacyCheckBox() {
    return Padding(
      padding: EdgeInsets.only(left: 24.w, right: 27.w,),
      child: PrivacyCheckbox(
        value: _isCheck,
        onChanged: (v) {
          setState(() {
            _isCheck = v;
          });
        },
        userAgreementOnClick: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => Scaffold(
                        appBar: baseAppBarForLogin(
                          title: '用户服务协议',
                          onPressed: () {
                            Navigator.pop(context);
                          },
                        ),
                        body: WebView(
                          initialUrl:
                              'https://dxjk.daxiangjd.com/agreement/?tag=user_agreement',
                          javascriptMode: JavascriptMode.unrestricted,
                        ),
                      )));
        },
        privacyPolicyOnClick: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => Scaffold(
                        appBar: baseAppBarForLogin(
                          title: '隐私协议',
                          onPressed: () {
                            Navigator.pop(context);
                          },
                        ),
                        body: WebView(
                          initialUrl:
                              'https://dxjk.daxiangjd.com/agreement/?tag=privacy_agreement',
                          onPageFinished: (String url) {
                            debugPrint('Page finished loading: $url');
                          },
                          onPageStarted: (String url) {
                            debugPrint('Page started loading: $url');
                          },
                          onWebViewCreated:
                              (WebViewController webViewController) {},
                          javascriptMode: JavascriptMode.unrestricted,
                        ),
                      )));
        },
      ),
    );
  }

  //其他登录选择
  Widget _buildOtherLogin(context) {
    List<Widget> list = [
      Platform.isIOS
          ? meIconButton(
              icon: "denglu_icon_apple.png",
              text: "苹果ID",
              isShowText: Platform.isAndroid,
              onTap: () async {
                if (_isCheck) {
                  await _signByApple();
                } else {
                  Toast.show('请同意隐私协议!');
                }
                debugPrint("苹果id登陆");
              })
          : Container(),
      meIconButton(
          icon: "denglu_icon_shouji.png",
          text: "手机号登陆",
          isShowText: Platform.isAndroid,
          onTap: () {
            Get.toNamed(AppRoutes.phoneLogin);
            // Navigator.pushNamedAndRemoveUntil(
            //     context, "phone_login", (r) => true);
            debugPrint("手机号登陆");
          }),
    ];
    if (MainController.showVxLogin()) {
      list.insert(
          0,
          meIconButton(
              icon: "denglu_icon_weixin.png",
              text: "微信登陆",
              isShowText: Platform.isAndroid,
              onTap: () {
                Get.toNamed(AppRoutes.wechatLogin);
                // gotoWechatLogin.call();
                // Navigator.pushNamedAndRemoveUntil(
                //     context, "wechat_login", (r) => true);
                debugPrint("微信登陆");
              }));
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: list,
    );
  }

  //获取苹果id及登录
  Future<void> _signByApple() async {
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );
    debugPrint("获取到的credential:$credential");
    loginByApple(
        name: (credential.familyName ?? '') + (credential.givenName ?? ''),
        email: credential.email ?? '',
        userIdentifier: credential.userIdentifier ?? '',
        identityToken: credential.identityToken ?? '',
        authorizationCode: credential.authorizationCode);
  }

  Future<void> loginByApple(
      {name, email, userIdentifier, identityToken, authorizationCode}) async {
    try {
      final userDM = await userRepository.loginByApple(
          name: name,
          email: email,
          userIdentifier: userIdentifier,
          identityToken: identityToken,
          authorizationCode: authorizationCode);
      if (userDM == null) {
      } else {
        if (userDM.bindMobile == null) {
          AliAuth.initSdk(getDefaultConfig());
        } else {
          if (userDM.topicType == null) {
            Get.toNamed(AppRoutes.homeStudyTypeSelect,
                arguments: {'isLoginEnter': true});
          } else {
            JumpPageUntil.jumpHomeAndCloseAll();
          }
        }
      }
    } catch (e) {
      if (e is DomainException) {
        Toast.show(e.message);
      } else {
        Toast.show(e.toString());
      }
    }
  }

  ///阿里云一键登录事件监听回调
  void _loginListener(event) {
    switch (event['code']) {
      case "600004":
      case "600005":
      case "600008":
      case "600009":
      case "600010":
        AliAuth.quitPage();
        Toast.show('${event['msg']},获取Token失败！');
        Get.toNamed(AppRoutes.phoneLogin, arguments: {"bindOrLogin": "bind"});

        break;
      case "600011":
        AliAuth.quitPage();
        Toast.show('${event['msg']},获取Token失败！');
        Get.toNamed(AppRoutes.phoneLogin, arguments: {"bindOrLogin": "bind"});
        break;
      case "600012":
      case "600013":
      case "600014":
      case "600015":
      case "600017":
      case "600021":
      case "600023":
      case "600025":
      case "600026":
        AliAuth.quitPage();
        Toast.show('请求超时,请切换其他手机号绑定!');
        Get.toNamed(AppRoutes.phoneLogin, arguments: {"bindOrLogin": "bind"});
        break;
      case "500004":
      case "600024":

        ///初始化插件返回的code，不做处理
        break;

      case "600000":
        _bindWithPhoneOneKey(event['data']);
        break;

      case "600002":
        AliAuth.quitPage();
        AliAuth.initSdk(getDefaultConfig());
        break;

      case "600007":
        Toast.show('未检测到SIM卡!');
        Get.toNamed(AppRoutes.phoneLogin, arguments: {"bindOrLogin": "bind"});
        break;

      case "700001":
        Get.toNamed(AppRoutes.phoneLogin, arguments: {"bindOrLogin": "bind"});
        break;

      // default:
      //   Toast.show('未知异常！');
      //   gotoPhoneLogin.call();
      //   break;
      // case '600019':
      //   if(event['data']==0){
      //     AliAuth.quitPage();
      //   }else if(event['data']==1){
      //     _signByApple();
      //   }else if(event['data']==2){
      //     gotoPasswordLogin.call();
      //   }
      //   break;
    }
  }

  //第三方登录后一键绑定手机号
  Future<void> _bindWithPhoneOneKey(String accessToken) async {
    try {
      var map = await userRepository.bindPhoneOneKey(accessToken);
      // if(map['token']!=null){
      var userInfo = await userRepository.queryUserInfo();
      if (userInfo.bindMobile != null) {
        await userRepository.setUserName(userInfo.bindMobile!);
      }
      if (userInfo.topicType == null || userInfo.topicType == '') {
        // Toast.show('successToStudyType');
        // successToStudyType.call();
        JumpPageUntil.jumpHomeAndCloseAll(arguments: {'isLoginEnter': true});
      } else {
        // Toast.show('successToHome');
        JumpPageUntil.jumpHomeAndCloseAll();
      }
    } catch (e) {
      if (e is DomainException) {
        Toast.show(e.message);
      } else {
        Toast.show(e.toString());
      }
    }
    // }
  }
}

//账号密码登录
Future<void> _startPasswordLogin(
    UserRepository userRepository, userName, passWord) async {
  try {
    // Loading.show();
    Map keyMap = await userRepository.getPassWordKey();
    // debugPrint('获取key结果：$keyMap');

    // print("加密:encrypt:${keyMap['secret']}----${EncryptUtils.aesEncrypt(passWord,keyMap['key'], keyMap['secret'])}");
    // print("解密:decrypt:${keyMap['secret']}----${EncryptUtils.aesDecrypt(EncryptUtils.aesEncrypt(passWord,keyMap['key'], keyMap['secret']), keyMap['secret'])}");
    String? token = await userRepository.getToken(
        userName,
        EncryptUtils.aesEncrypt(passWord, keyMap['key'], keyMap['secret']),
        keyMap['key']);
    debugPrint('登录结果：$token');
    if (token != null) {
      await userRepository.setUserToken(token);
      // await userRepository.setUserTokenInfo(code!, accessT['token']);
      var userInfo = await userRepository.queryUserInfo();
      if (userInfo.bindMobile == null || userInfo.bindMobile == '') {
        AliAuth.initSdk(getDefaultConfig());
      } else {
        await userRepository.setUserName(userInfo.bindMobile!);
        if (userInfo.topicType == null || userInfo.topicType == '') {
          // Toast.show('successToStudyType');
          JumpPageUntil.jumpHomeAndCloseAll(arguments: {'isLoginEnter': true});
        } else {
          // Toast.show('successToHome');
          JumpPageUntil.jumpHomeAndCloseAll();
        }
      }
    }
    // Loading.dismiss();
  } catch (e) {
    debugPrint('登录失败：$e');
    if (e is DomainException) {
      Toast.show(e.message);
    } else {
      Toast.show(e.toString());
    }
  }

  // debugPrint('登录结果：$login');
}

///阿里云一键登录自定义UI
AliAuthModel getDefaultConfig() {
  return AliAuthModel(
    androidSk, iosSk,
    // statusBarColor:'#FFFFFF',
    // statusBarColor: "#026ED2",
    lightColor: true,
    logoImgPath: 'assets/images/logo_denglu_shu.png',
    logoWidth: 202.w.toInt(),
    logoHeight: 183.h.toInt(),
    numberSize: 24.sp.toInt(),
    numberColor: '#4E4E4E',
    sloganHidden: true,
    pageBackgroundPath: 'assets/images/mobile_login_bg.png',
    alertBarIsHidden: true,
    isStatusBarHidden: false,
    logBtnText: '手机号一键绑定',
    logBtnTextColor: '#FFFFFF',
    logBtnMarginLeftAndRight: 24.w.toInt(),
    logBtnHeight: 51.h.toInt(),
    logBtnBackgroundPath:
        'assets/images/button_baoming.png,assets/images/button_baoming.png,assets/images/button_baoming.png',
    navHidden: true,
    switchAccText: '其他手机号绑定',
    switchOffsetY: (350.h + 20.h).toInt(),
    switchAccTextColor: '#268FF7',
    switchAccTextSize: 12.sp.toInt(),
    // customThirdView: customThirdView,//第三方登录按钮
    privacyBefore: '登陆即表明您同意',
    protocolOneName: '《用户服务协议》',
    protocolOwnColor: '#75C1FE',
    protocolOwnOneColor: '#75C1FE',
    protocolOneURL: 'https://dxjk.daxiangjd.com/agreement/?tag=user_agreement',
    protocolTwoName: '《隐私政策》',
    protocolOwnTwoColor: '#75C1FE',
    protocolTwoURL:
        'https://dxjk.daxiangjd.com/agreement/?tag=privacy_agreement',
    vendorPrivacyPrefix: '《',
    vendorPrivacySuffix: '》',
    privacyTextSize: 12.sp.toInt(),
    privacyEnd: '.',
    uncheckedImgPath: 'assets/images/regisster-icon-normai.png',
    checkedImgPath: 'assets/images/regisster_icon_selected.png',
    privacyConectTexts: [
      '和',
      ',',
    ],
    privacyOffsetY_B: 162.h.toInt(),
    // isHiddenCustom: true
    privacyAlertIsNeedShow: true,
    // yyy 2024-05-31
    isDebug: true,
    isDelay: false,
    statusBarUIFlag: UIFAG.systemUiFalgFullscreen,
    // backgroundPath: 'assets/images/mobile_login_bg.png',
    numFieldOffsetY: (220.h + 20.h).toInt(),
    logBtnOffsetY: (250.h + 51.h).toInt(),
    webViewStatusBarColor: '#FFFFFF',
    privacyState: false,
    checkboxHidden: false,
    privacyAlertContentHorizontalMargin: 24.w.toInt(),
    privacyAlertCloseImagPath: 'assets/home_img/dialog_close.png',
    checkBoxHeight: 20.h.toInt(),
    checkBoxWidth: 20.w.toInt(),
    privacyAlertContentAlignment: Gravity.centerHorizntal,
    privacyAlertBtnTextColor: '#FFFFFF',
    privacyAlertBtnHeigth: 35.h.toInt(),
    switchCheck: false,
    isHideToast: false,
    protocolThreeName: '',
    protocolGravity: Gravity.centerHorizntal,
    webNavColor: '#FFFFFF',
    webNavTextColor: '#000000',
    webNavReturnImgPath: 'assets/home_img/nav_icon_back.png',
    privacyAlertBtnBackgroundImgPath: 'assets/images/button_baoming.png',
    privacyAlertCornerRadiusArray: [
      12.w.toInt(),
      12.w.toInt(),
      12.w.toInt(),
      12.w.toInt()
    ],
    privacyAlertWidth: 300.w.toInt(),
    privacyAlertHeight: 200.h.toInt(),
    privacyAlertCloseImgWidth: 25.w.toInt(),
    privacyAlertCloseImgHeight: 25.h.toInt(),
    privacyAlertCloseScaleType: ScaleType.centerCrop,
  );
}

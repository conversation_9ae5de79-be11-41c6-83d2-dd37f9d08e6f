import 'dart:io';

import 'package:ali_auth/ali_auth.dart';
import 'package:api/api.dart';
import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:login/login.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:user_repository/user_repository.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'common/common_btn.dart';
import 'login_coustom_widget/auth_dialog.dart';
import 'login_coustom_widget/icon_btn.dart';
import 'login_coustom_widget/login_base_container.dart';
import 'login_coustom_widget/privacy_checkbox.dart';
import 'login_coustom_widget/send_sms.dart';

class PhoneLoginPage extends StatefulWidget {
  const PhoneLoginPage({
    super.key,
  });

  @override
  State<PhoneLoginPage> createState() => _PhoneLoginPageState();
}

class _PhoneLoginPageState extends State<PhoneLoginPage> {
  late UserRepository userRepository;

  bool _isCheck = false;
  final TextEditingController _phoneC = TextEditingController();
  final TextEditingController _verC = TextEditingController();

  _PhoneLoginPageState();

  // late LoginBloc bloc;
  bool isbind = false;
  Map loginMap = {};
  @override
  void initState() {
    super.initState();
    userRepository = Get.find<MainController>().userRepository;
    if (Get.arguments != null) {
      isbind = Get.arguments['bindOrLogin'] == 'bind';
      loginMap = Get.arguments['loginMap'] ?? {};
    }
    debugPrint("isbind:$isbind");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // bloc = BlocProvider.of<LoginBloc>(context);
  }

  @override
  void dispose() {
    super.dispose();
    _phoneC.clear();
    _verC.dispose();
    // bloc.add(CommitPhone(''));
  }

  @override
  Widget build(BuildContext context) {
    // dynamic param=RouteData.of(context).queryParameters;
    // debugPrint("param--:${param['bindOrLogin']}");
    // bool isbind=param['bindOrLogin']=='bind';
    return Scaffold(
        resizeToAvoidBottomInset: false,
        body: PopScope(
          canPop: true,
          onPopInvoked: (bool didPop) {
            debugPrint("didPop:$didPop");
            if (didPop) {
            } else {
              Navigator.pop(context);
            }
          },
          child: loginContainer(
            context,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.only(top: 148.h, left: 32.w),
                  child: Column(
                    // mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      app20sp268FF7Text(isbind ? "绑定手机号" : "欢迎来到大象驾到",
                          fontFamily: "PingFangSC-Medium"),
                      loginTitleInd()
                    ],
                  ),
                ),
                //手机号加验证码输入textField
                _buildPhoneAndVerText(),
                //手机号码登陆/绑定按钮
                _buildPhoneLoginBtn(isbind).paddingOnly(top: 60.h),
                const Spacer(),
                isbind
                    ? Container()
                    : PrivacyCheckbox(
                        // value: state.isCheck!,
                        value: _isCheck,
                        onChanged: (v) {
                          // BlocProvider.of<LoginBloc>(context)
                          //     .add(ChangeCheckBox(v));
                          setState(() {
                            _isCheck = v;
                          });
                        },
                        userAgreementOnClick: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => Scaffold(
                                        appBar: baseAppBarForLogin(
                                          title: '用户服务协议',
                                          onPressed: () {
                                            Navigator.pop(context);
                                          },
                                        ),
                                        body: WebView(
                                          initialUrl:
                                              'https://dxjk.daxiangjd.com/agreement/?tag=user_agreement',
                                          javascriptMode:
                                              JavascriptMode.unrestricted,
                                        ),
                                      )));
                        },
                        privacyPolicyOnClick: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => Scaffold(
                                        appBar: baseAppBarForLogin(
                                          title: '隐私协议',
                                          onPressed: () {
                                            Navigator.pop(context);
                                          },
                                        ),
                                        body: WebView(
                                          initialUrl:
                                              'https://dxjk.daxiangjd.com/agreement/?tag=privacy_agreement',
                                          onPageFinished: (String url) {
                                            debugPrint(
                                                'Page finished loading: $url');
                                          },
                                          onPageStarted: (String url) {
                                            debugPrint(
                                                'Page started loading: $url');
                                          },
                                          onWebViewCreated: (WebViewController
                                              webViewController) {},
                                          javascriptMode:
                                              JavascriptMode.unrestricted,
                                        ),
                                      )));
                        },
                      ).paddingOnly(left: 24.w, right: 27.w),
                SizedBox(
                  height: 51.h,
                ),
                isbind ? Container() : _buildOtherLogin(context),
                SizedBox(
                  height: 49.h,
                ),
              ],
            ),
          ),
        )
        // BlocBuilder<LoginBloc, LoginState>(
        //   builder: (context, state) {
        //     return

        //   },
        // ),
        // ),
        // )
        );
  }

  //手机号加验证码输入textField
  Widget _buildPhoneAndVerText() {
    // return BlocBuilder<LoginBloc, LoginState>(
    //   // buildWhen: (previos, current) {
    //   //   return previos.phoneNumber != current.phoneNumber;
    //   // },
    //   builder: (context, state) {
    return Padding(
      padding: EdgeInsets.all(32.0.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          loginPhoneTextField(controller: _phoneC),
          // TextField(
          //   inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          //   keyboardType: TextInputType.phone,
          //   maxLength: 11,
          //   // autofocus: true,
          //   controller: _phoneC,
          //   style: TextStyle(
          //       color: AppColors.login_textfiled_color, fontSize: 16.sp),
          //   onChanged: (v) {
          //     if (v != "") {
          //       // BlocProvider.of<LoginBloc>(context).add(CommitPhone(v));
          //     }
          //   },
          //   // focusNode: _nameFocusNode,
          //   decoration: InputDecoration(
          //     border: InputBorder.none,
          //     counterText: "",
          //     hintText: '请输入手机号',
          //     prefixIconConstraints:
          //         BoxConstraints.tightFor(height: 30.h, width: 30.w),
          //     hintStyle: TextStyle(
          //         height: 2.h,
          //         color: AppColors.login_textfiled_color,
          //         fontSize: 16.sp
          //     ),
          //     prefixIcon: assImg(img: 'icon_denglu_shoujihao',).marginOnly(right: 10.w),
          //   ),
          // ),
          Divider(height: 1.h, color: AppColors.E6F2FE),
          SizedBox(height: 20.0.h),
          Row(
            children: [
              Expanded(
                child: loginPhoneTextField(
                    controller: _verC,
                    maxLength: 5,
                    hintText: '请输入验证码',
                    img: 'icon_denglu_yanzheng'),
                // TextField(
                //   controller: _verC,
                //   keyboardType: TextInputType.phone,
                //   maxLength: 6,
                //   inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                //   // focusNode: _passwordFocusNode,
                //   // obscureText: true
                //   onChanged: (v) {
                //     // BlocProvider.of<LoginBloc>(context).add(CommitVer(v));
                //   },
                //   decoration: InputDecoration(
                //     // filled: true,
                //     // fillColor: _passwordHasFocus ? Colors.lightBlue[100] : Colors.grey[200],
                //     border: InputBorder.none,
                //     counterText: "",
                //     hintText: '请输入验证码',
                //     prefixIconConstraints:
                //         BoxConstraints.tightFor(height: 22.h, width: 22.w),
                //     hintStyle: TextStyle(
                //         height: 2.h,
                //         color: AppColors.login_textfiled_color,
                //         fontSize: 16.sp),
                //     prefixIcon: Image(
                //       height: 20.h,
                //       width: 20.w,
                //       image: AssetImage(
                //           "assets/images/icon_denglu_yanzheng.png"),
                //       // color: Colors.blue,
                //     ),
                //   ),
                // ),
              ),
              // BlocBuilder<LoginBloc, LoginState>(
              //   builder: (context, state) {
              //     return
              SendSmsBtn(
                onTap: () async {
                  if (_phoneC.value.text == "" ||
                      _phoneC.value.text.isEmpty ||
                      !(RegExp(r"^1[3-9]\d{9}$")
                          .hasMatch(_phoneC.value.text))) {
                    Toast.show("请输入正确手机号");
                    return false;
                  } else {
                    if (await getPhoneVer(_phoneC.value.text)) {
                      return true;
                    } else {
                      return false;
                    }
                  }
                  // String regPs = r"^1[3-9]\d{9}$";
                  //
                  // if (state.phoneNumber!.isEmpty ||
                  //     state.phoneNumber! == "" ||
                  //     !(RegExp(r"^1[3-9]\d{9}$")
                  //         .hasMatch(state.phoneNumber!))) {
                  //   Toast.show("请输入正确手机号");
                  //   return false;
                  // } else {
                  //   return true;
                  // }
                },
              )
              //   },
              // ),
            ],
          ),
          Divider(height: 1.h, color: AppColors.E6F2FE),
        ],
      ),
    );
    //   },
    // );
  }

  //手机号码登陆/绑定按钮
  Widget _buildPhoneLoginBtn(isbind) {
    return GestureDetector(
        onTap: isbind
            ? () {
                if (_phoneC.value.text.isEmpty ||
                    !(RegExp(r"^1[3-9]\d{9}$").hasMatch(_phoneC.value.text))) {
                  Toast.show("请输入正确手机号！");
                } else {
                  if (_verC.value.text.isEmpty ||
                      _verC.value.text.length != 5) {
                    Toast.show("请输入正确验证码！");
                  } else {
                    // Toast.show("发起绑定请求！");
                    if (loginMap.isEmpty) {
                      bindPhoneWithMobileVerify(
                          _phoneC.value.text, _verC.value.text);
                    } else {
                      vxBindPhoneWithMobileVerify(
                          _phoneC.value.text, _verC.value.text);
                    }
                  }
                }
              }
            : () {
                // if (state.isCheck!) {
                if (_isCheck) {
                  // if (state.phoneNumber!.isEmpty ||
                  //     state.phoneNumber! == "" ||
                  //     !(RegExp(r"^1[3-9]\d{9}$").hasMatch(state.phoneNumber!))) {
                  //   Toast.show("请输入正确手机号！");
                  // }
                  if (_phoneC.value.text.isEmpty ||
                      !(RegExp(r"^1[3-9]\d{9}$")
                          .hasMatch(_phoneC.value.text))) {
                    Toast.show("请输入正确手机号！");
                  } else {
                    if (_verC.value.text.isEmpty ||
                        _verC.value.text.length != 5) {
                      Toast.show("请输入正确验证码！");
                    } else {
                      _startLogin(
                          userRepository, _phoneC.value.text, _verC.value.text);
                      // Toast.show("发起登陆请求！");
                    }
                    // if (state.verNumber!.isEmpty ||
                    //     state.verNumber!.length != 6) {
                    //   Toast.show( "请输入正确验证码！");
                    // } else {
                    //   Toast.show("发起登陆请求！");
                    // }
                  }
                } else {
                  //弹窗
                  // _showPerDialog(context);
                  showPrivacyDialog(context).then((value) {
                    if (value == true) {
                      setState(() {
                        if (!_isCheck) {
                          _isCheck = !_isCheck;
                        }
                      });
                    }
                  });
                  // showDialog(
                  //     context: context,
                  //     builder: (c) {
                  //       return AuthDialog(
                  //           title: "温馨提示",
                  //           content: "为了更好的保障您的合法权益，请您阅读并同意《用户服务协议》、《隐私政策》.",
                  //           onTap: () {
                  //             // if (!state.isCheck!) {
                  //             //   BlocProvider.of<LoginBloc>(context)
                  //             //       .add(ChangeCheckBox(!state.isCheck!));
                  //             //   Navigator.pop(context);
                  //             // }
                  //             // if (!_isCheck) {
                  //             //   _isCheck = !_isCheck;
                  //             // }
                  //             // setState(() {
                  //             //   Navigator.pop(c);
                  //             // });
                  //           },
                  //           btn_cancel_content: '不同意',
                  //           btn_confim_content: '同意');
                  //     }).then((value) {
                  //
                  // });
                }
              },
        child: h46c5AADr25Container(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Center(
              child: app16spFFFText(isbind ? "确认绑定" : "登录",
                  fontFamily: 'PingFangSC-Medium'),
            )
            // BlocBuilder<LoginBloc, LoginState>(
            //   builder: (context, state) {
            //     return

            // ElevatedButton(
            //   onPressed: isbind?(){
            //     Toast.show("发起绑定请求");
            //   } :() {
            //     // if (state.isCheck!) {
            //     if (_isCheck) {
            //       // if (state.phoneNumber!.isEmpty ||
            //       //     state.phoneNumber! == "" ||
            //       //     !(RegExp(r"^1[3-9]\d{9}$").hasMatch(state.phoneNumber!))) {
            //       //   Toast.show("请输入正确手机号！");
            //       // }
            //       if (_phoneC.value.text.isEmpty ||
            //           !(RegExp(r"^1[3-9]\d{9}$").hasMatch(_phoneC.value.text))) {
            //         Toast.show("请输入正确手机号！");
            //       } else {
            //         if (_verC.value.text.isEmpty || _verC.value.text.length != 6) {
            //           Toast.show("请输入正确验证码！");
            //         } else {
            //           Toast.show("发起登陆请求！");
            //         }
            //         // if (state.verNumber!.isEmpty ||
            //         //     state.verNumber!.length != 6) {
            //         //   Toast.show( "请输入正确验证码！");
            //         // } else {
            //         //   Toast.show("发起登陆请求！");
            //         // }
            //       }
            //     } else {
            //       //弹窗
            //       // _showPerDialog(context);
            //       showDialog(
            //           context: context,
            //           builder: (c) {
            //             return AuthDialog(
            //                 title: "温馨提示",
            //                 content: "为了更好的保障您的合法权益，请您阅读并同意《用户服务协议》、《隐私政策》.",
            //                 onTap: () {
            //                   // if (!state.isCheck!) {
            //                   //   BlocProvider.of<LoginBloc>(context)
            //                   //       .add(ChangeCheckBox(!state.isCheck!));
            //                   //   Navigator.pop(context);
            //                   // }
            //                   // if (!_isCheck) {
            //                   //   _isCheck = !_isCheck;
            //                   // }
            //                   // setState(() {
            //                   //   Navigator.pop(c);
            //                   // });
            //                 },
            //                 btn_cancel_content: '不同意',
            //                 btn_confim_content: '同意');
            //           }).then((value) {
            //         if (value == true) {
            //           setState(() {
            //             if (!_isCheck) {
            //               _isCheck = !_isCheck;
            //             }
            //           });
            //         }
            //       });
            //     }
            //   },
            //   style: ElevatedButton.styleFrom(
            //       shape: RoundedRectangleBorder(
            //           borderRadius: BorderRadius.circular(25.r)),
            //       backgroundColor: AppColors.login_btn_color),
            //   child: Text(
            //     isbind?"确认绑定":"登录",
            //     style: TextStyle(color: AppColors.white, fontSize: 16.sp),
            //   ),
            // )
            //   },
            // ),
            ).marginSymmetric(horizontal: 24.w));
  }

  //其他登录
  Widget _buildOtherLogin(context) {
    List<Widget> list = [
      Platform.isIOS
          ? meIconButton(
              icon: "denglu_icon_apple.png",
              text: "苹果ID",
              isShowText: Platform.isAndroid,
              onTap: () async {
                if (_isCheck) {
                  await _signByApple();
                } else {
                  Toast.show('请同意隐私协议!');
                }
                debugPrint("苹果id登陆");
              })
          : Container(),
      meIconButton(
          icon: "denglu_icon_mima.png",
          text: "密码登陆",
          isShowText: Platform.isAndroid,
          onTap: () {
            Get.toNamed(AppRoutes.passwordLogin);
            debugPrint("密码登陆");
          }),
    ];
    if (MainController.showVxLogin()) {
      list.insert(
          0,
          meIconButton(
              icon: "denglu_icon_weixin.png",
              text: "微信登陆",
              isShowText: Platform.isAndroid,
              onTap: () {
                Navigator.pop(context);

                debugPrint("微信登陆");
              }));
    }
    return Padding(
        padding: EdgeInsets.only(bottom: 15.h),
        child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: list));
  }

  //获取苹果id及登录
  Future<void> _signByApple() async {
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );
    debugPrint("获取到的credential:$credential");
    loginByApple(
        name: (credential.familyName ?? '') + (credential.givenName ?? ''),
        email: credential.email ?? '',
        userIdentifier: credential.userIdentifier ?? '',
        identityToken: credential.identityToken ?? '',
        authorizationCode: credential.authorizationCode);
  }

  Future<void> loginByApple(
      {name, email, userIdentifier, identityToken, authorizationCode}) async {
    try {
      final userDM = await userRepository.loginByApple(
          name: name,
          email: email,
          userIdentifier: userIdentifier,
          identityToken: identityToken,
          authorizationCode: authorizationCode);
      if (userDM == null) {
      } else {
        if (userDM.bindMobile == null) {
          AliAuth.initSdk(getDefaultConfig());
        } else {
          if (userDM.topicType == null) {
            Get.toNamed(AppRoutes.homeStudyTypeSelect,
                arguments: {'isLoginEnter': true});
          } else {
            JumpPageUntil.jumpHomeAndCloseAll();
          }
        }
      }
    } catch (e) {
      if (e is DomainException) {
        Toast.show(e.message);
      } else {
        Toast.show(e.toString());
      }
    }
  }

  //获取验证码
  Future<bool> getPhoneVer(String phone) async {
    bool success = false;
    final Map result;
    try {
      result = await userRepository.getVer(phone);
      if (result.isEmpty) {
        success = true;
      }
    } catch (e) {
      if (e is DomainException) {
        Toast.show(e.message);
      } else {
        Toast.show(e.toString());
      }
      return false;
    }
    return success;
  }

  //手机号码登陆
  Future<void> _startLogin(UserRepository userRepository, phone, ver) async {
    try {
      // var  a ="***********";
      // var  a ="***********";
      var userDM = await userRepository.phoneLogin(phone, ver);
      debugPrint("userDM:-----${userDM.toString()}");
      if (userDM == null) {
      } else {
        if (userDM.topicType == null || userDM.topicType == '') {
          Get.toNamed(AppRoutes.homeStudyTypeSelect,
              arguments: {'isLoginEnter': true});
        } else {
          // await Get.find<ExaminationController>(tag: ExaminationPageState.uniqueKey.toString()).userSubject?.cancel();
          print("-------ExaminationPage initState cancel userSubject-------");
          JumpPageUntil.jumpHomeAndCloseAll();
          // Get.offAllNamed(AppRoutes.home,);
        }
      }
    } catch (e) {
      if (e is DomainException) {
        Toast.show(e.message);
      } else {
        Toast.show(e.toString());
      }
    }
    // if(map['token']!=null) {
    //   debugPrint('---------->${map['token']}');
    //   await userRepository.setUserTokenInfo(phone,map['token']);
    //   var userInfo=await userRepository.queryUserInfo();
    //   debugPrint('---------->${userInfo.toString()}');
    //   if(userInfo.topicType==null||userInfo.topicType==''){
    //     successToStudyType.call();
    //   }else{
    //     successToHome.call();
    //   }
    // }
  }

  //手机号验证码绑定
  Future<void> bindPhoneWithMobileVerify(String phone, String verify) async {
    try {
      var map = await userRepository.bindPhoneWithMobileVerify(phone, verify);
      var userInfo = await userRepository.queryUserInfo();
      await userRepository.setUserName(userInfo.bindMobile!);
      if (userInfo.topicType == null || userInfo.topicType == '') {
        // Toast.show('successToStudyType');
        // successToStudyType.call();
        Get.toNamed(AppRoutes.homeStudyTypeSelect,
            arguments: {'isLoginEnter': true});
      } else {
        // Toast.show('successToHome');
        // await Get.find<ExaminationController>(tag: ExaminationPageState.uniqueKey.toString()).userSubject?.cancel();
        print("-------ExaminationPage initState cancel userSubject-------");
        JumpPageUntil.jumpHomeAndCloseAll();
        // Get.offAllNamed(AppRoutes.home,);
      }
    } catch (e) {
      if (e is DomainException) {
        Toast.show(e.message);
      } else {
        Toast.show(e.toString());
      }
      return;
    }
  }

  //手机号验证码绑定(微信)
  Future<void> vxBindPhoneWithMobileVerify(String phone, String verify) async {
    try {
      var map = await userRepository.vXbindPhoneOneKey(
          accessToken: loginMap!['accessToken']!,
          openId: loginMap!['openId']!,
          isSimple: 2,
          mobile: phone,
          code: verify);
      // if(map['token']!=null){
      await userRepository.setUserToken(map['token']);
      var userInfo = await userRepository.queryUserInfo();
      if (userInfo.bindMobile != null) {
        await userRepository.setUserName(userInfo.bindMobile!);
      }
      if (userInfo.topicType == null || userInfo.topicType == '') {
        // Toast.show('successToStudyType');
        // successToStudyType.call();
        Get.toNamed(AppRoutes.homeStudyTypeSelect,
            arguments: {'isLoginEnter': true});
      } else {
        // Toast.show('successToHome');
        JumpPageUntil.jumpHomeAndCloseAll();
      }
    } catch (e) {
      if (e is DomainException) {
        Toast.show(e.message);
      } else {
        Toast.show(e.toString());
      }
    }
  }
}

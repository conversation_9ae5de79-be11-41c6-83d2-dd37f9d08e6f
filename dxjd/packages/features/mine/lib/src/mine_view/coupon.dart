import 'package:api/api.dart';
import 'package:component_library/component_library.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:login/login.dart';
import 'package:mine/src/mine_view/coupon_controller.dart';
import 'package:mine/src/mine_view/coustom_widget/custom_view.dart';

class CouponPage extends StatefulWidget {
  const CouponPage({super.key});

  @override
  State<CouponPage> createState() => _CouponPageState();
}

class _CouponPageState extends State<CouponPage>
    with SingleTickerProviderStateMixin {
  late TabController tabController;
  int unusedTotal = 0;
  int usedTotal = 0;
  int expiredTotal = 0;
  @override
  void initState() {
    // TODO: implement initState
    tabController = TabController(length: 3, vsync: this);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '优惠券',
          style: TextStyle(
              color: const Color(0xFF001833),
              fontFamily: "PingFangSC-Bold",
              fontSize: 16.sp,
              fontWeight: FontWeight.bold),
        ),
        titleTextStyle: const TextStyle(fontWeight: FontWeight.normal),
        centerTitle: true,
        leading: IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: assImg2(img: 'nav_icon_back', w: 18.w)),
      ),
      body: Column(
        children: [
          TabBar(
            controller: tabController,
            tabs: [
              Text(
                "未使用${unusedTotal == 0 ? "" : _isMoreThan99(unusedTotal)}",
              ),
              Tab(text: "已使用${usedTotal == 0 ? "" : _isMoreThan99(usedTotal)}"),
              Tab(
                  text:
                      "已过期${expiredTotal == 0 ? "" : _isMoreThan99(expiredTotal)}")
            ],
            labelColor: const Color(0xFF000000),
            tabAlignment: TabAlignment.fill,
            labelStyle: TextStyle(
                fontFamily: "PingFangSC-Bold",
                fontWeight: FontWeight.w700,
                fontSize: 16.sp),
            unselectedLabelStyle: TextStyle(
                fontFamily: "PingFangSC-Medium",
                fontWeight: FontWeight.w500,
                fontSize: 16.sp),
            unselectedLabelColor: const Color(0xFFAAAAAA),
            overlayColor:
                MaterialStateProperty.all(Colors.transparent), // 去除水波纹
            dividerHeight: 0,
            indicator: UnderlineTabIndicator(
              borderSide:
                  const BorderSide(width: 3.0, color: Color(0xFF000000)),
              borderRadius: BorderRadius.circular(8.r),
              insets: EdgeInsets.symmetric(
                  horizontal: 12.w), // 通过调整horizontal的值来设置宽度
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: tabController,
              children: [
                KeepAliveWrappers(
                  child: GetBuilder<CouponController>(
                      init: CouponController(),
                      tag: "1",
                      builder: (logic) {
                        return Container(
                          color: const Color(0xFFF5F7F9),
                          child: EasyRefresh(
                            onLoad: logic.isNoMore
                                ? null
                                : () {
                                    logic.loadData(false, "1");
                                  },
                            onRefresh: () {
                              logic.loadData(true, "1", callback: _setTabNum);
                            },
                            controller: logic.easyRefreshController,
                            header: const ClassicHeader(
                                processingText: "正在刷新...",
                                readyText: "正在刷新...",
                                armedText: "释放以刷新",
                                dragText: "下拉刷新",
                                processedText: "刷新成功",
                                failedText: "刷新失败",
                                showMessage: false),
                            footer: const ClassicFooter(
                                processingText: "加载中...",
                                processedText: "加载成功",
                                readyText: "加载中...",
                                armedText: "释放以加载更多",
                                dragText: "上拉加载",
                                failedText: "加载失败",
                                noMoreText: "没有更多内容",
                                showMessage: false),
                            child: ListView.builder(
                              physics: logic.isBanScroll
                                  ? const NeverScrollableScrollPhysics()
                                  : null,
                              itemBuilder: (context, index) {
                                if (logic.couponList == null &&
                                    logic.isNoMore) {
                                  return Padding(
                                    padding: REdgeInsets.only(left: 95),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          height: 64.h,
                                        ),
                                        assImg2(
                                          img: 'bg_coupon_no_data',
                                          w: 160.w,
                                          h: 129.h,
                                        ),
                                        SizedBox(
                                          height: 23.h,
                                        ),
                                        Padding(
                                          padding: REdgeInsets.only(left: 58),
                                          child: Text(
                                            '暂无优惠券',
                                            style: TextStyle(
                                              color: const Color(0xFFAAAAAA),
                                              fontSize: 14.sp,
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  );
                                }
                                CouponItem item = logic.couponList!.list[index];
                                return couponWidget(logic, 0, item);
                              },
                              itemCount: logic.couponList?.list.length ??
                                  (logic.isNoMore ? 1 : 0),
                            ),
                          ),
                        );
                      }),
                ),
                KeepAliveWrappers(
                  child: GetBuilder<CouponController>(
                      init: CouponController(),
                      tag: "2",
                      builder: (logic) {
                        return Container(
                          color: const Color(0xFFF5F7F9),
                          child: EasyRefresh(
                            onLoad: logic.isNoMore
                                ? null
                                : () {
                                    logic.loadData(false, "2");
                                  },
                            onRefresh: () {
                              logic.loadData(true, "2", callback: _setTabNum);
                            },
                            controller: logic.easyRefreshController,
                            header: const ClassicHeader(
                                processingText: "正在刷新...",
                                readyText: "正在刷新...",
                                armedText: "释放以刷新",
                                dragText: "下拉刷新",
                                processedText: "刷新成功",
                                failedText: "刷新失败",
                                showMessage: false),
                            footer: const ClassicFooter(
                                processingText: "加载中...",
                                processedText: "加载成功",
                                readyText: "加载中...",
                                armedText: "释放以加载更多",
                                dragText: "上拉加载",
                                failedText: "加载失败",
                                noMoreText: "没有更多内容",
                                showMessage: false),
                            child: ListView.builder(
                              physics: logic.isBanScroll
                                  ? const NeverScrollableScrollPhysics()
                                  : null,
                              itemBuilder: (context, index) {
                                if (logic.couponList == null &&
                                    logic.isNoMore) {
                                  return Padding(
                                    padding: REdgeInsets.only(left: 95),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          height: 64.h,
                                        ),
                                        assImg2(
                                          img: 'bg_coupon_no_data',
                                          w: 160.w,
                                          h: 129.h,
                                        ),
                                        SizedBox(
                                          height: 23.h,
                                        ),
                                        Padding(
                                          padding: REdgeInsets.only(left: 58),
                                          child: Text(
                                            '暂无优惠券',
                                            style: TextStyle(
                                              color: const Color(0xFFAAAAAA),
                                              fontSize: 14.sp,
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  );
                                }
                                CouponItem item = logic.couponList!.list[index];
                                return couponWidget(logic, 1, item);
                              },
                              itemCount: logic.couponList?.list.length ??
                                  (logic.isNoMore ? 1 : 0),
                            ),
                          ),
                        );
                      }),
                ),
                KeepAliveWrappers(
                  child: GetBuilder<CouponController>(
                      init: CouponController(),
                      tag: "3",
                      builder: (logic) {
                        return Container(
                          color: const Color(0xFFF5F7F9),
                          child: EasyRefresh(
                            onLoad: logic.isNoMore
                                ? null
                                : () {
                                    logic.loadData(
                                      false,
                                      "3",
                                    );
                                  },
                            onRefresh: () {
                              logic.loadData(true, "3", callback: _setTabNum);
                            },
                            controller: logic.easyRefreshController,
                            header: const ClassicHeader(
                                processingText: "正在刷新...",
                                readyText: "正在刷新...",
                                armedText: "释放以刷新",
                                dragText: "下拉刷新",
                                processedText: "刷新成功",
                                failedText: "刷新失败",
                                showMessage: false),
                            footer: const ClassicFooter(
                                processingText: "加载中...",
                                processedText: "加载成功",
                                readyText: "加载中...",
                                armedText: "释放以加载更多",
                                dragText: "上拉加载",
                                failedText: "加载失败",
                                noMoreText: "没有更多内容",
                                showMessage: false),
                            child: ListView.builder(
                              physics: logic.isBanScroll
                                  ? const NeverScrollableScrollPhysics()
                                  : null,
                              itemBuilder: (context, index) {
                                if (logic.couponList == null &&
                                    logic.isNoMore) {
                                  return Padding(
                                    padding: REdgeInsets.only(left: 95),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          height: 64.h,
                                        ),
                                        assImg2(
                                          img: 'bg_coupon_no_data',
                                          w: 160.w,
                                          h: 129.h,
                                        ),
                                        SizedBox(
                                          height: 23.h,
                                        ),
                                        Padding(
                                          padding: REdgeInsets.only(left: 58),
                                          child: Text(
                                            '暂无优惠券',
                                            style: TextStyle(
                                              color: const Color(0xFFAAAAAA),
                                              fontSize: 14.sp,
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  );
                                }
                                CouponItem item = logic.couponList!.list[index];
                                return couponWidget(logic, 2, item);
                              },
                              itemCount: logic.couponList?.list.length ??
                                  (logic.isNoMore ? 1 : 0),
                            ),
                          ),
                        );
                      }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // status 0未使用 1已使用 2已过期
  Widget couponWidget(CouponController logic, int status, CouponItem item) {
    return Stack(
      children: [
        Container(
          margin: REdgeInsets.symmetric(vertical: 10, horizontal: 11),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: Colors.white,
          ),
          child: Column(
            children: [
              Padding(
                padding: REdgeInsets.symmetric(horizontal: 22, vertical: 14),
                child: Row(
                  children: [
                    Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.baseline,
                          textBaseline: TextBaseline.alphabetic,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              "￥",
                              style: TextStyle(
                                  color: status != 0
                                      ? const Color(0xFFAAAAAA)
                                      : const Color(0xFFFF3639),
                                  fontSize: 12.sp,
                                  height: 0.5,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "PingFangSC-Bold"),
                            ),
                            Text(
                              "${item.discount ~/ 100}",
                              style: TextStyle(
                                  color: status != 0
                                      ? const Color(0xFFAAAAAA)
                                      : const Color(0xFFFF3639),
                                  fontSize: 32.sp,
                                  height: 0.75,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "PingFangSC-Bold"),
                            )
                          ],
                        ),
                        SizedBox(
                          height: 7.h,
                        ),
                        Text(
                          "满${item.minOrderAmount ~/ 100}可用",
                          style: TextStyle(
                              color: status != 0
                                  ? const Color(0xFFAAAAAA)
                                  : const Color(0xFF000000),
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                              fontFamily: "PingFangSC-Medium"),
                        )
                      ],
                    ),
                    SizedBox(
                      width: 17.w,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.couponName,
                          style: TextStyle(
                              color: status != 0
                                  ? const Color(0xFFAAAAAA)
                                  : const Color(0xFF000000),
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              fontFamily: "PingFangSC-Bold"),
                        ),
                        SizedBox(
                          height: 6.h,
                        ),
                        Text(
                          item.vaildEndTime == -1
                              ? ""
                              : "有效期至${_formatTimestamp(item.vaildEndTime)}",
                          style: TextStyle(
                              color: const Color(0xFFAAAAAA),
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                              fontFamily: "PingFangSC-Medium"),
                        ),
                      ],
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: status != 0
                          ? null
                          : () {
                              logic.goUseCoupon(item);
                            },
                      child: Container(
                        padding:
                            REdgeInsets.symmetric(horizontal: 17, vertical: 7),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15.r),
                          color: status != 0
                              ? const Color(0xFFAAAAAA)
                              : const Color(0xFFFF3639),
                        ),
                        child: Text(
                          status == 1 ? "已使用" : (status == 2 ? "已过期" : "去使用"),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              DashedLineWidget(
                width: double.infinity,
                height: 1,
                dashWidth: 5,
                dashSpace: 5,
                color: const Color(0xFFEAEAEA),
              ),
              Padding(
                padding: REdgeInsets.symmetric(horizontal: 19, vertical: 9),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "适用商品",
                      style: TextStyle(
                          color: const Color(0xFFAAAAAA),
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          fontFamily: "PingFangSC-Medium"),
                    ),
                    SizedBox(
                      width: 9.w,
                    ),
                    Expanded(
                      child: Row(
                          children: List.generate(
                              item.sellsGoodsInfoList.length, (index) {
                        return _productItem(
                            item.sellsGoodsInfoList[index], logic, status);
                      })),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
        Positioned(
            left: 6.w,
            top: 50.w,
            child: Container(
              width: 10.w,
              height: 10.w,
              decoration: BoxDecoration(
                color: const Color(0xFFF5F7F9),
                borderRadius: BorderRadius.circular(5),
              ),
            )),
        Positioned(
            right: 6.w,
            top: 50.w,
            child: Container(
              width: 10.w,
              height: 10.w,
              decoration: BoxDecoration(
                color: const Color(0xFFF5F7F9),
                borderRadius: BorderRadius.circular(5),
              ),
            ))
      ],
    );
  }

  Widget _productItem(
      SellsGoodsInfoList item, CouponController logic, int status) {
    return Padding(
      padding: REdgeInsets.only(left: 17.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          InkWell(
            onTap: status != 0
                ? null
                : () {
                    JumpCouPonPage.jumpCouPonUsePage(item);
                  },
            child: item.cover.isEmpty
                ? SizedBox(
                    width: 40.w,
                    height: 40.w,
                    child: assImg2(img: "icon_coupon_good"),
                  )
                : netWorkCacheImg2(
                    img: item.cover[0], w: 40.w, h: 40.w, fit: BoxFit.fill),
          ),
          SizedBox(
            height: 6.h,
          ),
          Text(
            item.title ?? "",
            style: TextStyle(
                color: const Color(0xFFAAAAAA),
                fontSize: 10.sp,
                fontWeight: FontWeight.w500,
                fontFamily: "PingFangSC-Medium"),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(int timestamp) {
    // 判断是秒级还是毫秒级时间戳（通常 Flutter 时间戳是毫秒级）
    final dateTime = DateTime.fromMillisecondsSinceEpoch(
      timestamp > 9999999999 ? timestamp : timestamp * 1000,
    );

    // 使用 DateFormat 格式化
    return DateFormat('yyyy.M.d HH:mm').format(dateTime);
  }

  _setTabNum(int num, String status) {
    setState(() {
      if (status == "1") {
        unusedTotal = num;
      } else if (status == "2") {
        usedTotal = num;
      } else {
        expiredTotal = num;
      }
    });
  }

  _isMoreThan99(int num) {
    if (num <= 99) {
      return " ($num)";
    } else {
      return " (99+)";
    }
  }
}

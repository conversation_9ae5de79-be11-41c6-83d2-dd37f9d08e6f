import 'dart:io';

import 'package:component_library/component_library.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:login/login.dart';
import 'package:tools/tools.dart';
import 'package:mop/mop.dart';
import 'package:user_repository/user_repository.dart';

import 'coustom_widget/custom_view.dart';
import 'package:path_provider/path_provider.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<SettingPage> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  late UserRepository userRepository;

  List settingItem = [
    '清理缓存',
    '关于我们',
    '隐私管理',
    '会员协议',
    '隐私设置',
    '个人信息收集清单',
    '第三方共享清单',
    // '切换摄像头',
    '当前版本',
  ];

  _SettingPageState();

  double cache = 0;
  String version = '';
  @override
  void initState() {
    super.initState();
    userRepository = Get.find<MainController>().userRepository;
    _getCacheSize();
  }

  _getCacheSize() async {
    final _tempDir = await getTemporaryDirectory();
    double _cache = await CacheUtils.getTotalSizeOfFilesInDir(_tempDir);
    var verTemp = await Tools.getAppVersion();
    setState(() {
      cache = _cache;
      version = verTemp;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData(
        appBarTheme: const AppBarTheme(
          /// ...
          /// 设置AppBar 的表面颜色为透明色
          surfaceTintColor: Colors.transparent,

          /// ...
        ),
      ),
      child: Scaffold(
        appBar: baseAppBar(
          title: '设置',
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        body: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          height: double.infinity,
          color: AppColors.F6F5F5,
          child: Column(
            children: [
              white8radiusContainer(
                  height: 124.h,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _settingItemOnTap(
                          onTap: () async {
                            // Toast.show("账户管理");
                            final bindInfo =
                                await userRepository.queryUserAccountOpenInfo();
                            if (bindInfo.isNotEmpty) {
                              userRepository.setAccountInfo(bindInfo);
                              Get.toNamed(AppRoutes.accountManagePage);
                            }
                          },
                          text: '账户管理'),
                      _settingItemOnTap(
                          onTap: () {
                            const shopPath = '/pages/operate/feedback/add/add';
                            // Mop.instance.openApplet('fc2230308094514309',
                            //     path: shopPath);
                            JumpSmallProgramUtils.jump(shopPath, "fc2230308094514309");
                            // Toast.show("意见反馈");
                          },
                          text: '意见反馈'),
                    ],
                  )).paddingOnly(top: 24.h, bottom: 13.h),
              Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8.r)),
                  constraints: BoxConstraints(maxHeight: 432.h),
                  child: ListView.builder(
                      itemBuilder: (_, index) {
                        return _settingItemOnTap(
                            index: index,
                            text: settingItem[index],
                            onTap: () {
                              if (index == 0) {
                                _clearCache(context);
                                // Toast.show("清理缓存");
                              } else if (index == 1) {
                                const shopPath = '/pages/about/index/index';
                                // Mop.instance.openApplet('fc2230308094514309',
                                //     path: shopPath);
                                JumpSmallProgramUtils.jump(shopPath, "fc2230308094514309");
                              } else if (index == 2) {
                                const shopPath =
                                    '/pages/about/agreement/agreement?tag=privacy_agreement';
                                // Mop.instance.openApplet('fc2230308094514309',
                                //     path: shopPath);
                                JumpSmallProgramUtils.jump(shopPath, "fc2230308094514309");
                              } else if (index == 3) {
                                const shopPath =
                                    '/pages/about/agreement/agreement?tag=vip_agreement';
                                // Mop.instance.openApplet('fc2230308094514309',
                                //     path: shopPath);
                                JumpSmallProgramUtils.jump(shopPath, "fc2230308094514309");
                              } else if (index == 4) {
                                Get.toNamed(AppRoutes.privacySettingsPage);
                              } else if (index == 5) {
                                Get.toNamed(AppRoutes
                                    .personalInformationCollectionChecklistPage);
                              } else if (index == 6) {
                                Get.toNamed(AppRoutes.sharedListPage);
                              } else if (index == 7) {
                                // Get.toNamed(AppRoutes.sharedListPage);
                              }
                              // Toast.show(settingItem[index]);
                            }).paddingOnly(left: 16.w, top: 16.h, bottom: 16.h);
                      },
                      shrinkWrap: true,
                      itemCount: settingItem.length)),
              Spacer(),
              GestureDetector(
                  onTap: () {
                    _userLogout();
                    userRepository.cleanVipMap();
                    // Toast.show("退出登录");
                  },
                  child: Container(
                    height: 46.h,
                    margin: REdgeInsets.only(top: 16),
                    decoration: BoxDecoration(
                        image: DecorationImage(
                            image: assetImg2(img: 'button_gerenxinxi'))),
                    child: Center(child: app16spFFFText('退出登录',fontFamily: 'PingFangSC-Medium')),
                  )),
              SizedBox(
                  height: Platform.isIOS
                      ? MediaQuery.of(context).padding.bottom
                      : 20.h)
            ],
          ),
        ),
      ),
    );
  }

  // 清理缓存
  _clearCache(BuildContext context) async {
    debugPrint("清理缓存");
    try {
      final _tempDir = await getTemporaryDirectory();
      Loading.show();
      await CacheUtils.requestPermission(
        _tempDir,
        context,
      );
      Loading.dismiss();
      _getCacheSize();
    } catch (err) {
      Loading.dismiss();
      Toast.show('清除失败');
    }
  }

  Widget _settingItemOnTap({index, onTap, text}) {
    return GestureDetector(
        onTap: onTap,
        child: Container(
          color: AppColors.white,
          width: double.infinity,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              index == null
                  ? app16spA1Text(text,fontFamily: 'PingFangSC-Regular').paddingOnly(left: 16.w)
                  : app16spA1Text(text,fontFamily: 'PingFangSC-Regular'),
              // index==settingItem.length-1?Expanded(child: Align(alignment: Alignment.centerRight,child: app14sp4e4eText('通用拍摄').paddingOnly(right: 20.w))):Container(),
              index == 0
                  ? app14sp4e4eText(CacheUtils.renderSize(cache),fontFamily: 'PingFangSC-Regular')
                      .paddingOnly(right: 16.w)
                  : index == 7
                      ? app14sp4e4eText('v$version',fontFamily: 'PingFangSC-Regular').paddingOnly(right: 16.w)
                      : assImg2(img: 'list_icon_more', w: 16.w)
                          .paddingOnly(right: 16.w)
            ],
          ),
        ));
  }

  Future<void> _userLogout() async {
    try {
      await PreferencesService().setString('useTopicType', '');
      await PreferencesService().setInt('useDivision', 0);
      final loginOut = await userRepository.loginOut();
      ITools.get().clear();
    } catch (e) {
      print(e);
    }

    // await Get.find<ExaminationController>(tag: ExaminationPageState.uniqueKey.toString()).userSubject?.cancel();
    print("-------ExaminationPage initState cancel userSubject-------");
    JumpPageUntil.jumpHomeAndCloseAll();
    // Get.offAllNamed(AppRoutes.home);
  }
}

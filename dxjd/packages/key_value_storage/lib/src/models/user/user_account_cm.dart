import 'package:hive/hive.dart';

part 'user_account_cm.g.dart';

@HiveType(typeId: 33)
class UserAccountCM extends HiveObject {
  // UserAccountCM(this.UserId, this.Name, this.Mobile, this.Email, this.NickName,
  //     this.Image, this.Location, this.CreateTime);

  @HiveField(0)
  final String accountUid;
  @HiveField(1)
  final String? accountSid;
  @HiveField(2)
  final int? birth;
  @HiveField(3)
  final String? name;
  @HiveField(4)
  final String? image;
  @HiveField(5)
  final int? gender;
  @HiveField(6)
  final int? isBind;
  @HiveField(7)
  final String? platSchoolId;
  @HiveField(8)
  final String? platSchoolName;
  @HiveField(9)
  final String? platTrainType;
  @HiveField(10)
  final String? topicType;
  @HiveField(11)
  final String? idCard;
  @HiveField(12)
  final String? bindMobile;
  @HiveField(13)
  final String? mobile;
  @HiveField(14)
  final int? realAuthStatus;
  @HiveField(15)
  final int? registerDivision;
  @HiveField(16)
  final int? isRecorded;
  @HiveField(17)
  final int? isVip;
  @HiveField(18)
  final int? platCode;
  @HiveField(19)
  final int? platRegisterDate;
  @HiveField(20)
  final int? registerDate;
  @HiveField(21)
  final int? vip1ExpireValue;
  @HiveField(22)
  final int? vip2ExpireValue;
  @HiveField(23)
  final int? vip3ExpireValue;
  @HiveField(24)
  final int? vip4ExpireValue;
  @HiveField(25)
   final int? platGradStatus;

  UserAccountCM(
      {required this.accountUid,
      required this.accountSid,
      required this.birth,
      required this.name,
      required this.image,
      required this.gender,
      required this.isBind,
      required this.platSchoolId,
      required this.platSchoolName,
      required this.platTrainType,
      required this.topicType,
      required this.idCard,
      required this.bindMobile,
      required this.mobile,
      required this.realAuthStatus,
      required this.registerDivision,
      required this.isRecorded,
      required this.isVip,
      required this.platCode,
      required this.platRegisterDate,
      required this.registerDate,
      required this.vip1ExpireValue,
      required this.vip2ExpireValue,
      required this.vip3ExpireValue,
      required this.vip4ExpireValue,
      required this.platGradStatus});
}

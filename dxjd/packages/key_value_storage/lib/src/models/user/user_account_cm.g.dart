// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_account_cm.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserAccountCMAdapter extends TypeAdapter<UserAccountCM> {
  @override
  final int typeId = 33;

  @override
  UserAccountCM read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserAccountCM(
      accountUid: fields[0] as String,
      accountSid: fields[1] as String?,
      birth: fields[2] as int?,
      name: fields[3] as String?,
      image: fields[4] as String?,
      gender: fields[5] as int?,
      isBind: fields[6] as int?,
      platSchoolId: fields[7] as String?,
      platSchoolName: fields[8] as String?,
      platTrainType: fields[9] as String?,
      topicType: fields[10] as String?,
      idCard: fields[11] as String?,
      bindMobile: fields[12] as String?,
      mobile: fields[13] as String?,
      realAuthStatus: fields[14] as int?,
      registerDivision: fields[15] as int?,
      isRecorded: fields[16] as int?,
      isVip: fields[17] as int?,
      platCode: fields[18] as int?,
      platRegisterDate: fields[19] as int?,
      registerDate: fields[20] as int?,
      vip1ExpireValue: fields[21] as int?,
      vip2ExpireValue: fields[22] as int?,
      vip3ExpireValue: fields[23] as int?,
      vip4ExpireValue: fields[24] as int?,
      platGradStatus: fields[25] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, UserAccountCM obj) {
    writer
      ..writeByte(26)
      ..writeByte(0)
      ..write(obj.accountUid)
      ..writeByte(1)
      ..write(obj.accountSid)
      ..writeByte(2)
      ..write(obj.birth)
      ..writeByte(3)
      ..write(obj.name)
      ..writeByte(4)
      ..write(obj.image)
      ..writeByte(5)
      ..write(obj.gender)
      ..writeByte(6)
      ..write(obj.isBind)
      ..writeByte(7)
      ..write(obj.platSchoolId)
      ..writeByte(8)
      ..write(obj.platSchoolName)
      ..writeByte(9)
      ..write(obj.platTrainType)
      ..writeByte(10)
      ..write(obj.topicType)
      ..writeByte(11)
      ..write(obj.idCard)
      ..writeByte(12)
      ..write(obj.bindMobile)
      ..writeByte(13)
      ..write(obj.mobile)
      ..writeByte(14)
      ..write(obj.realAuthStatus)
      ..writeByte(15)
      ..write(obj.registerDivision)
      ..writeByte(16)
      ..write(obj.isRecorded)
      ..writeByte(17)
      ..write(obj.isVip)
      ..writeByte(18)
      ..write(obj.platCode)
      ..writeByte(19)
      ..write(obj.platRegisterDate)
      ..writeByte(20)
      ..write(obj.registerDate)
      ..writeByte(21)
      ..write(obj.vip1ExpireValue)
      ..writeByte(22)
      ..write(obj.vip2ExpireValue)
      ..writeByte(23)
      ..write(obj.vip3ExpireValue)
      ..writeByte(24)
      ..write(obj.vip4ExpireValue)
      ..writeByte(25)
      ..write(obj.platGradStatus);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAccountCMAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

import 'dart:async';

import 'package:common_utils/common_utils.dart';
import 'package:component_library/component_library.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:login/login.dart';
import 'package:quiz/quiz.dart';
import 'package:quiz/src/bean/knowledge.dart';
import 'package:quiz/src/home/<USER>';
import 'package:tools/tools.dart';
import 'vip_course.dart';

StreamSubscription? _ibus;

/**
 * 类：学习计划
 */
class StudyPlan extends StatefulWidget {
  StudyPlan({Key? key, this.subject, this.examDate}) : super(key: key);

  final int? subject;
  final DateTime? examDate;

  @override
  _StudyPlanState createState() => _StudyPlanState();
}

class _StudyPlanState extends State<StudyPlan> {
  DateTime? examDate;
  int total = 0; //题目总数
  int done = 0; //做题已做题数
  int wrong = 0; //做题错题数
  int rectify = 0; //错题正确数

  int exam_count = 0; //模拟考试次数
  int exam_average = 0; //模拟考试-近两次平均分
  int exam_pass = 0; //模拟考试-通过次数

  int real_count = 0; //真题考试次数
  int real_average = 0; //真题考试-近两次平均分
  int real_pass = 0; //真题考试-通过次数

  String adSpaceId7 = '';
  String adSpaceId8 = '';
  String adSpaceId9 = '';

  bool isVip = false;

  @override
  void initState() {
    super.initState();
    isVip = ITools.get().isVip(widget.subject!);
    // isVip = true;
    _ibus = IEventBus.get().register<IEvent>((event) {
      if (JkKey.EVENT_REFRESH_QUIZ == event.type) {
        refreshPlanQuiz(true);
        Logs.e("StudyPlan 刷新做题记录");
      } else if (JkKey.EVENT_REFRESH_EXAM == event.type) {
        refreshPlanQuiz(false);
        refreshPlanExam(true);
        Logs.e("StudyPlan 刷新考试记录");
      } else if (JkKey.EVENT_REFRESH_FIXED == event.type ||
          JkKey.EVENT_REFRESH_ESSENCE == event.type ||
          JkKey.EVENT_CLOSE_BUY == event.type) {
        refreshUI();
      } else if (JkKey.EVENT_REFRESH_KNOWLEDGE_RATE == event.type) {
        refreshKnowledgeRate();
      }
    });
    refreshPlanQuiz(false);
    refreshPlanExam(false);
    refreshKnowledge();

    examDate = widget.examDate ?? getAfterDays(7);

    // for (int i = 0; i < AdsData.get().ads7.length; i++) {
    //   adSpaceId7 = AdsData.get().ads7[i].id;
    // }
    // for (int i = 0; i < AdsData.get().ads8.length; i++) {
    //   adSpaceId8 = AdsData.get().ads8[i].id;
    // }
    // for (int i = 0; i < AdsData.get().ads9.length; i++) {
    //   adSpaceId9 = AdsData.get().ads9[i].id;
    // }
    Logs.e(
        "StudyPlan fixed_count = ${FixedData.get().fixed_count}, fixed_total = ${FixedData.get().fixed_total}, fixed_done = ${FixedData.get().fixed_done}, essence_count = ${EssenceData.get().essence_count}, essence_pass = ${EssenceData.get().essence_pass}");
  }

  static DateTime getAfterDays(int day) {
    DateTime now = DateTime.now();
    return now.add(Duration(days: day));
  }

  @override
  void dispose() {
    IEventBus.get().unregister(_ibus);
    super.dispose();
  }

  void refreshUI() async {
    try {
      if (mounted) {
        setState(() {});
      }
    } catch (e) {}
  }

  void refreshPlanQuiz(bool refresh) async {
    if (widget.subject == 4) {
      if (IHome.get().quiz4 != null) {
        total = IHome.get().quiz4!.total;
        done = IHome.get().quiz4!.done;
        wrong = IHome.get().quiz4!.wrong;
        rectify = IHome.get().quiz4!.rectify;
      } else {
        total = 0;
        done = 0;
        wrong = 0;
        rectify = 0;
      }
    } else {
      if (IHome.get().quiz1 != null) {
        total = IHome.get().quiz1!.total;
        done = IHome.get().quiz1!.done;
        wrong = IHome.get().quiz1!.wrong;
        rectify = IHome.get().quiz1!.rectify;
      } else {
        total = 0;
        done = 0;
        wrong = 0;
        rectify = 0;
      }
    }
    if (refresh) {
      refreshUI();
    }
  }

  void refreshPlanExam(bool refresh) async {
    if (widget.subject == 4) {
      if (IHome.get().planExam4 != null) {
        exam_count = IHome.get().planExam4!.exam_count;
        exam_average = IHome.get().planExam4!.exam_average;
        exam_pass = IHome.get().planExam4!.exam_pass;
        real_count = IHome.get().planExam4!.real_count;
        real_average = IHome.get().planExam4!.real_average;
        real_pass = IHome.get().planExam4!.real_pass;
      } else {
        exam_count = 0;
        exam_average = 0;
        exam_pass = 0;
        real_count = 0;
        real_average = 0;
        real_pass = 0;
      }
    } else {
      if (IHome.get().planExam1 != null) {
        exam_count = IHome.get().planExam1!.exam_count;
        exam_average = IHome.get().planExam1!.exam_average;
        exam_pass = IHome.get().planExam1!.exam_pass;
        real_count = IHome.get().planExam1!.real_count;
        real_average = IHome.get().planExam1!.real_average;
        real_pass = IHome.get().planExam1!.real_pass;
      } else {
        exam_count = 0;
        exam_average = 0;
        exam_pass = 0;
        real_count = 0;
        real_average = 0;
        real_pass = 0;
      }
    }
    if (refresh) {
      refreshUI();
    }
  }

  List<KnowledgeMo> KnowledgeArr = <KnowledgeMo>[];
  Future<void> refreshKnowledge() async {
    try {
      final result = await HttpDao.get()
          .getKnowledge(examType: widget.subject!, type: JkKey.KNOWPOINT);
      KnowledgeArr = (result as List<dynamic>)
          .map((e) => KnowledgeMo.fromJson(e))
          .toList();
      //
      refreshKnowledgeRate();
    } catch (e) {
      Logs.e('refreshKnowledge error: $e');
    }
  }

  Future<void> refreshKnowledgeRate() async {
    for (KnowledgeMo mo in KnowledgeArr) {
      try {
        List<QuizMo> quizList = await QuizUtils.get().getFilterQuiz(
          mo.subIds ?? [],
          ITools.get().getUid('0'),
          JkKey.KEY_KNOWLEDGE,
          ITools.get().isLogin,
          false,
        );
        int doNum = 0;
        int errorNum = 0;
        for (QuizMo quiz in quizList) {
          doNum = doNum + quiz.correctcount + quiz.errorcount;
          errorNum = errorNum + quiz.errorcount;
        }
        if (doNum > 0) {
          double rate = (errorNum / doNum) * 100;
          int rateInt = rate.round();
          mo.errorRate = rateInt.toDouble() / 100;
        }
      } catch (e) {
        Logs.e('refreshKnowledgeRate error: $e');
      }
    }
    refreshUI();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: Size(375, 812));
    return UIUtils.statusBarDarkWidget(Scaffold(
      backgroundColor: Color(0xffF7F8FB),
      resizeToAvoidBottomInset: false,
      body: WillPopScope(
          child: Stack(
            children: <Widget>[
              // Image.asset(
              //   'assets/home_img/ic_plan_bg.png',
              //   width: double.infinity,
              //   height: double.infinity,
              //   fit: BoxFit.cover,
              // ),
              Column(
                children: <Widget>[
                  Stack(
                    children: <Widget>[
                      // Image.asset(
                      //   'assets/home_img/ic_plan_header.png',
                      //   width: double.infinity,
                      //   fit: BoxFit.fitWidth,
                      // ),
                      isVip ? Container(
                        decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Color(0xff1B1718), Color(0xff373536), Color(0xff272425)],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,),
                            image: DecorationImage(
                              image: AssetImage('assets/home_img/genwoxue_vip_header_bg.png'),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.all(Radius.circular(UIUtils.dp(10)))
                        ),
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(56) + UIUtils.statusHeight,
                            left: UIUtils.dp(11),
                            right: UIUtils.dp(11)),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: <Widget>[
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: <Widget>[
                                      Stack(
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.only(bottom: 5.0.h),
                                            child: Container(
                                              margin: EdgeInsets.only(top: UIUtils.dp(11), left: UIUtils.dp(11)),
                                              padding: EdgeInsets.only(
                                                  top: UIUtils.dp(1.5),
                                                  bottom: UIUtils.dp(1.5),
                                                  left: UIUtils.dp(1.5),
                                                  right: UIUtils.dp(1.5)),
                                              decoration: BoxDecoration(
                                                color: Color(0xffF0AB5C),
                                                borderRadius: BorderRadius.all(Radius.circular(50)),
                                              ),
                                              child: INetworkImage(
                                                url: ITools.get().getImage(),
                                                radius: UIUtils.dp(25),
                                                width: UIUtils.dp(50),
                                                height: UIUtils.dp(50),
                                                def: 'assets/home_img/ic_header.png',
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            right: 0,
                                            left: 10.w,
                                            bottom: 0,
                                            child:
                                            assImg2(img: 'icon_my_vip',w: 45.w,h: 19.h),
                                          ),
                                        ],
                                      ),
                                      Container(
                                        margin: EdgeInsets.only(
                                            left: UIUtils.dp(10)),
                                        child: Column(
                                          crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: <Widget>[
                                            Text(
                                              ITools.get().getName('驾考学员'),
                                              style:
                                              TextStyle(color: Color(0xffE2AE73), fontSize: 14,fontFamily: 'PingFangSC-Semibold'),
                                            ),
                                            Container(
                                              margin: EdgeInsets.only(
                                                  top: UIUtils.dp(7)),
                                              child: Text(
                                                '已为您定制专属',
                                                style:
                                                TextStyle(color: Color(0xffF7DDC0), fontSize: 12,fontFamily: 'PingFangSC-Semibold'),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Container(
                                        margin: EdgeInsets.only(
                                            top: UIUtils.dp(19),left: UIUtils.dp(6)),
                                        width: UIUtils.dp(65),
                                        height: UIUtils.dp(22),
                                        alignment: AlignmentDirectional.center,
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                                colors: [Color(0xffF0AE56), Color(0xffDEAA6B)],
                                                begin: Alignment.centerLeft,
                                                end: Alignment.centerRight,),
                                            borderRadius: BorderRadius.all(Radius.circular(UIUtils.dp(11)))
                                        ),
                                        child: Text(
                                            '学习计划',
                                            style: TextStyle(color: Color(0xff663409), fontSize: 12,fontFamily: 'PingFangSC-Semibold')
                                        ),
                                      ),
                                    ],
                                  ),
                                  Container(
                                    margin:
                                    EdgeInsets.only(top: UIUtils.dp(16),bottom: UIUtils.dp(13),left: UIUtils.dp(11)),
                                    child: Text.rich(
                                      TextSpan(
                                        children: [
                                          TextSpan(
                                            text: '终极目标：',
                                            style:
                                            TextStyle(color: Color(0xffF7DDC0), fontSize: 14,fontFamily: 'PingFangSC-Semibold'),
                                          ),
                                          TextSpan(
                                            text:
                                            '${widget.subject == 4 ? '科四' : '科一'}正式考试＞90分',
                                            style:
                                            TextStyle(color: Color(0xffF7DDC0), fontSize: 14,fontFamily: 'PingFangSC-Semibold'),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: UIUtils.dp(1),top: UIUtils.dp(7),right: UIUtils.dp(10)),
                              child: InkWell(
                                child: Stack(
                                  children: <Widget>[
                                    Container(
                                      margin: EdgeInsets.only(
                                          bottom: UIUtils.dp(6),
                                          right: UIUtils.dp(4)),
                                      width: UIUtils.dp(86),
                                      decoration: Style.plan_box_blue_r6,
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: <Widget>[
                                          Container(
                                            width: UIUtils.dp(86),
                                            height: UIUtils.dp(30),
                                            decoration:
                                                BoxDecoration(
                                                  gradient: LinearGradient(
                                                    colors: [Color(0xffEFAE57), Color(0xffDFAA6A)],
                                                    begin: Alignment.centerLeft,
                                                    end: Alignment.centerRight,),
                                                  borderRadius: BorderRadius.only(
                                                    topLeft: Radius.circular(6),
                                                    topRight: Radius.circular(6),
                                                  ),
                                                ),
                                            alignment:
                                            AlignmentDirectional.center,
                                            child: Text(
                                              '考试日期',
                                              style:
                                              TextStyle(color: Color(0xff663409), fontSize: 14,fontFamily: 'PingFangSC-Semibold'),
                                            ),
                                          ),
                                          Container(
                                            height: UIUtils.dp(28),
                                            padding: EdgeInsets.only(
                                                top: UIUtils.dp(5)),
                                            child: Text(
                                              '${DateUtil.formatDate(examDate, format: DateFormats.zh_mo_d)}',
                                              style:
                                              TextStyle(color: Color(0xff663409), fontSize: 14,fontFamily: 'PingFangSC-Semibold'),
                                            ),
                                          ),
                                          // Container(
                                          //   margin: EdgeInsets.only(
                                          //       top: UIUtils.dp(2),
                                          //       bottom: UIUtils.dp(4),
                                          //       left: UIUtils.dp(5),
                                          //       right: UIUtils.dp(5)),
                                          //   child: Text(
                                          //     '准确填写考试日期学习计划更精准',
                                          //     style: Style.plan_text_8,
                                          //     textAlign: TextAlign.center,
                                          //   ),
                                          // ),
                                        ],
                                      ),
                                    ),
                                    // Positioned(
                                    //   right: 0,
                                    //   bottom: 0,
                                    //   child: Image.asset(
                                    //     'assets/home_img/ic_plan_pointer.png',
                                    //     width: UIUtils.dp(14),
                                    //     fit: BoxFit.fitWidth,
                                    //   ),
                                    // ),
                                  ],
                                ),
                                onTap: () {
                                  DialogHelper.showDialogDatePicker(
                                      context, examDate, (DateTime? date) {
                                    if (date != null) {
                                      examDate = date;
                                      Storage.setString(
                                          Storage.examDate +
                                              ITools.get().getUid('0'),
                                          DateUtil.formatDate(date));
                                      refreshUI();
                                    }
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      ): Container(
                        decoration: BoxDecoration(
                            gradient: LinearGradient(
                                colors: [Color(0xff1B92EE), Color(0xff5BB2F7)],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,),
                          borderRadius: BorderRadius.all(Radius.circular(UIUtils.dp(10)))
                        ),
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(56) + UIUtils.statusHeight,
                            left: UIUtils.dp(11),
                            right: UIUtils.dp(11)),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: <Widget>[
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: <Widget>[
                                      Container(
                                        margin: EdgeInsets.only(top: UIUtils.dp(11), left: UIUtils.dp(11)),
                                        padding: EdgeInsets.only(
                                            top: UIUtils.dp(1),
                                            bottom: UIUtils.dp(1),
                                            left: UIUtils.dp(1),
                                            right: UIUtils.dp(1)),
                                        decoration: BoxDecoration(
                                            color: Color(0xff8ECCFF),
                                          borderRadius: BorderRadius.all(Radius.circular(50)),
                                        ),
                                        child: INetworkImage(
                                          url: ITools.get().getImage(),
                                          radius: UIUtils.dp(25),
                                          width: UIUtils.dp(50),
                                          height: UIUtils.dp(50),
                                          def: 'assets/home_img/ic_header.png',
                                        ),
                                      ),
                                      Container(
                                        margin: EdgeInsets.only(
                                            left: UIUtils.dp(10)),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: <Widget>[
                                            Text(
                                              ITools.get().getName('驾考学员'),
                                              style:
                                                  TextStyle(color: Colors.white, fontSize: 14,fontFamily: 'PingFangSC-Semibold'),
                                            ),
                                            Container(
                                              margin: EdgeInsets.only(
                                                  top: UIUtils.dp(7)),
                                              child: Text(
                                                '已为您定制专属',
                                                style:
                                                TextStyle(color: Colors.white, fontSize: 12,fontFamily: 'PingFangSC-Semibold'),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Container(
                                        margin: EdgeInsets.only(
                                            top: UIUtils.dp(19),left: UIUtils.dp(6)),
                                        width: UIUtils.dp(65),
                                        height: UIUtils.dp(22),
                                        alignment: AlignmentDirectional.center,
                                        decoration: Style.plan_box_blue_h_r50,
                                        child: Text(
                                          '学习计划',
                                          style: TextStyle(color: Color(0xff136EB7), fontSize: 12,fontFamily: 'PingFangSC-Semibold')
                                        ),
                                      ),
                                    ],
                                  ),
                                  Container(
                                    margin:
                                        EdgeInsets.only(top: UIUtils.dp(16),bottom: UIUtils.dp(13),left: UIUtils.dp(11)),
                                    child: Text.rich(
                                      TextSpan(
                                        children: [
                                          TextSpan(
                                            text: '终极目标：',
                                            style:
                                            TextStyle(color: Colors.white, fontSize: 14,fontFamily: 'PingFangSC-Semibold'),
                                          ),
                                          TextSpan(
                                            text:
                                                '${widget.subject == 4 ? '科四' : '科一'}正式考试＞90分',
                                            style:
                                            TextStyle(color: Colors.white, fontSize: 14,fontFamily: 'PingFangSC-Semibold'),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: UIUtils.dp(1),top: UIUtils.dp(7),right: UIUtils.dp(10)),
                              child: InkWell(
                                child: Stack(
                                  children: <Widget>[
                                    Container(
                                      margin: EdgeInsets.only(
                                          bottom: UIUtils.dp(6),
                                          right: UIUtils.dp(4)),
                                      width: UIUtils.dp(86),
                                      decoration: Style.plan_box_blue_r6,
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: <Widget>[
                                          Container(
                                            width: UIUtils.dp(86),
                                            height: UIUtils.dp(30),
                                            decoration:
                                                Style.plan_box_blue_h_ab_r6,
                                            alignment:
                                                AlignmentDirectional.center,
                                            child: Text(
                                              '考试日期',
                                              style:
                                              TextStyle(color: Colors.white, fontSize: 14,fontFamily: 'PingFangSC-Semibold'),
                                            ),
                                          ),
                                          Container(
                                            height: UIUtils.dp(28),
                                            padding: EdgeInsets.only(
                                                top: UIUtils.dp(5)),
                                            child: Text(
                                              '${DateUtil.formatDate(examDate, format: DateFormats.zh_mo_d)}',
                                              style:
                                              TextStyle(color: Colors.black, fontSize: 14,fontFamily: 'PingFangSC-Semibold'),
                                            ),
                                          ),
                                          // Container(
                                          //   margin: EdgeInsets.only(
                                          //       top: UIUtils.dp(2),
                                          //       bottom: UIUtils.dp(4),
                                          //       left: UIUtils.dp(5),
                                          //       right: UIUtils.dp(5)),
                                          //   child: Text(
                                          //     '准确填写考试日期学习计划更精准',
                                          //     style: Style.plan_text_8,
                                          //     textAlign: TextAlign.center,
                                          //   ),
                                          // ),
                                        ],
                                      ),
                                    ),
                                    // Positioned(
                                    //   right: 0,
                                    //   bottom: 0,
                                    //   child: Image.asset(
                                    //     'assets/home_img/ic_plan_pointer.png',
                                    //     width: UIUtils.dp(14),
                                    //     fit: BoxFit.fitWidth,
                                    //   ),
                                    // ),
                                  ],
                                ),
                                onTap: () {
                                  DialogHelper.showDialogDatePicker(
                                      context, examDate, (DateTime? date) {
                                    if (date != null) {
                                      examDate = date;
                                      Storage.setString(
                                          Storage.examDate +
                                              ITools.get().getUid('0'),
                                          DateUtil.formatDate(date));
                                      refreshUI();
                                    }
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Expanded(
                    child: XScrollView(
                      child: Stack(
                        key: UniqueKey(),
                        children: <Widget>[
                          // Positioned.fill(
                          //     child: Image.asset('assets/home_img/ic_plan_bbk.png',
                          //         fit: BoxFit.fill)),
                          Column(
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              Container(
                                // constraints: BoxConstraints(
                                //   minHeight: UIUtils.dp(53),
                                // ),
                                margin: EdgeInsets.only(right: UIUtils.dp(11), bottom: UIUtils.dp(12), top: UIUtils.dp(11)),
                                alignment: AlignmentDirectional.centerEnd,
                                child: Text(
                                  '准确填写考试日期学习计划更精准',
                                  style:
                                  TextStyle(color: Color(0xffB2B2B2), fontSize: 12,fontFamily: 'PingFangSC-Medium'),
                                ),
                              ),
                              if (!isVip)
                                Container(
                                  margin: EdgeInsets.only(
                                      left: UIUtils.dp(11),
                                      right: UIUtils.dp(11)),
                                  decoration: Style.plan_border_bg_r15,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: <Widget>[
                                      Container(
                                        margin:
                                            EdgeInsets.only(top: UIUtils.dp(11),left: UIUtils.dp(11)),
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.end,
                                          children: <Widget>[
                                            Container(
                                              // margin: EdgeInsets.only(
                                              //     left: UIUtils.dp(16)),
                                              width: UIUtils.dp(72),
                                              height: UIUtils.dp(24),
                                              alignment:
                                                  AlignmentDirectional.center,
                                              // decoration: Style.plan_box_bg_r50,
                                              child: Text(
                                                '普通方案',
                                                style:
                                                TextStyle(color: Colors.black, fontSize: 16,fontFamily: 'PingFangSC-Semibold'),
                                              ),
                                            ),
                                            const Expanded(
                                              child: SizedBox(),
                                            ),
                                            Container(
                                              margin: EdgeInsets.only(
                                                  right: UIUtils.dp(14)),
                                              child: Row(
                                                children: [
                                                  app14sp268Text('预计耗时：',color: Colors.black,fontFamily: 'PingFangSC-Semibold'),
                                                  Container(
                                                    width: UIUtils.dp(70),
                                                    height: UIUtils.dp(25),
                                                    decoration: BoxDecoration(
                                                      gradient: LinearGradient(
                                                        colors: [
                                                          Color(0xff2397F0),
                                                          Color(0xff5BB0F5)
                                                        ],
                                                        begin: Alignment.centerLeft,
                                                        end: Alignment.centerRight,
                                                      ),
                                                      borderRadius:
                                                          BorderRadius.all(Radius.circular(UIUtils.dp(12))),
                                                    ),
                                                    alignment:
                                                        AlignmentDirectional.center,
                                                    child:
                                                    app14sp268Text('12天',color: Colors.white,fontFamily: 'PingFangSC-Semibold'),
                                                  )
                                                ],
                                              )
                                            ),
                                          ],
                                        ),
                                      ),
                                      Container(
                                        key: UniqueKey(),
                                        margin: EdgeInsets.only(
                                            top: UIUtils.dp(10),
                                            bottom: UIUtils.dp(16),
                                            left: UIUtils.dp(7.5),
                                            right: UIUtils.dp(1.5)),
                                        child: SingleChildScrollView(
                                          // physics: NeverScrollableScrollPhysics(),
                                          scrollDirection: Axis.horizontal,
                                          child: Container(
                                            // height: UIUtils.dp(100),
                                            // width: MediaQuery.of(context).size.width,
                                            child: Row(
                                              children: <Widget>[
                                                //顺序练习
                                                normalUnit(
                                                    1,
                                                    '刷完题库\n${total}题',
                                                    '已做过${done}',
                                                    Math.divided(done * 100, total),
                                                    '去完成',
                                                    done > 0 && done == total, () {

                                                  JumpUtils.get()
                                                      .jumpChapter(widget.subject!);
                                                }),
                                                //vip课程
                                                normalUnit(
                                                    2,
                                                    '普通模考\n稳定＞93分',
                                                    '最近两次模拟考\n平均分：${exam_count == 0 ? '无记录' : exam_average}',
                                                    0,
                                                    '去完成',
                                                    exam_count >= 2 &&
                                                        exam_pass >= 2, () {
                                                  /*
                                                  IPlatform.get().jumpMock(
                                                      JkKey.KEY_MOCK,
                                                      widget.subject!);
                                                      */

                                                  JumpUtils.get().jumpMockExercise(
                                                      JkKey.KEY_MOCK,
                                                      widget.subject!);
                                                }),
                                                //模拟考试
                                                normalUnit(
                                                    3,
                                                    '掌握\n${Math.divided(rectify * 100, wrong).round()}%错题',
                                                    '当前错题：${wrong}\n错题最近正确：${rectify}',
                                                    0,
                                                    '去看看',
                                                    wrong > 0 && wrong == rectify,
                                                    () {

                                                  JumpUtils.get().jumpFolder(
                                                    widget.subject!,
                                                    JkKey.KEY_ERROR,
                                                  );
                                                }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              if (!isVip)
                                Container(
                                  margin: EdgeInsets.symmetric(horizontal: UIUtils.dp(11),vertical: UIUtils.dp(11)),
                                  alignment: AlignmentDirectional.center,
                                  child: Image.asset(
                                    'assets/home_img/ic_plan_vs.png',
                                    // width: UIUtils.dp(62),
                                    // fit: BoxFit.fitWidth,
                                  ),
                                ),
                              Container(
                                margin:
                                EdgeInsets.symmetric(horizontal: UIUtils.dp(11)),
                                // margin: EdgeInsets.only(
                                //     top: isVip ? 0 : 0),
                                // constraints: BoxConstraints(
                                //   minHeight:
                                //       isVip ? UIUtils.dp(426) : UIUtils.dp(232),
                                // ),
                                // decoration: CheckUtils.isVip(widget.subject!)
                                //     ? Style.plan_box1
                                //     : Style.plan_box,
                                decoration: BoxDecoration(
                                  color: Color(0xffF7DDC0),
                                  borderRadius: BorderRadius.all(Radius.circular(UIUtils.dp(10))),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: <Widget>[
                                    Container(
                                      margin:
                                      EdgeInsets.only(top: UIUtils.dp(11)),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: <Widget>[
                                          SizedBox(width: UIUtils.dp(11),),
                                          app16spAC5000Text('VIP专享进阶方案',color: Color(0xff663409),fontFamily: 'PingFangSC-Semibold'),
                                          // Text(
                                          //   'VIP专享进阶方案',
                                          //   style: Style.plan_blue_16_w500,
                                          // ),
                                          const Expanded(
                                            child: SizedBox(),
                                          ),
                                          Container(
                                            margin: EdgeInsets.only(
                                                right: UIUtils.dp(14)),
                                            child: Row(
                                              children: [
                                                app14sp268Text('预计耗时：',color: Color(0xff663409),fontFamily: 'PingFangSC-Medium'),
                                                Container(
                                                  width: UIUtils.dp(70),
                                                  height: UIUtils.dp(25),
                                                  decoration: BoxDecoration(
                                                    gradient: LinearGradient(
                                                      colors: [
                                                        Color(0xffF0AE56),
                                                        Color(0xffE1AA67)
                                                      ],
                                                      begin: Alignment.centerLeft,
                                                      end: Alignment.centerRight,
                                                    ),
                                                    borderRadius:
                                                    BorderRadius.all(Radius.circular(UIUtils.dp(12))),
                                                  ),
                                                  alignment:
                                                  AlignmentDirectional.center,
                                                  child:
                                                  app16spAC5000Text('3天',color: Color(0xff69390F),fontFamily: 'PingFangSC-Semibold'),
                                                )
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      key: UniqueKey(),
                                      margin: EdgeInsets.only(
                                          top: UIUtils.dp(3),
                                          bottom: UIUtils.dp(3.5),
                                          left: UIUtils.dp(6.5),
                                          right: UIUtils.dp(6.5)),
                                      child: SingleChildScrollView(
                                        scrollDirection: Axis.horizontal,
                                        child: Container(
                                          // height: UIUtils.dp(100),
                                          // width: MediaQuery.of(context).size.width,
                                          child: Row(
                                            children: <Widget>[
                                              //理论视频
                                              vipUnit(
                                                  1,
                                                  '节省2/3刷题时间',
                                                  '只刷精简',
                                                  '${FixedData.get().fixed_count}常考题',
                                                  '已做过${FixedData.get().fixed_done}',
                                                  Math.divided(
                                                      FixedData.get().fixed_done *
                                                          100,
                                                      FixedData.get().fixed_total),
                                                  '去完成',
                                                  FixedData.get().fixed_done > 0 &&
                                                      FixedData.get().fixed_done ==
                                                          FixedData.get()
                                                              .fixed_total, () {
                                                BuryingPointUtils.instance.addPoint(
                                                    buryingPointList: BuryingPointList(
                                                        eventType: 1,
                                                        entranceType: 32,
                                                        action: 1, browseDuration: 0)
                                                );
                                                if (ITools.get()
                                                    .isVip(widget.subject!)) {
                                                  JumpUtils.get().jumpVipCourse(
                                                      widget.subject!);
                                                } else {
                                                  JumpUtils.get().jumpBuyVip(vipType: widget.subject!,type: 'jingxuan600ti');
                                                }
                                              }),
                                              //vip课程
                                              vipUnit(
                                                  2,
                                                  '还原正式考试流程',
                                                  '真实考场模拟',
                                                  '3次93分',
                                                  '最近3次模考\n平均分：${real_count == 0 ? '无记录' : real_average}',
                                                  0,
                                                  '去完成',
                                                  real_count >= 2 && real_pass >= 2,
                                                  () {
                                                /*
                                                IPlatform.get().jumpRealExam(
                                                    widget.subject!,
                                                    adSpaceId: adSpaceId8);
                                                    */
                                                // JumpUtils.get().jumpRealExam(
                                                //     widget.subject!, () {});
                                                    BuryingPointUtils.instance.addPoint(
                                                        buryingPointList: BuryingPointList(
                                                            eventType: 1,
                                                            entranceType: 33,
                                                            action: 1, browseDuration: 0)
                                                    );
                                                JumpUtils.get()
                                                    .jumpRealHome(widget.subject!);
                                              }),
                                              //模拟考试
                                              vipUnit(
                                                  3,
                                                  '考前急救分',
                                                  '考前密卷',
                                                  '临时抱佛脚专属',
                                                  '达到93分',
                                                  Math.divided(
                                                      EssenceData.get()
                                                              .essence_pass *
                                                          100,
                                                      EssenceData.get()
                                                          .essence_count),
                                                  '去完成',
                                                  EssenceData.get().essence_pass >
                                                          0 &&
                                                      EssenceData.get()
                                                              .essence_pass ==
                                                          EssenceData.get()
                                                              .essence_count, () {
                                                BuryingPointUtils.instance.addPoint(
                                                    buryingPointList: BuryingPointList(
                                                        eventType: 1,
                                                        entranceType: 34,
                                                        action: 1, browseDuration: 0)
                                                );
                                                JumpUtils.get()
                                                    .jumpEssence(widget.subject!);
                                              }),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: UIUtils.dp(10),),
                                    assImg2(img: 'genwoxue_vip_fangan_tips',w: 283.w,h: 34.h),
                                    SizedBox(height: UIUtils.dp(11),),
                                    // if (isVip)
                                    //   Container(
                                    //     margin: EdgeInsets.only(
                                    //         top: UIUtils.dp(2.5),
                                    //         bottom: UIUtils.dp(17.5),
                                    //         left: UIUtils.dp(18),
                                    //         right: UIUtils.dp(18)),
                                    //     decoration: Style.style_box_white_r10,
                                    //     child: Column(
                                    //       mainAxisSize: MainAxisSize.min,
                                    //       children: <Widget>[
                                    //         Container(
                                    //           margin: EdgeInsets.only(
                                    //               top: UIUtils.dp(16)),
                                    //           child: Text(
                                    //             '注意事项',
                                    //             style:
                                    //                 Style.style_text1_16_bold,
                                    //           ),
                                    //         ),
                                    //         Container(
                                    //           margin: EdgeInsets.only(
                                    //               top: UIUtils.dp(10),
                                    //               left: UIUtils.dp(13),
                                    //               right: UIUtils.dp(13)),
                                    //           child: Row(
                                    //             crossAxisAlignment:
                                    //                 CrossAxisAlignment.start,
                                    //             children: <Widget>[
                                    //               Text(
                                    //                 '1.',
                                    //                 style: Style
                                    //                     .plan_text2_11_w500,
                                    //               ),
                                    //               Expanded(
                                    //                 child: Container(
                                    //                   margin: EdgeInsets.only(
                                    //                       left: UIUtils.dp(3)),
                                    //                   alignment:
                                    //                       AlignmentDirectional
                                    //                           .centerStart,
                                    //                   child: Text(
                                    //                     '鼠标别连续点，易造成连续答题或电脑死机',
                                    //                     style: Style
                                    //                         .plan_text2_11_w500,
                                    //                   ),
                                    //                 ),
                                    //               ),
                                    //             ],
                                    //           ),
                                    //         ),
                                    //         Container(
                                    //           margin: EdgeInsets.only(
                                    //               top: UIUtils.dp(8),
                                    //               left: UIUtils.dp(13),
                                    //               right: UIUtils.dp(13)),
                                    //           child: Row(
                                    //             crossAxisAlignment:
                                    //                 CrossAxisAlignment.start,
                                    //             children: <Widget>[
                                    //               Text(
                                    //                 '2.',
                                    //                 style: Style
                                    //                     .plan_text2_11_w500,
                                    //               ),
                                    //               Expanded(
                                    //                 child: Container(
                                    //                   margin: EdgeInsets.only(
                                    //                       left: UIUtils.dp(3)),
                                    //                   alignment:
                                    //                       AlignmentDirectional
                                    //                           .centerStart,
                                    //                   child: Text(
                                    //                     '调整摄像头或者座椅，确保面部清晰地显示在电脑左上角的对话框中，考试过程中会随时抓拍照片，不要捂嘴、托腮等遮挡面部',
                                    //                     style: Style
                                    //                         .plan_text2_11_w500,
                                    //                   ),
                                    //                 ),
                                    //               ),
                                    //             ],
                                    //           ),
                                    //         ),
                                    //         Container(
                                    //           margin: EdgeInsets.only(
                                    //               top: UIUtils.dp(8),
                                    //               bottom: UIUtils.dp(16),
                                    //               left: UIUtils.dp(13),
                                    //               right: UIUtils.dp(13)),
                                    //           child: Row(
                                    //             crossAxisAlignment:
                                    //                 CrossAxisAlignment.start,
                                    //             children: <Widget>[
                                    //               Text(
                                    //                 '3.',
                                    //                 style: Style
                                    //                     .plan_text2_11_w500,
                                    //               ),
                                    //               Expanded(
                                    //                 child: Container(
                                    //                   margin: EdgeInsets.only(
                                    //                       left: UIUtils.dp(3)),
                                    //                   alignment:
                                    //                       AlignmentDirectional
                                    //                           .centerStart,
                                    //                   child: Text(
                                    //                     '答题遇到电脑卡顿，试题不出现、交卷无反应、下一题点不动等情况，不要随意重启电脑，举手示意请工作人员及时处理',
                                    //                     style: Style
                                    //                         .plan_text2_11_w500,
                                    //                   ),
                                    //                 ),
                                    //               ),
                                    //             ],
                                    //           ),
                                    //         ),
                                    //       ],
                                    //     ),
                                    //   ),
                                  ],
                                ),
                              ),
                              AnalyzeWidget(),
                            ],
                          ),
                          // if (!isVip)
                          //   Positioned(
                          //     right: UIUtils.dp(28),
                          //     top: UIUtils.dp(31.5),
                          //     child: Image.asset(
                          //       'assets/home_img/ic_plan_tag.png',
                          //       width: UIUtils.dp(47.5),
                          //       fit: BoxFit.fitWidth,
                          //     ),
                          //   ),
                          // if (!isVip)
                          //   Positioned(
                          //     left: 0,
                          //     right: 0,
                          //     // top: UIUtils.dp(202),
                          //     child: Container(
                          //       alignment: AlignmentDirectional.center,
                          //       child: Image.asset(
                          //         'assets/home_img/ic_plan_vs.png',
                          //         width: UIUtils.dp(62),
                          //         fit: BoxFit.fitWidth,
                          //       ),
                          //     ),
                          //   ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              UITitle(
                title: '科一专属备考计划',
                type: 'white_null',
                onTap: (String? type) {
                  if (type == 'left') {
                    finish();
                  }
                },
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: BottomBuyWidget(),
              )
            ],
          ),
          onWillPop: () async {
            //这里可以响应物理返回键
            finish();
            return false;
          }),
    ));
  }

  void finish() {
    Navigator.of(context).pop();
  }

  Widget normalUnit(int index, String text, String desc, double progress,
      String btn, bool state, Function()? onTap) {
    return Container(
      margin: EdgeInsets.only(left: UIUtils.dp(5.5), right: UIUtils.dp(5.5)),
      width: UIUtils.dp(146),
      height: UIUtils.dp(135),
      decoration: BoxDecoration(
        color: Color(0xffF7F8FB),
        borderRadius: BorderRadius.all(Radius.circular(UIUtils.dp(12))),
      ),
      child: JkInkWell(
        child: Stack(
          children: <Widget>[
            Container(
              margin: EdgeInsets.only(
                  bottom: UIUtils.dp(5), right: UIUtils.dp(4)),
              constraints: BoxConstraints(
                minHeight: UIUtils.dp(97),
                // minWidth: UIUtils.dp(146),
              ),
              // decoration: Style.plan_border_blue_r7,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    margin: EdgeInsets.only(
                        top: UIUtils.dp(8),
                        left: UIUtils.dp(8),
                        right: UIUtils.dp(6)),
                    alignment: AlignmentDirectional.center,
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          child: Container(
                            alignment: AlignmentDirectional.centerStart,
                            child: Text(
                              text,
                              style: Style.style_text6_8_5_bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: UIUtils.dp(9),
                        left: UIUtils.dp(8),
                        right: UIUtils.dp(6)),
                    alignment: AlignmentDirectional.centerStart,
                    child: Text(
                      desc,
                      style: Style.plan_text1_7,
                    ),
                  ),
                  if (index == 1)
                    Container(
                      margin: EdgeInsets.only(
                          top: UIUtils.dp(4),
                          left: UIUtils.dp(8),
                          right: UIUtils.dp(8)),
                      child: Progress(
                        progress: progress,
                        max: 100,
                        width:
                        // (UIUtils.width - UIUtils.dp(45)) / 3 -
                            UIUtils.dp(126),
                        height: UIUtils.dp(5),
                        radius: UIUtils.dp(4),
                        bg: Color(0xFFEAEAEA),
                        speed: Color(0xFF349FF2),
                      ),
                    ),
                ],
              ),
            ),
            Positioned(
              left: UIUtils.dp(8),
              bottom: UIUtils.dp(11),
              child: Container(
                width: UIUtils.dp(70),
                height: UIUtils.dp(25),
                alignment: AlignmentDirectional.center,
                decoration: Style.plan_box_blue1_r50,
                child: 
                    app14sp268Text(btn,color: Colors.white,fontFamily: 'PingFangSC-Semibold')
              ),
            ),
            Positioned(
              right: UIUtils.dp(11),
              top: 0,
              child: Image.asset(
                state
                    ? 'assets/home_img/ic_plan_completed.png'
                    : 'assets/home_img/ic_plan_incomplete.png',
                width: UIUtils.dp(22),
                fit: BoxFit.fitWidth,
              ),
            ),
            Positioned(
              right: UIUtils.dp(11),
              bottom: UIUtils.dp(11),
              child: Image.asset(
                'assets/home_img/ic_plan_no0${index}.png',
                width: UIUtils.dp(31),
                height: UIUtils.dp(35),
                fit: BoxFit.fitWidth,
              ),
            ),
          ],
        ),
        onTap: () {
          onTap!();
        },
      ),
    );
  }

  Widget vipUnit(int index, String title, String text, String text1,
      String desc, double progress, String btn, bool state, Function()? onTap) {
    return JkInkWell(
      child: Container(
        margin: EdgeInsets.only(left: UIUtils.dp(5.5), right: UIUtils.dp(5.5),top: UIUtils.dp(10)),
        width: UIUtils.dp(146),
        height: UIUtils.dp(175),
        decoration: BoxDecoration(
          color: Color(0xff333031),
          borderRadius: BorderRadius.all(Radius.circular(UIUtils.dp(12))),
        ),
        // padding: EdgeInsets.only(
            // top: UIUtils.dp(3.5),
            // bottom: UIUtils.dp(11),
            // left: UIUtils.dp(11),
            // right: UIUtils.dp(1)
        // ),
        constraints: BoxConstraints(
          minHeight: UIUtils.dp(163),
        ),
        // decoration: Style.plan_vip_box,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          // mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Container(
              constraints: BoxConstraints(
                minHeight: UIUtils.dp(28),
              ),
              // color: Color(0x881D8AE0),
              alignment: AlignmentDirectional.center,
              child: 
                  app14sp268Text(title,color: Color(0xffF7DDC0),fontFamily: 'PingFangSC-Semibold')
              // Text(
              //   title,
              //   style: Style.style_white_10_w500,
              // ),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Color(0xffF7F8FB),
                  borderRadius: BorderRadius.all(Radius.circular(UIUtils.dp(12))),
                ),
                child: Column(
                  children: [
                    Container(
                      // margin: EdgeInsets.only(
                      //     left: UIUtils.dp(8),
                      //     right: UIUtils.dp(6)),
                      alignment: AlignmentDirectional.center,
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              margin:EdgeInsets.only(left: UIUtils.dp(8)),
                              alignment: AlignmentDirectional.centerStart,
                              child: Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text: '${text}\n',
                                      style: Style.style_text6_9_5_bold,
                                    ),
                                    TextSpan(
                                      text: text1,
                                      style: index == 3
                                          ? Style.style_text6_8_bold
                                          : Style.style_text6_9_5_bold,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Image.asset(
                            state
                                ? 'assets/home_img/ic_plan_completed_vip.png'
                                : 'assets/home_img/ic_plan_incomplete.png',
                            width: UIUtils.dp(22),
                            fit: BoxFit.fitWidth,
                          ),
                          SizedBox(width: UIUtils.dp(10))
                          // Image.asset(
                          //   'assets/home_img/ic_plan_no1${index}.png',
                          //   width: UIUtils.dp(20),
                          //   fit: BoxFit.fitWidth,
                          // ),
                        ],
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(
                          top: UIUtils.dp(8),
                          left: UIUtils.dp(8),
                          right: UIUtils.dp(6)),
                      alignment: AlignmentDirectional.centerStart,
                      child: Text(
                        desc,
                        style: Style.plan_text1_h8_w500,
                      ),
                    ),
                    if (index == 1 || index == 3)
                      Container(
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(6),
                            left: UIUtils.dp(8),
                            right: UIUtils.dp(8)),
                        child: Progress(
                          progress: progress,
                          max: 100,
                          width:
                          // (UIUtils.width - UIUtils.dp(45)) / 3 - UIUtils.dp(37),
                          UIUtils.dp(126),
                          height: UIUtils.dp(5),
                          radius: UIUtils.dp(4),
                          bg: Color(0xFFEAEAEA),
                          speed: Color(0xFFF7DDC0),
                        ),
                      ),
                    Container(
                      margin: EdgeInsets.only(
                          top: index == 2? UIUtils.dp(6) : UIUtils.dp(15),
                          bottom: UIUtils.dp(6),
                          left: UIUtils.dp(8),
                          right: UIUtils.dp(6)),
                      alignment: AlignmentDirectional.center,
                      child: Row(
                        children: <Widget>[
                          Container(
                            width: UIUtils.dp(70),
                            height: UIUtils.dp(25),
                            alignment: AlignmentDirectional.center,
                            // decoration: Style.plan_box_red2yellow_h_r50,
                            decoration: BoxDecoration(
                                color: Color(0xffF7DDC0),
                                borderRadius: BorderRadius.all(Radius.circular(UIUtils.dp(12))),
                            ),
                            child:
                                app14sp268Text(btn, color: Color(0xff663409), fontFamily: 'PingFangSC-Semibold')
                            // Text(
                            //   btn,
                            //   style: Style.style_white_9_w500,
                            // ),
                          ),
                          const Expanded(
                            child: SizedBox(),
                          ),
                          Image.asset(
                            'assets/home_img/ic_plan_no1${index}.png',
                            width: UIUtils.dp(30),
                            fit: BoxFit.fitWidth,
                          ),

                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      onTap: () {
        onTap!();
      },
    );
  }

  Widget AnalyzeWidget() {
    return Column(
      children: [
        Container(
          width: double.infinity,
          // height: 180,
          color: Colors.transparent,
          margin: EdgeInsets.only(top: 15.w),
          child: Column(
            children: [
              Row(
                children: [
                  SizedBox(width: 22.w),
                  app16spAC5000Text('我的做题分析',color: Colors.black,fontFamily: 'PingFangSC-Semibold'),
                  const Expanded(child: SizedBox()),
                  app14sp268Text('知识点掌握分布',color: Color(0xffB2B2B2),fontFamily: 'PingFangSC-Medium'),
                  // Column(
                  //   children: [
                  //     Image.asset(
                  //       'assets/home_img/ic_plan_jiantou.png',
                  //       width: 14.w,
                  //       height: 29.w,
                  //     ),
                  //     const Text(
                  //       '下滑查看',
                  //       style: TextStyle(
                  //         fontSize: 8,
                  //         color: Color(0xFFAAAAAA),
                  //       ),
                  //     ),
                  //   ],
                  // ),
                  SizedBox(width: 10.w),
                ],
              ),
              SizedBox(height: 5.w),
              ...AnalyzeItemWidget(),
              if (isVip)
                Container(
                  margin: EdgeInsets.only(
                      top: UIUtils.dp(2.5),
                      bottom: UIUtils.dp(17.5),
                      left: UIUtils.dp(22),
                      right: UIUtils.dp(18)),
                  // decoration: Style.style_box_white_r10,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Container(
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(16)),
                        child: Text(
                          '注意事项',
                          style:
                          Style.style_text1_16_bold,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(10),
                            // left: UIUtils.dp(13),
                            right: UIUtils.dp(13)),
                        child: Row(
                          crossAxisAlignment:
                          CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(
                              '1.',
                              style: Style
                                  .plan_text2_11_w500,
                            ),
                            Expanded(
                              child: Container(
                                margin: EdgeInsets.only(
                                    left: UIUtils.dp(3)),
                                alignment:
                                AlignmentDirectional
                                    .centerStart,
                                child: Text(
                                  '先答会的题，不会的可先点击右上方题号格子跳过，放在最后答!题目可能先难后易，避免影响答题心态!',
                                  style: Style
                                      .plan_text2_11_w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(2),
                            // left: UIUtils.dp(13),
                            right: UIUtils.dp(13)),
                        child: Row(
                          crossAxisAlignment:
                          CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(
                              '2.',
                              style: Style
                                  .plan_text2_11_w500,
                            ),
                            Expanded(
                              child: Container(
                                margin: EdgeInsets.only(
                                    left: UIUtils.dp(3)),
                                alignment:
                                AlignmentDirectional
                                    .centerStart,
                                child: Text(
                                  '交卷前，一定要检查跳过的题，切忌漏答',
                                  style: Style
                                      .plan_text2_11_w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(2),
                            // left: UIUtils.dp(13),
                            right: UIUtils.dp(13)),
                        child: Row(
                          crossAxisAlignment:
                          CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(
                              '3.',
                              style: Style
                                  .plan_text2_11_w500,
                            ),
                            Expanded(
                              child: Container(
                                margin: EdgeInsets.only(
                                    left: UIUtils.dp(3)),
                                alignment:
                                AlignmentDirectional
                                    .centerStart,
                                child: Text(
                                  '鼠标不要连续惦点击，易造成连续答题或电脑死机。',
                                  style: Style
                                      .plan_text2_11_w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(2),
                            // left: UIUtils.dp(13),
                            right: UIUtils.dp(13)),
                        child: Row(
                          crossAxisAlignment:
                          CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(
                              '4.',
                              style: Style
                                  .plan_text2_11_w500,
                            ),
                            Expanded(
                              child: Container(
                                margin: EdgeInsets.only(
                                    left: UIUtils.dp(3)),
                                alignment:
                                AlignmentDirectional
                                    .centerStart,
                                child: Text(
                                  '调整摄像头或者座椅，确保面部清晰地显示在电脑左上角的对话框中，考试过程中会随时抓拍照片，不要捂嘴、托腮等遮挡面部。',
                                  style: Style
                                      .plan_text2_11_w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(
                            left: UIUtils.dp(3),
                            bottom: UIUtils.dp(16),
                        ),
                        alignment:
                        AlignmentDirectional
                            .centerStart,
                        child: Text(
                          '答题遇到电脑卡顿，试题不出现、交卷无反应、下一题点不动等情况，不要随意重启电脑，举手示意请工作人员及时处理。',
                          style: Style
                              .plan_text2_11_w500,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
        // if (!CheckUtils.isVip(widget.subject!))
        Container(
          width: double.infinity,
          height: 129.w + ScreenUtil().bottomBarHeight,
          color: Colors.transparent,
        )
      ],
    );
  }

  List<Widget> AnalyzeItemWidget() {
    if (KnowledgeArr.isEmpty) {
      return [];
    }
    List<Widget> rows = [];
    for (int i = 0; i < KnowledgeArr.length; i ++) {
      // 每次取两个元素
      rows.add(
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnalyzeItems(i),
            // if (i + 1 < KnowledgeArr.length) AnalyzeItems(i + 1),
            // if (i + 1 >= KnowledgeArr.length)
            //   SizedBox(
            //     width: 175.w,
            //     height: 88.5.w,
            //   ),
          ],
        ),
      );
    }
    return rows;
  }

  Widget AnalyzeItems(int i) {
    KnowledgeMo mo = KnowledgeArr[i];
    int rate = (mo.errorRate * 100.00).toInt();
    String img = '';
    if (rate < 0 || rate >= 40) {
      img = 'assets/home_img/ic_plan_item_bg1.png';
    } else if (rate >= 20 && rate < 40) {
      img = 'assets/home_img/ic_plan_item_bg2.png';
    } else {
      img = 'assets/home_img/ic_plan_item_bg3.png';
    }
    return JkInkWell(
      onTap: () async {
        if (isVip) {
          if (mo.subIds!.isEmpty) {
            return;
          }
          JumpUtils.get().jumpChapterExercise(
              JkKey.KEY_KNOWLEDGE, widget.subject!,
              ids: mo.subIds);
        } else {
          BuryingPointUtils.instance.addPoint(
              buryingPointList: BuryingPointList(
                  eventType: 1,
                  entranceType: 31,
                  action: 1, browseDuration: 0)
          );
          JumpUtils.get().jumpBuyVip(vipType: widget.subject!,type: 'jingxuan600ti');
        }
      },
      child: Container(
        width: MediaQuery.of(context).size.width-22.w,
        height: 39.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(UIUtils.dp(12))),
        ),
        margin: EdgeInsets.only(left: 11.w, right: 11.w, top: 11.w),
        child: Row(
          children: [
            // Positioned(
            //   left: 0,
            //   right: 0,
            //   top: 0,
            //   child: Image.asset(img),
            // ),
            Container(
              width: 230.w,
              // color: Colors.white,
              padding: EdgeInsets.only(left: 11.w, right: 10.w),
              // alignment: AlignmentDirectional.center,
              child: Text(
                mo.knowledgeName,
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF808080),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            assImg2(img: 'genwoxue_more_icon',w: 16.w,h: 16.w,),
            SizedBox(width: 4.w,),
            Expanded(
              child: Container(
                // color: Colors.white,
                width: 96.w,
                padding: EdgeInsets.only(left: 1.w, right: 1.w),
                alignment: AlignmentDirectional.center,
                decoration: BoxDecoration(
                  color: Color(0xFF4FABF4),
                  borderRadius: BorderRadius.only(topRight: Radius.circular(UIUtils.dp(10)), bottomRight: Radius.circular(UIUtils.dp(10))),
                ),
                child: Text(
                  mo.errorRate < 0 ? '没有做题记录' : '错误率: ${rate}%',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    // color: mo.errorRate < 0 ? Color(0xFFE82D2D) : Colors.black,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget BottomBuyWidget() {
    if (isVip) {
      return SizedBox();
    }
    return Container(
      height: 129.w + ScreenUtil().bottomBarHeight,
      color: Colors.transparent,
      child: Stack(
        children: [
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            top: 24.w,
            child: Container(
              color: Colors.white,
            ),
          ),
          Positioned(
            left: (ScreenUtil().screenWidth - 312.w) / 2,
            top: 33.w,
            child: JkInkWell(
              onTap: () {
                if (isVip) {
                  JumpUtils.get().jumpVipCourse(widget.subject!);
                } else {
                  BuryingPointUtils.instance.addPoint(
                      buryingPointList: BuryingPointList(
                          eventType: 1,
                          entranceType: 12,
                          action: 1, browseDuration: 0)
                  );
                  JumpUtils.get().jumpBuyVip(vipType: widget.subject!,type: 'jingxuan600ti');
                }
              },
              child: Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage('assets/home_img/ic_plan_buybg.png'),fit: BoxFit.fill),
                ),
                width: 327.w,
                height: 55.w,
                child: Column(
                  // mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(height: 8.w),
                    Row(
                      children: [
                        SizedBox(width: 86.w),
                        assImg2(img: 'mine_vip_card_huangguan',w: 16.w,h: 16.w,),
                        SizedBox(width: 4.w),
                        const Text(
                          '开通VIP享进阶方案',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFFE2AE73),
                            fontFamily: 'PingFangSC-Semibold',
                          ),
                        ),
                      ],
                    ),
                    const Text(
                      '精选题库押题准，科学备考效率高',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFFE2AE73),
                        fontFamily: 'PingFangSC-Medium',                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            // left: 0,
            right: 36.w,
            top: 14.w,
            // bottom: 0,
            // width: 50.w,
            // height: 50.w,
            child: Container(
              width: 96.w,
              height: 26.w,
              padding: EdgeInsets.only(bottom: 5.w,left: 3.w,right: 3.w),
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: assetImg2(img: 'genwoxue_by_tips',w: 21.w,h: 21.w),
                  fit: BoxFit.fill,
                )
              ),
              child: Center(child: app12spAAAText('不过单科赔80元',color: Color(0xff2E2C2D),fontFamily: 'PingFangSC-Medium'))
            ),
          ),
        ],
      ),
    );
  }
}

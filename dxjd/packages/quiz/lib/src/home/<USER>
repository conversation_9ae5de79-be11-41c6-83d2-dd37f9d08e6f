import 'dart:async';

import 'package:component_library/component_library.dart';
import 'package:flutter/material.dart';
import 'package:tools/tools.dart';

import '../../quiz.dart';
import '../bean/fixed_ids.dart';
import '../bean/qualify.dart';
import '../bean/record.dart';
import '../bean/record_mock.dart';
import '../bean/record_six.dart';

StreamSubscription? _ibus;

class Qualify extends StatefulWidget {
  const Qualify(
      {Key? key,
      this.subject,
      this.quiz_title,
      this.qualify = 0,
      this.quizs,
      this.exams})
      : super(key: key);

  final int? subject;
  final String? quiz_title;
  final int? qualify;
  final List<RecordMo>? quizs;
  final List<RecordMockMo>? exams;

  @override
  _QualifyState createState() => _QualifyState();
}

class _QualifyState extends State<Qualify> {
  ScrollController? _controller = ScrollController(keepScrollOffset: true);

  String TrainType = '';

  List<RecordMo> _quizs = <RecordMo>[];
  List<RecordMockMo> _exams = <RecordMockMo>[];
  String quiz_title = '';
  int qualify = 0;

  @override
  void initState() {
    super.initState();
    TrainType = ITools.get().trainType();
    // 订阅事件
    _ibus = IEventBus.get().register<IEvent>((event) {
      if (JkKey.EVENT_QUALIFY_SYNC == event.type) {
        QualifyMo data = event.object;
        Logs.e('接收 data = ${data.toJson()}');
        if (data != null) {
          qualify = data.qualify;
          checkExamScore(data.sixSetRecordList);
          checkMockScore(data.mockRecordList);
        }
        refreshUI();
      }
    });
    refresh();
  }

  @override
  void dispose() {
    super.dispose();
    _controller!.dispose();
    IEventBus.get().unregister(_ibus);
  }

  void refreshUI() async {
    try {
      if (mounted) {
        setState(() {});
      }
    } catch (e) {}
  }

  //回到顶部
  void toTop() {
    if (_controller!.hasClients) {
      _controller!.animateTo(0,
          duration: const Duration(microseconds: 600), curve: Curves.ease);
    }
  }

  void refresh() async {
    try {
      quiz_title = widget.quiz_title!;
      qualify = widget.qualify!;
      _quizs.clear();
      if (widget.quizs == null) {
        _quizs = <RecordMo>[];
      } else {
        for (int i = 0; i < widget.quizs!.length; i++) {
          if (widget.quizs![i].title != '货车试题' &&
              widget.quizs![i].title != '货车资格证试题' &&
              widget.quizs![i].title != '客车试题') {
            _quizs.add(widget.quizs![i]);
          }
        }
      }
      if (widget.exams == null) {
        _exams = <RecordMockMo>[];
      } else {
        _exams = widget.exams!;
      }
    } catch (e) {
    } finally {
      refreshUI();
    }
  }

  int getExamScore(List<RecordSixMo> list, String title) {
    if (list == null) {
      return 0;
    }
    for (int i = 0; i < list.length; i++) {
      RecordSixMo bean = list[i];
      if (title == bean.recordTitle) {
        return bean.examScore;
      }
    }
    return 0;
  }

  void checkExamScore(List<RecordSixMo> list) {
    for (int i = 0; i < _quizs.length; i++) {
      int score = getExamScore(list, _quizs[i].title);
      _quizs[i] = RecordMo(_quizs[i].id, _quizs[i].title, score);
    }
  }

  void checkMockScore(List<RecordMockMo> list) {
    _exams.clear();
    for (int i = 0; i < 3; i++) {
      if (list.length > i) {
        _exams.add(list[i]);
      } else {
        _exams.add(RecordMockMo('', 0, 0));
      }
    }
  }

  String getFixedTips() {
    if (ITools.get().getType(TrainType) == JkKey.D) {
      if (widget.subject! == 4) {
        return '*做3套保命题，确保充分掌握好3套保命题';
      } else if (widget.subject! == 1) {
        return '*做4套保命题，确保充分掌握好4套保命题';
      }
    }
    return '*做6套保命题，确保充分掌握好6套保命题';
  }

  @override
  Widget build(BuildContext context) {
    return UIUtils.statusBarDarkWidget(
      Scaffold(
        resizeToAvoidBottomInset: false,
        body: WillPopScope(
          onWillPop: () {
            //这里可以响应物理返回键
            finish();
            return Future.value(false);
          },
          child: Container(
            decoration: Style.style_bg,
            child: Column(
              children: <Widget>[
                UITitle(
                  type: 'white',
                  title: widget.subject! == 1 ? '科一补偿资格获取' : '科四补偿资格获取',
                  onTap: (String? type) {
                    if (type == 'left') {
                      finish();
                    }
                  },
                ),
                Expanded(
                  child: XScrollView(
                    child: Column(
                      children: <Widget>[
                        buildBannerWidget(),
                        Container(
                          margin: EdgeInsets.only(
                              top: UIUtils.dp(16),
                              left: UIUtils.dp(16),
                              right: UIUtils.dp(16)),
                          alignment: AlignmentDirectional.center,
                          decoration: Style.style_box_white_r8,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Container(
                                margin: EdgeInsets.only(
                                    top: UIUtils.dp(16),
                                    left: UIUtils.dp(16),
                                    right: UIUtils.dp(16)),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: <Widget>[
                                    Text(
                                      quiz_title,
                                      style: Style.style_blue_16_w500,
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                          left: UIUtils.dp(4),
                                          bottom: UIUtils.dp(1.5)),
                                      child: Text(
                                        '（每套100题）',
                                        style: Style.style_blue1_12_w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    top: UIUtils.dp(4),
                                    left: UIUtils.dp(8),
                                    right: UIUtils.dp(8)),
                                child: XGridView(
                                    shrinkWrap: true,
                                    physics: false,
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 2,
                                      childAspectRatio: 163 / 98,
                                    ),
                                    itemCount: _quizs.length,
                                    itemBuilder: (context, i) {
                                      return _buildFixed(i, _quizs[i]);
                                    }),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    top: UIUtils.dp(20),
                                    bottom: UIUtils.dp(16),
                                    left: UIUtils.dp(16),
                                    right: UIUtils.dp(16)),
                                child: Text(
                                  getFixedTips(),
                                  style: Style.style_red_14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(
                              top: UIUtils.dp(16),
                              left: UIUtils.dp(16),
                              right: UIUtils.dp(16)),
                          alignment: AlignmentDirectional.center,
                          decoration: Style.style_box_white_r8,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Container(
                                margin: EdgeInsets.only(
                                    top: UIUtils.dp(16),
                                    left: UIUtils.dp(16),
                                    right: UIUtils.dp(16)),
                                child: Text(
                                  '连续三次模拟成绩',
                                  style: Style.style_text1_16_w500,
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    top: UIUtils.dp(8),
                                    left: UIUtils.dp(16),
                                    right: UIUtils.dp(16)),
                                child: Text(
                                  '(如有一次成绩低于93分，需从第一次开始再做)',
                                  style: Style.style_text3_12_w500,
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    top: UIUtils.dp(16),
                                    left: UIUtils.dp(11),
                                    right: UIUtils.dp(11)),
                                child: Row(
                                  children: <Widget>[
                                    Expanded(
                                      child: UnitMock(
                                          index: 0,
                                          mo: _exams[0],
                                          onTap: () {
                                            JumpUtils.get().jumpMockExercise(
                                                JkKey.KEY_MOCK,
                                                widget.subject!);
                                          }),
                                    ),
                                    Expanded(
                                      child: UnitMock(
                                          index: 1,
                                          mo: _exams[1],
                                          onTap: () {
                                            JumpUtils.get().jumpMockExercise(
                                                JkKey.KEY_MOCK,
                                                widget.subject!);
                                          }),
                                    ),
                                    Expanded(
                                      child: UnitMock(
                                          index: 2,
                                          mo: _exams[2],
                                          onTap: () {
                                            JumpUtils.get().jumpMockExercise(
                                                JkKey.KEY_MOCK,
                                                widget.subject!);
                                          }),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    top: UIUtils.dp(20),
                                    bottom: UIUtils.dp(16),
                                    left: UIUtils.dp(16),
                                    right: UIUtils.dp(16)),
                                child: Text(
                                  '*需要连续三次模拟考试均达到93分以上',
                                  style: Style.style_red_14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: UIUtils.dp(16)),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: UIUtils.dp(12)),
                Container(
                  padding: EdgeInsets.only(
                      left: UIUtils.dp(24), right: UIUtils.dp(24)),
                  alignment: AlignmentDirectional.center,
                  child: InkWellShadow(
                    child: Container(
                      alignment: AlignmentDirectional.center,
                      height: UIUtils.dp(46),
                      decoration: qualify == 1
                          ? Style.style_btn_blue_big
                          : Style.style_box_dark_r50,
                      child: Text(
                        // qualify == 1 ? '已获取补偿资格，去汇报正式考试成绩' : '未获取赔偿资格',
                        qualify == 1 ? '已获取补偿资格' : '未获取赔偿资格',
                        style: Style.style_white_16_w500,
                      ),
                    ),
                    onTap: () async {
                        if (qualify == 1) {
                          JumpSmallProgramUtils.jump('/pages/grade/compensate/compensate?Subject=${widget.subject!} ', "fc2230308094514309");
                        }
                        // JumpUtils.get().jumpMockHome(widget.subject!);
                      },
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                      top: UIUtils.dp(12),
                      bottom: UIUtils.dp(24),
                      left: UIUtils.dp(16),
                      right: UIUtils.dp(16)),
                  child: Text(
                    '正式考试当天或以后获取的补偿资格无效，不予补偿!',
                    style: Style.style_red_12,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildBannerWidget() {
    return InkWell(
      onTap: () {
        JumpSmallProgramUtils.jump("/pages/operate/article/details/details?id=009C42C806", "fc2230308094514309");
      },
      child: Container(
        margin: EdgeInsets.only(top: UIUtils.dp(24)),
        height: UIUtils.dp(108),
        alignment: AlignmentDirectional.center,
        child: ClipRRect(
          borderRadius: BorderRadius.all(Radius.circular(8)),
          child: Image.asset(
            'assets/train/ic_qualify_banner.png',
            fit: BoxFit.fill,
            width: UIUtils.width - UIUtils.dp(32),
            height: UIUtils.dp(108),
          ),
          // child: BannerView(
          //   list: [],
          //   width: UIUtils.width - UIUtils.dp(32),
          //   height: UIUtils.dp(108),
          //   onTap: (AdSpaceMo mo) {
          //
          //   },
          // ),
        ),
      ),
    );
  }

  //报忧查询审核状态
  void _getBadNewsSubmitStatus(int subject) async {
    int status = await HttpDao.get().badnewsQuery(subject);
    if (status == -1) {
      //查询失败
    } else {
      // JumpUtils.get().jump(BringBadNews(subject: subject, status: status));
    }
  }

  void finish() {
    Navigator.of(context).pop();
  }

  Widget _buildFixed(int index, RecordMo mo) => Container(
        margin: EdgeInsets.only(
            top: UIUtils.dp(16), left: UIUtils.dp(8), right: UIUtils.dp(8)),
        alignment: Alignment.center,
        child: UnitFixed(
            index: index,
            mo: mo,
            onTap: (int id) async {
              final result = await HttpDao.get().getFixedIds(id: id);
              FixedIdsMo ids = FixedIdsMo.fromJson(result);
              JumpUtils.get().jumpChapterExercise(
                  JkKey.KEY_FIXED, widget.subject!,
                  fixid: id, recordTitle: mo.title, ids: ids.subUidArr!);
            }),
      );
}

class UnitFixed extends StatefulWidget {
  const UnitFixed({Key? key, this.index, this.mo, this.onTap})
      : super(key: key);

  final int? index;
  final RecordMo? mo;
  final Function(int id)? onTap;

  @override
  _UnitFixedState createState() => _UnitFixedState();
}

class _UnitFixedState extends State<UnitFixed> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return JkInkWell(
      child: Container(
        width: (UIUtils.width - UIUtils.dp(82)) / 2,
        height: UIUtils.dp(82),
        alignment: AlignmentDirectional.center,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(getFixedBg(widget.mo!.score)),
            fit: BoxFit.fill,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Container(
              margin: EdgeInsets.only(left: UIUtils.dp(12), top: UIUtils.dp(6)),
              child: Image.asset(
                getFixedIcon(widget.index!, widget.mo!.score > 0),
                width: UIUtils.dp(38),
                height: UIUtils.dp(38),
                fit: BoxFit.contain,
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    margin: EdgeInsets.only(
                        top: UIUtils.dp(6),
                        left: UIUtils.dp(6),
                        right: UIUtils.dp(24)),
                    constraints: BoxConstraints(
                      minHeight: UIUtils.dp(28),
                    ),
                    child: Text(
                      widget.mo!.title.trim(),
                      style: widget.mo!.title.length > 3
                          ? Style.style_text1_13_w500
                          : Style.style_text1_16_w500,
                      textAlign: TextAlign.left,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: UIUtils.dp(4), left: UIUtils.dp(6)),
                    child: Text(
                      '成绩：${widget.mo!.score}',
                      style: Style.style_text2_12,
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      onTap: () {
        widget.onTap!(widget.mo!.id);
      },
    );
  }
}

String getFixedBg(int score) {
  if (score >= 96) {
    return 'assets/train/ic_qualify_fixed_box1.png';
  } else if (score > 0) {
    return 'assets/train/ic_qualify_fixed_box2.png';
  }
  return 'assets/train/ic_qualify_fixed_box3.png';
}

String getFixedIcon(int index, bool isPass) {
  if (index == 0) {
    return isPass
        ? 'assets/train/ic_qualify_fixed1.png'
        : 'assets/train/ic_qualify_fixed1_n.png';
  } else if (index == 1) {
    return isPass
        ? 'assets/train/ic_qualify_fixed2.png'
        : 'assets/train/ic_qualify_fixed2_n.png';
  } else if (index == 2) {
    return isPass
        ? 'assets/train/ic_qualify_fixed3.png'
        : 'assets/train/ic_qualify_fixed3_n.png';
  } else if (index == 3) {
    return isPass
        ? 'assets/train/ic_qualify_fixed4.png'
        : 'assets/train/ic_qualify_fixed4_n.png';
  } else if (index == 4) {
    return isPass
        ? 'assets/train/ic_qualify_fixed5.png'
        : 'assets/train/ic_qualify_fixed5_n.png';
  } else if (index == 5) {
    return isPass
        ? 'assets/train/ic_qualify_fixed6.png'
        : 'assets/train/ic_qualify_fixed6_n.png';
  }
  return isPass
      ? 'assets/train/ic_qualify_fixed1.png'
      : 'assets/train/ic_qualify_fixed1_n.png';
}

class UnitMock extends StatefulWidget {
  const UnitMock({Key? key, this.index, this.mo, this.onTap}) : super(key: key);

  final int? index;
  final RecordMockMo? mo;
  final Function()? onTap;

  @override
  _UnitMockState createState() => _UnitMockState();
}

class _UnitMockState extends State<UnitMock> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return JkInkWell(
      child: Container(
        margin: EdgeInsets.only(left: UIUtils.dp(5), right: UIUtils.dp(5)),
        width: (UIUtils.width - UIUtils.dp(80)) / 3,
        height: ((UIUtils.width - UIUtils.dp(80)) / 3) * 101 / 98,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(getMockBg(widget.mo!.examScore)),
            fit: BoxFit.fill,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Column(
                children: <Widget>[
                  Container(
                    margin: EdgeInsets.only(top: UIUtils.dp(22)),
                    child: Image.asset(
                      widget.mo!.examScore > 0
                          ? 'assets/train/ic_qualify_mock.png'
                          : 'assets/train/ic_qualify_mock_n.png',
                      width: UIUtils.dp(38),
                      height: UIUtils.dp(38),
                      fit: BoxFit.contain,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: UIUtils.dp(12)),
                    child: Text(
                      '成绩：${widget.mo!.examScore}',
                      style: Style.style_text2_12,
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: UIUtils.dp(20),
            ),
          ],
        ),
      ),
      onTap: () {
        widget.onTap!();
      },
    );
  }
}

String getMockBg(int score) {
  if (score >= 93) {
    return 'assets/train/ic_qualify_mock_box1.png';
  } else if (score > 0) {
    return 'assets/train/ic_qualify_mock_box2.png';
  }
  return 'assets/train/ic_qualify_mock_box3.png';
}

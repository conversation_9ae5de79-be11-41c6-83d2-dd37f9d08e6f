import 'dart:convert';
import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:component_library/component_library.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:home_repository/home_repository.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:mop/mop.dart';
import 'package:quiz/src/home/<USER>';
import 'package:timing/timing.dart';
import 'package:tools/tools.dart';
import 'package:user_repository/user_repository.dart';

import '../../quiz.dart';
import '../bean/dtjq.dart';
import '../bean/essence.dart';
import '../bean/feedback_type.dart';
import '../bean/fixed.dart';
import '../bean/knowledge.dart';
import '../bean/ladder.dart';
import '../bean/ladder_cache.dart';
import '../bean/newly.dart';
import '../bean/qualify.dart';
import '../bean/quiz.dart';
import '../bean/quizsort.dart';
import '../bean/sub_record.dart';
import '../data/i_quiz.dart';
import '../data/quiz_utils.dart';
import '../home/<USER>';
import '../home/<USER>/page_browse.dart';
import '../home/<USER>/page_chapter.dart';
import '../home/<USER>/page_mock.dart';
import '../home/<USER>/page_real_exam.dart';
import '../home/<USER>/page_real_home.dart';
import '../home/<USER>/rectify.dart';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';

class JumpUtils {
  //构造方法
  JumpUtils();

  // 单例方法
  static JumpUtils? instance;

  static JumpUtils get() {
    if (instance == null) {
      instance = JumpUtils();
    }
    return instance!;
  }

  bool isClick = false;

  void startTiming(bool timing, int subject, void Function() callback) async {
    Logs.e("timing = $timing");
    if (!timing) {
      callback();
      return;
    }
    startTimingDialog(Global.appContext(), subject, (start) {
      if (start) {
        callback();
      }
    });
  }

  // 计时选择科目弹框
  Future<void> startTimingDialog(
    BuildContext context,
    int subject,
    Function(bool) callBack,
  ) async {
    // callBack(true);
    // 是否正在计时
    if (TimingHelper.isRemoteTrain) {
      callBack(true);
      return;
    }

    Future.delayed(Duration.zero, () {
      ITiming.get().CurrentTimingSubject = subject;
      TimingHelper.startNormalTimingDialog(context, (b) {
        callBack(b);
      });
    });
  }

  bool showBuyVip(BuildContext context, bool isVip) {
    if (!isVip) {
      DialogHelper.showDialogTips(
          context, 0, '温馨提示', '您需要购买VIP才能继续操作', '取消', '去购买', (String? type) {
        if (type == 'confirm') {
          jumpBuyVip();
        }
      });
      return false;
    }
    return true;
  }

  void jumpPromote(int subject, {String type = '', int vipType = 1,isTiming = true}) async {
    if (ITools.get().isVip(subject)) {
      jumpVipCourse(subject,isTiming: isTiming);
    } else {
      jumpBuyVip(type: type, vipType: vipType);
    }
  }

  void jumpBuyVip({String type = '', int vipType = 1}) async {
    if (!MainController.isLoginIntercept()) {
      return;
    }
    //打开vip购买小程序
    var shopPath;
    String cityCode = "";
    String cityName = "";
    ExaminationController examinationController = Get.find<ExaminationController>(tag: ExaminationPageState.key.toString());
    UserAccount? userInfo = await examinationController.userRepository.userAccountDM;
    if (examinationController.selectCityMap.isEmpty) {
      cityCode =
          userInfo?.reqisterDivision.toString().substring(0, 4) ?? "440100";
      cityName = cityMap[cityCode.toString()] ?? "广州市";
      cityCode = "${cityCode}00";
    } else {
      cityCode = examinationController.selectCityMap['division'];
      cityName = examinationController.selectCityMap['name'];
    }
    if (vipType == 1) {
      shopPath =
          '/pages/vip/part1/part1?type=$type&goods=vip1&city=$cityCode&cityname=$cityName';
    } else if (vipType == 4) {
      shopPath =
          '/pages/vip/part1/part1?type=$type&goods=vip4&city=$cityCode&cityname=$cityName';
    } else {
      shopPath =
          '/pages/vip/part1/part1?type=$type&goods=vip1vip4&city=$cityCode&cityname=$cityName';
    }
    // Mop.instance.openApplet('fc2259710004813765', path: shopPath);
    JumpSmallProgramUtils.jump(shopPath, "fc2259710004813765");
  }

  void jumpVipCourse(int subject,{isTiming = true}) async {
    startTiming(isTiming, subject, () async {
      if (isClick) {
        return;
      }
      isClick = true;
      String trainType = ITools.get().trainType();
      FixedMo? _fixed = FixedMo(0, 0, 0, 0, 0, 0, 0);
      QualifyMo? _qualify = QualifyMo(0, [], [], []);

      EasyLoading.show();
      try {
        final result =
            await HttpDao.get().getFixed(subject: subject, type: trainType);
        if (result == null) {
          UIUtils.showToastSafe(msg: '获取VIP课程失败，请检查网络再试！', gravity: 'center');
          return;
        }
        _fixed = FixedMo.fromJson(result);
        final result1 = await HttpDao.get()
            .getQualify(subject: subject, trainType: trainType);
        if (result1 != null) {
          _qualify = QualifyMo.fromJson(result1);
        }
      } catch (e) {
      } finally {
        EasyLoading.dismiss();
        isClick = false;
      }
      Tools.get().jump(VipCourse(
          subject: subject,
          trainType: trainType,
          fixed: _fixed,
          qualify: _qualify));
    });
  }

  void jumpKnowledge(int subject, int type) async {
    if (isClick) {
      return;
    }
    isClick = true;
    EasyLoading.show();
    List<KnowledgeMo> list = <KnowledgeMo>[];
    try {
      final result =
          await HttpDao.get().getKnowledge(examType: subject, type: type);
      list = (result as List<dynamic>)
          .map((e) => KnowledgeMo.fromJson(e))
          .toList();
    } catch (e) {
    } finally {
      EasyLoading.dismiss();
      isClick = false;
    }

    Tools.get().jump(Knowledge(subject: subject, type: type, list: list));
  }

  void jumpChapter(int subject, {isTiming = true}) async {
    Logs.e("isLogin = ${ITools.get().isLogin}");
    if (ITools.get().isLogin) {
      startTiming(isTiming, subject, () async {
        doChapter(subject);
      });
    } else {
      doChapter(subject);
    }
  }

  void doChapter(int subject) async {
    if (isClick) {
      return;
    }
    isClick = true;
    List<NewlyMo> newly = <NewlyMo>[];
    List<QuizsortMo> list = <QuizsortMo>[];

    EasyLoading.show();
    try {
      final result = await HttpDao.get().getNewly(subject, 2);
      List<NewlyMo> arr =
          (result as List<dynamic>).map((e) => NewlyMo.fromJson(e)).toList();
      ITrain.get().addNewlyMo(newly, arr, '新交规');
      ITrain.get().addNewlyMo(newly, arr, '新增题');
      ITrain.get().addNewlyMo(newly, arr, '争议题');
      ITrain.get().addNewlyMo(newly, arr, '手势题');
      list = await ITrain.get().getChapters(JkKey.KEY_CAPTER, subject);
    } catch (e) {
    } finally {
      EasyLoading.dismiss();
      isClick = false;
    }
    Tools.get().jump(Chapter(subject: subject, newly: newly, list: list));
  }

  void jumpMockHome(int subject, {isTiming = true}) async {
    startTiming(isTiming, subject, () async {
      if (isClick) {
        return;
      }
      isClick = true;

      EasyLoading.show();
      try {
        await IHome.get().updatePassRate(subject);
      } catch (e) {
      } finally {
        EasyLoading.dismiss();
        isClick = false;
      }
      Tools.get().jump(MockHome(subject: subject));
    });
  }

  void jumpExamRecord(int subject) async {
    if (isClick) {
      return;
    }
    isClick = true;
    EasyLoading.show();
    List<SubRecordMo> list = <SubRecordMo>[];

    try {
      String value = await Storage.getString(
          Storage.examRecord + ITools.get().getUid(''),
          jsonEncode(<Map<String, dynamic>>[]));
      // Logs.e('考试记录 value = ${value}');
      list = (jsonDecode(value) as List<dynamic>)
          .map((e) => SubRecordMo.fromJson(e))
          .toList();
      await IHome.get().updatePassRate(subject);
    } catch (e) {
    } finally {
      EasyLoading.dismiss();
      isClick = false;
    }
    Tools.get().jump(Transcript(subject: subject, list: list));
  }

  void jumpLadder(int subject) async {
    startTiming(true, subject, () async {
      if (isClick) {
        return;
      }
      isClick = true;
      List<LadderMo> list = <LadderMo>[];

      EasyLoading.show();
      try {
        final result = await HttpDao.get()
            .getLadder(subject: subject, type: ITools.get().trainType());
        list =
            (result as List<dynamic>).map((e) => LadderMo.fromJson(e)).toList();
        if (ITools.get().isVip(subject)) {
          list = await ITrain.get().checkLadder(list);
          list = await ITrain.get().updateLadder(list);
          await ITrain.get().loadLadder(list);
        } else {
          list = await ITrain.get().cacheLadder(list);
        }
      } catch (e) {
      } finally {
        EasyLoading.dismiss();
        isClick = false;
      }
      Tools.get().jump(Ladder(subject: subject, list: list));
    });
  }

  void jumpEssence(int subject,{isTiming = true}) async {
    startTiming(isTiming, subject, () async {
      if (isClick) {
        return;
      }
      isClick = true;
      String trainType = '';
      List<EssenceMo> data = <EssenceMo>[];

      EasyLoading.show();
      try {
        trainType =
            ITrain.get().getEssenceType(subject, ITools.get().trainType());
        final result = await HttpDao.get().getEssence(type: trainType);
        data = (result as List<dynamic>)
            .map((e) => EssenceMo.fromJson(e))
            .toList();
      } catch (e) {
      } finally {
        EasyLoading.dismiss();
        isClick = false;
      }
      Tools.get()
          .jump(Essence(subject: subject, trainType: trainType, data: data));
    });
  }

  void jumpMixed(int subject, {bool isGoWatchVideo = false}) async {
    startTiming(true, subject, () async {
      if (isClick) {
        return;
      }
      isClick = true;

      EasyLoading.show();
      List<int> ids = <int>[];
      try {
        final result = await HttpDao.get().getMixError(subject: subject);
        ids = (result as List<dynamic>).map((e) => e as int).toList();
      } catch (e) {
      } finally {
        EasyLoading.dismiss();
        isClick = false;
      }
      jumpChapterExercise(JkKey.KEY_MIXED, subject,
          ids: ids, isGoWatchVideo: isGoWatchVideo);
    });
  }

  void jumpFolder(int subject, String type, {isTiming = true}) async {
    startTiming(isTiming, subject, () async {
      if (isClick) {
        return;
      }
      isClick = true;

      EasyLoading.show();
      try {
        await QuizUtils.get().getFolder(subject, (List<QuizsortMo> list1,
            List<int> today1,
            List<int> ids1,
            List<QuizsortMo> list2,
            List<int> today2,
            List<int> ids2) {
          FolderData.get().data_wrong = list1;
          FolderData.get().today_ids_wrong = today1;
          FolderData.get().all_ids_wrong = ids1;
          FolderData.get().data_collect = list2;
          FolderData.get().today_ids_collect = today2;
          FolderData.get().all_ids_collect = ids2;
        });
      } catch (e) {
      } finally {
        EasyLoading.dismiss();
        isClick = false;
      }
      Tools.get().jump(Folder(subject: subject, type: type));
    });
  }

  void jumpTwoSecondAnswer(int subject) async {
    startTiming(true, subject, () async {
      if (isClick) {
        return;
      }
      isClick = true;
      EasyLoading.show();
      List<dtjqMo> moArr = [];
      try {
        moArr = await HttpDao.get().dtjqList(subject);
      } catch (e) {
      } finally {
        EasyLoading.dismiss();
        isClick = false;
      }
      Tools.get().jump(TwoSecondAnswer(
        subject: subject,
        moArr: moArr,
      ));
    });
  }

  void jumpChapterExercise(String type, int subject,
      {int sortId = 0,
      List<dynamic>? ids,
      int fixid = 0,
      int mainId = 0,
      int childId = 0,
      int luid = 0,
      bool isGoWatchVideo = false,
      String recordTitle = ''}) async {
    EasyLoading.show();
    try {
      IQuiz.get().clear();
      //解析参数
      if (JkKey.KEY_NEWLY == type ||
          JkKey.KEY_MIXED == type ||
          JkKey.KEY_LADDER == type ||
          JkKey.KEY_KNOWLEDGE == type ||
          JkKey.KEY_FIXED == type) {
        List<QuizMo> quizList = await QuizUtils.get().getFilterQuiz(
            ids!, ITools.get().getUid('0'), type, ITools.get().isLogin, false);
        if (JkKey.KEY_FIXED == type) {
          // 题库排序
          List<QuizMo> list1 = ITrain.get().getSortQuiz(JkKey.JUDGE, quizList);
          List<QuizMo> list2 = ITrain.get().getSortQuiz(JkKey.SINGLE, quizList);
          List<QuizMo> list3 = ITrain.get().getSortQuiz(JkKey.MULTI, quizList);
          IQuiz.get().quizList.addAll(list1);
          IQuiz.get().quizList.addAll(list2);
          IQuiz.get().quizList.addAll(list3);
        } else {
          IQuiz.get().quizList.addAll(quizList);
        }
        if (JkKey.KEY_LADDER == type) {
          List<LadderCacheMo> lists = await QuizUtils.get()
              .getLadder(luid, uid: ITools.get().getUid('0'));
          for (int i = 0; i < lists.length; i++) {
            //增加错题选项记录
            for (int n = 0; n < IQuiz.get().quizList.length; n++) {
              if (lists[i].subId == IQuiz.get().quizList[n].sub_Id) {
                IQuiz.get().quizList[n].isDone = true;
                if (lists[i].answer.isNotEmpty) {
                  IQuiz.get().quizList[n].selection = lists[i].answer;
                }
                //通过判断选择的选项和答案对比判断此题是否正确
                if (IQuiz.get().quizList[n].selection.isNotEmpty) {
                  IQuiz.get().quizList[n].examstate =
                      IQuiz.get().quizList[n].selection.toUpperCase() ==
                              IQuiz.get().quizList[n].answer.toUpperCase()
                          ? 1
                          : 2;
                }
              }
            }
          }
          for (int i = 0; i < IQuiz.get().quizList.length; i++) {
            if (!IQuiz.get().quizList[i].isDone) {
              IQuiz.get().currentpage = i;
              break;
            }
          }
        }
        if (JkKey.KEY_NEWLY == type) {
          for (int i = 0; i < IQuiz.get().quizList.length; i++) {
            if (IQuiz.get().quizList[i].examstate == 0) {
              IQuiz.get().currentpage = i;
              break;
            }
          }
        }
      } else if (JkKey.KEY_ERROR == type || JkKey.KEY_COLLECT == type) {
        IQuiz.get().quizList = await QuizUtils.get().getFilterQuiz(
            ids!, ITools.get().getUid('0'), type, ITools.get().isLogin, true);
      } else if (JkKey.KEY_CAPTER == type) {
        IQuiz.get().quizList = await QuizUtils.get().getChapterQuiz(
            sortId,
            ITools.get().trainType(),
            ITools.get().cityId(),
            ITools.get().getUid('0'),
            ITools.get().isLogin,
            type);

        int position = 0;
        if (ITools.get().isLogin) {
          position =
              await Storage.getInt('${subject}page${JkKey.KEY_CAPTER}$sortId');
        } else {
          position = await Storage.getInt(
              '${subject}page_visitor${JkKey.KEY_CAPTER}$sortId');
        }
        if (subject == 0 || position == IQuiz.get().quizList.length - 1) {
          IQuiz.get().currentpage = 0;
        } else {
          IQuiz.get().currentpage = position;
        }
      } else {
        IQuiz.get().quizList = await QuizUtils.get().getAll(
            subject,
            ITools.get().trainType(),
            ITools.get().cityId(),
            ITools.get().getUid('0'),
            ITools.get().isLogin,
            type);
        int position = 0;
        if (ITools.get().isLogin) {
          position =
              await Storage.getInt('${subject}page${JkKey.KEY_NONE}$sortId');
        } else {
          position = await Storage.getInt(
              '${subject}page_visitor${JkKey.KEY_NONE}$sortId');
        }
        if (subject == 0 || position == IQuiz.get().quizList.length - 1) {
          IQuiz.get().currentpage = 0;
        } else {
          IQuiz.get().currentpage = position;
        }
      }
      ITrain.get().fontSize = await Storage.getString(Storage.fontSize);
    } catch (e) {
    } finally {
      EasyLoading.dismiss();
    }

    Logs.e(
        "quizList = ${IQuiz.get().quizList.length}, currentpage = ${IQuiz.get().currentpage}, fontSize = ${ITrain.get().fontSize}");
    Tools.get().jump(PageChapter(
        subject: subject,
        type: type,
        sortId: sortId,
        ids: ids,
        fixid: fixid,
        mainId: mainId,
        childId: childId,
        luid: luid,
        isGoWatchVideo: isGoWatchVideo,
        recordTitle: recordTitle));
  }

  void jumpBrowseExercise(String type, int subject,
      {List<QuizMo>? list}) async {
    IQuiz.get().quizList = list!;
    IQuiz.get().currentpage = 0;
    ITrain.get().fontSize = await Storage.getString(Storage.fontSize);
    Logs.e(
        '错题：list = ${list.length}, quizList = ${IQuiz.get().quizList.length}, currentpage = ${IQuiz.get().currentpage}');
    Tools.get().jump(PageBrowse(subject: subject, type: type));
  }

  void jumpMockExercise(String type, int subject,
      {List<dynamic>? ids, int fixid = 0, Function()? callback}) async {
    EasyLoading.show();
    try {
      IQuiz.get().clear();
      //解析参数
      if (JkKey.KEY_ESSENCE == type) {
        List<QuizMo> quizList = await QuizUtils.get().getFilterQuiz(
            ids!, ITools.get().getUid('0'), type, ITools.get().isLogin, true);
        // 题库排序
        List<QuizMo> list1 = ITrain.get().getSortQuiz(JkKey.JUDGE, quizList);
        List<QuizMo> list2 = ITrain.get().getSortQuiz(JkKey.SINGLE, quizList);
        List<QuizMo> list3 = ITrain.get().getSortQuiz(JkKey.MULTI, quizList);
        IQuiz.get().quizList.addAll(list1);
        IQuiz.get().quizList.addAll(list2);
        IQuiz.get().quizList.addAll(list3);
      } else if (JkKey.KEY_MOCK == type) {
        IQuiz.get().quizList = await QuizUtils.get().getMockQuiz(
            subject,
            ITools.get().trainType(),
            ITools.get().cityId(),
            ITools.get().getUid('0'),
            type,
            true);
      }
      ITrain.get().fontSize = await Storage.getString(Storage.fontSize);
    } catch (e) {
    } finally {
      EasyLoading.dismiss();
    }

    if (callback != null) {
      callback();
    }
    Tools.get().jump(
        PageMock(subject: subject, type: type, ids: ids, miJuanId: fixid));
  }

  void jumpRealHome(int subject) async {
    if (isClick) {
      return;
    }
    isClick = true;
    EasyLoading.show();
    String videoUrl = '';
    try {
      String vid = await HttpDao.get().realExamBuyVideo();
      if (vid.isNotEmpty) {
        videoUrl = await HttpDao.get().videoUrl(vid);
      }
      bool exists = await File(ITools.get().videoPath).exists();
      Logs.e('视频：videoPath = ${ITools.get().videoPath}, exists = $exists');
      if (ITools.get().videoPath.isNotEmpty && exists) {
        videoUrl = ITools.get().videoPath;
      } else {
        videoUrl = await HttpDao.get().checkVideoUrl(max: 5);
        HttpDao.get().downloadVideo(videoUrl);
      }
    } catch (e) {
    } finally {
      EasyLoading.dismiss();
      isClick = false;
    }
    Tools.get().jump(PageRealHome(subject: subject, videoUrl: videoUrl));
  }

  void jumpRealExam(int subject) async {
    EasyLoading.show();
    try {
      IQuiz.get().clear();
      //解析参数
      List<QuizMo> list = await QuizUtils.get().getMockQuiz(
          subject,
          ITools.get().trainType(),
          ITools.get().cityId(),
          ITools.get().getUid('0'),
          JkKey.KEY_MOCK,
          true);
      IQuiz.get().quizList = await ITrain.get().checkQuizs(list);
    } catch (e) {
    } finally {
      EasyLoading.dismiss();
      PageRealHome.finish(false);
    }
    Tools.get().jump(PageRealExam(subject: subject));
  }

  void jumpRectify(int sub_id) async {
    EasyLoading.show();
    List<FeedbackTypeMo> list = <FeedbackTypeMo>[];
    try {
      final value = await HttpDao.get().getDict('question_feedback_type');
      list = (value as List<dynamic>)
          .map((e) => FeedbackTypeMo.fromJson(e))
          .toList();
    } catch (e) {
    } finally {
      EasyLoading.dismiss();
    }
    Tools.get().jump(Rectify(sub_id: sub_id, list: list));
  }

  void jumpStudyPlan(int subject) async {
    if (isClick) {
      return;
    }
    isClick = true;
    EasyLoading.show();
    DateTime? examDate;
    try {
      String value =
          await Storage.getString(Storage.examDate + ITools.get().getUid('0'));
      Logs.e("考试日期 date = ${value}");
      if (value.isNotEmpty) {
        examDate = DateUtil.getDateTime(value);
        if (DateChecks.isBefore(examDate!)) {
          examDate = null;
        }
      }
      await FixedData.get().refresh(subject, type: 3);
      await EssenceData.get().refresh(subject);
      await ITrain.get().examRefresh(false);
      await ITrain.get().quesRefresh(false);
    } catch (e) {
      print(e);
    } finally {
      EasyLoading.dismiss();
      isClick = false;
    }

    Tools.get().jump(StudyPlan(subject: subject, examDate: examDate));
  }
}

import 'package:quiz/quiz.dart';

import '../../tools.dart';
import '../http/http.dart';

/**
 * 方法：首页通用数据
 */
class ITools {
  //构造方法
  ITools();

  // 单例方法
  static ITools? instance;

  static ITools get() {
    if (instance == null) {
      instance = ITools();
    }
    return instance!;
  }

  bool isLogin = false;

  //app当前版本
  String version = '3.4.5';

  String videoPath = '';

  bool hideAds = false;

  String? uid;
  String? name;
  String? image;
  String? mobile;
  String? idcard;
  String? gender;
  String? schoolId;
  String? schoolName;
  String? platTrainType;
  int? reqisterDivision;
  bool vip1 = false;
  bool vip4 = false;
  int? authstatus;
  int? platCode;

  void init() {
    // PackageUtils.getAppVersion().then((value) async {
    //   // version = value;
    // });
    ITrain.get().quesRefresh(true);
  }

  void clear() async {
    isLogin = false;
    HttpDao.get().setToken('');
    HttpDao.get().setFirstLogin('');
    uid = null;
    name = null;
    image = null;
    mobile = null;
    idcard = null;
    gender = null;
    schoolId = null;
    schoolName = null;
    platTrainType = null;
    reqisterDivision = null;
    authstatus = null;
    platCode = null;
    vip1 = false;
    vip4 = false;
    IPlatform.get().setIsVip(1, vip1);
    IPlatform.get().setIsVip(4, vip4);
    IEventBus.get().post(IEvent(JkKey.EVENT_SUPPLY_HOURS, ''));
    IHome.get().clear();
  }

  // {"Uid":"69","Sid":"009C42D201","Name":"驾考用户","BindMobile": "***********","Mobile":"***********","Birth":0,"Gender":3,"RegisterDivision":370100,"TopicType":"C1","IsBind":2,"PlatCode":99,"PlatRegisterDate":0,"RealAuthStatus":2,"IsVip":2,"IsRecorded":2,"Vip1":false,"Vip2":false,"Vip3":false,"Vip4":false}
  ///学员信息
  Future<void> setUserAccount(Map<String, dynamic> user,
      {bool isNeedUpdateInfo = true}) async {
    var isUseTopicType = await Storage.getString('useTopicType');
    var isUseDivision = await Storage.getInt('useDivision');
    uid = user['Uid'] ?? '';
    name = user['Name'] ?? '';
    image = user['Image'] ?? '';
    mobile = user['BindMobile'] ?? '';
    if (mobile!.isEmpty) {
      mobile = user['Mobile'] ?? '';
    }
    idcard = user['IdCard'] ?? '';
    int value = user['Gender'] ?? 0;
    gender = value == 1
        ? '男'
        : value == 2
            ? '女'
            : '保密';
    schoolId = user['PlatSchoolId'] ?? '';
    schoolName = user['PlatSchoolName'] ?? '';
    if (isUseTopicType == ''|| isUseTopicType.isEmpty) {
    platTrainType = user['TopicType'] ?? '';
    }else {
      platTrainType = isUseTopicType;
    }

    if(isUseDivision == 0){
      reqisterDivision = user['RegisterDivision'] ?? 0;
    }else{
      reqisterDivision = isUseDivision;
    }
    authstatus = user['RealAuthStatus'] ?? 0;
    platCode = user['PlatCode'] ?? 0;
    vip1 = user['Vip1'] ?? false;
    vip4 = user['Vip4'] ?? false;
    IPlatform.get().setIsVip(1, vip1);
    IPlatform.get().setIsVip(4, vip4);
    Logs.e(
        "uid = ${uid}, name = ${name}, image = ${image}, mobile = ${mobile}, idcard = ${idcard}, gender = ${gender}, schoolId = ${schoolId}, schoolName = ${schoolName}, platTrainType = ${platTrainType}"
        ", reqisterDivision = ${reqisterDivision}, authstatus = ${authstatus}, vip1 = ${vip1}, vip4 = ${vip4}");
    if(isNeedUpdateInfo){
      HttpDao.get().phoneLogin(getMobile('')).then((value) async {
        // 同步登录
        if (value != null) {
          isLogin = true;
          HttpDao.get().setToken(value['accessToken']);
          HttpDao.get().setFirstLogin(value['isFirstLogin']);
          HttpDao.get().queryBaseInfo(check: true);
        }
        Storage.closeTiming();
      });
    }
  }

  /**
   * 方法：设置用户信息
   * uid：用户id
   * name：用户名
   * image：头像
   * mobile：手机号
   * gender：性别
   * schoolId：驾校id
   * schoolName：驾校名称
   * trainType：车型
   * division：城市编码
   * vip：vip状态
   * authstatus：实名认证
   */
  void setUserInfo(
      {String? uid,
      String? name,
      String? image,
      String? mobile,
      String? idcard,
      String? gender,
      String? schoolId,
      String? schoolName,
      String? trainType,
      int? division,
      bool? vip1,
      bool? vip4,
      int? authstatus,
      int? platCode}) {
    if (uid != null) {
      this.uid = uid;
    }
    if (name != null) {
      this.name = name;
    }
    if (image != null) {
      this.image = image;
    }
    if (mobile != null) {
      this.mobile = mobile;
    }
    if (idcard != null) {
      this.idcard = idcard;
    }
    if (gender != null) {
      this.gender = gender;
    }
    if (schoolId != null) {
      this.schoolId = schoolId;
    }
    if (schoolName != null) {
      this.schoolName = schoolName;
    }
    if (trainType != null) {
      this.platTrainType = trainType;
    }
    if (division != null) {
      this.reqisterDivision = division;
    }
    if (vip1 != null) {
      this.vip1 = vip1;
      IPlatform.get().setIsVip(1, vip1);
    }
    if (vip4 != null) {
      this.vip4 = vip4;
      IPlatform.get().setIsVip(4, vip4);
    }
    if (authstatus != null) {
      this.authstatus = authstatus;
    }
    if (platCode != null) {
      this.platCode = platCode;
    }
  }

  ///学员id
  String getUid(String def) {
    return uid ?? def;
  }

  ///学员头像
  String getImage() {
    return image ?? '';
  }

  ///学员名字
  String getName(String def) {
    return name ?? def;
  }

  ///学员手机号
  String getMobile(String def) {
    return mobile ?? def;
  }

  ///学员身份证
  String getIdcard(String def) {
    return idcard ?? def;
  }

  ///学员名字
  String getGender() {
    return gender ?? '保密';
  }

  ///驾校id
  String getSchoolId(String def) {
    return schoolId ?? def;
  }

  ///驾校名字
  String getSchoolName(String def) {
    return schoolName ?? def;
  }

  bool isBindSchool() {
    return getSchoolId('').isNotEmpty;
  }

  ///学员车型
  String trainType() {
    return getType(platTrainType ?? JkKey.C);
  }

  String getType(String type) {
    if (type.isNotEmpty) {
      if (type == JkKey.C || type == JkKey.C2 || type == JkKey.C3) {
        return JkKey.C;
      } else if (type == JkKey.D || type == JkKey.E || type == JkKey.F) {
        return JkKey.D;
      } else if (type == JkKey.B2 || type == JkKey.A2) {
        return JkKey.B2;
      } else if (type == JkKey.A1 || type == JkKey.A3 || type == JkKey.B1) {
        return JkKey.A1;
      } else if (type == JkKey.C6) {
        return JkKey.C6;
      }
    }
    return JkKey.C;
  }

  ///检查vip
  bool isVips() {
    return isVip(1) || isVip(4);
  }

  ///检查vip
  bool isVip(int subject) {
    // return true;
    if (subject == 1) {
      return vip1;
    } else if (subject == 4) {
      return vip4;
    }
    return false;
  }

  ///实名认证
  int realAuthstatus() {
    return authstatus ?? 0;
  }

  ///实名认证
  int division() {
    return reqisterDivision ?? 0;
  }

  List<String> cityId() {
    String code = division() != 0 ? division().toString() : '';
    if (code.isNotEmpty && code.length > 4) {
      code = code.substring(0, 4);
    }
    List<String> cityid = <String>[];
    setProvinceId(cityid, code);
    setCityId(cityid, code);
    return cityid;
  }

  void setProvinceId(List<String> cityid, String code) {
    if (code.length > 2) {
      cityid.add(code.substring(0, 2));
    }
  }

  void setCityId(List<String> cityid, String code) {
    cityid.add(code);
  }
}

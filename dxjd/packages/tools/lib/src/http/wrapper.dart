import 'dart:convert';

import 'package:dio/dio.dart';

import '../../tools.dart';
import '../bean/base.dart';
import 'utils.dart';

/**
 * 类：http回执
 */
class DioWrapper {
  static BaseMo errorWrapper(Object e) {
    return BaseMo(
        0, e is DioError ? _dioErrorWrapper(e) : '访问失败，请检查网络后再试', {}, 0, 0);
  }

  /**
   * 方法：访问回执异常
   * error：异常回执数据
   */
  static String _dioErrorWrapper(DioError error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return '连接服务器超时';
      case DioExceptionType.sendTimeout:
        return '连接服务器超时';
      case DioExceptionType.receiveTimeout:
        return '连接服务器超时';
      case DioExceptionType.cancel:
        return '连接被取消';
      default:
        return '访问失败，请检查网络后再试';
    }
  }

  static bool request = false;
  static String? url401;
  static dynamic params401;
  static String? method401;

  /**
   * 方法：访问回执成功
   * url：地址
   * response：回执数据
   */
  static Future<BaseMo> responseWrapper(
      String url, dynamic params, Response response,
      {String? method = ''}) async {
    if (url.contains(ApiUrl.phoneLogin)) {
      url401 = url;
      params401 = params;
      method401 = method;
    }
    // 此处如果数据比较大，可以使用 compute 放在后台计算
    final data = jsonDecode(response.data);
    print('url = ${url}, params = ${params}, data = ${data}');
    if (response.statusCode == 200) {
      BaseMo mo = BaseMo.fromJson(data);
      if (mo.msg.isNotEmpty && mo.msg.contains('token')) {
        if (mo.code == 500) {
          mo.msg = '';
        }
      }
      if (mo.code == 401) {
        if (request) {
          request = false;
          final result =
              await HttpDao.get().phoneLogin(ITools.get().getMobile(''));
          if (result != null) {
            HttpDao.get().setToken(result['accessToken']);
            HttpDao.get().setFirstLogin(result['isFirstLogin']);
            return await HttpUtils.instance.request(
              url,
              method: method,
              params: params,
            );
          }
        } else {
          Storage.setInt('useDivision',0);
          await Storage.setString('useTopicType', '');
          ITools.get().clear();
        }
      }
      return mo;
    } else {
      var msg = data['msg'] ?? '';
      return BaseMo(0, msg, {}, 0, 0);
    }
  }

  /**
   * 方法：访问回执成功
   * url：地址
   * response：回执数据
   */
  static BaseMo uploadWrapper(String url, dynamic params, Response response) {
    // 此处如果数据比较大，可以使用 compute 放在后台计算
    final data = jsonDecode(response.data);
    print('url = ${url}, params = ${params}, data = ${data}');
    if (response.statusCode == 200) {
      BaseMo mo = BaseMo.fromJson(data);
      if (mo.msg.isNotEmpty && mo.msg.contains('token')) {
        if (mo.code == 500) {
          mo.msg = '';
        }
      }
      if (mo.code == 401) {
        Storage.setString('useTopicType', '');
        Storage.setInt('useDivision', 0);
        ITools.get().clear();
      }
      return mo;
    } else {
      var msg = data['msg'] ?? '';
      return BaseMo(0, msg, {}, 0, 0);
    }
  }
}

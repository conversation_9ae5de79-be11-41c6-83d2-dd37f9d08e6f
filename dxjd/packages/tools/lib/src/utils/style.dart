import 'package:flutter/material.dart';

class Style {
  Style._();

  static const Color bg = Color(0xFFF6F5F5);
  static const Color bg4 = Color(0xFFF2F2F2);
  static const Color bg5 = Color(0xFFF4F1EA);
  static const Color bg11 = Color(0xFFF4F4EF);
  static const Color line = Color(0xFFF4F8FC);
  static const Color dark = Color(0xFFD4D4D4);

  static const Color white_trans = Color(0xEFFFFFFF);
  static const Color white_60 = Color(0xB3FFFFFF);

  static const Color blue = Color(0xFF268FF7);
  static const Color blue1 = Color(0xFF75C1FE);
  static const Color blue3 = Color(0xFFE8F2FB);
  static const Color blue3_60 = Color(0xB3E8F2FB);
  static const Color blue4 = Color(0xFF00A0DF);
  static const Color blue7 = Color(0xFF319AFB);
  static const Color blue12 = Color(0xFF4BC7F5);
  static const Color blue16 = Color(0xFF0096F8);
  static const Color blue17 = Color(0xFF1B35E2);
  static const Color blue18 = Color(0xFF51A9F1);
  static const Color blue19 = Color(0xFF4A9AFF);
  static const Color blue26 = Color(0xFF0074E5);

  static const Color red = Color(0xFFFF4D4D);
  static const Color red2 = Color(0xFFEC1C24);
  static const Color red5 = Color(0xFFF76464);
  static const Color jdb_red = Color(0xFFFF0000);

  static const Color jdb_green2 = Color(0xFF009919);

  static const Color yellow = Color(0xFFFFBD24);
  static const Color yellow1 = Color(0xFFFFC600);

  static const Color text1 = Color(0xFF1A1A1A);
  static const Color text2 = Color(0xFF4E4E4E);
  static const Color text3 = Color(0xFFAAAAAA);
  static const Color text4 = Color(0xFF0A2240);
  static const Color text5 = Color(0xFF0B2241);
  static const Color text6 = Color(0xFF131313);
  static const Color text7 = Color(0xFF878787);

  static const Color title = Color(0xFFEEEEEE);

  static const Color brown = Color(0xFF742000);

  static const Color green1 = Color(0xFF76F293);
  static const Color brown1 = Color(0xFF7D2F09);

  static const Color p_blue = Color(0xFF318DD7);

  static const Color p_red = Color(0xFFEC4133);
  static const Color p_red1 = Color(0x33FFB087);

  static const Color p_brown = Color(0xFFCF5A1A);

  static const Color p_text = Color(0xFF686868);
  static const Color p_text1 = Color(0xFF757575);
  static const Color p_text2 = Color(0xFF5E5E5E);

  ///主色调
  static const Color primaryColor = Color(0xFF6156F4);

  ///默认淡灰色
  static const Color defaultTextLightGreyColor = Color(0xff999999);

  ///默认黑色
  static const Color defaultTextBlackColor = Color(0xFF333333);

  static const BoxDecoration plan_box_blue_r6 = BoxDecoration(
    color: Color(0xFFF4F7F9),
    borderRadius: BorderRadius.all(Radius.circular(6)),
    boxShadow: [
      BoxShadow(
        color: Color(0x40000000),
        offset: Offset(0, 4),
        blurRadius: 2,
        spreadRadius: 0.5,
      ),
    ],
  );

  static const BoxDecoration plan_box_red2yellow_h_r50 = BoxDecoration(
    borderRadius: BorderRadius.all(Radius.circular(50)),
    gradient: LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        Color(0xFFFF78BA),
        Color(0xFFFE6E8B),
        Color(0xFFFC645B),
        Color(0xFFFC9F5D),
        Color(0xFFFCD95E)
      ],
    ),
    boxShadow: [
      BoxShadow(
        color: Color(0x88FFFFFF),
        offset: Offset(0, 3),
        blurRadius: 3,
        spreadRadius: 1,
      ),
    ],
  );

  static const TextStyle style_text6_8_bold = TextStyle(
    color: text6,
    fontSize: 14,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_text6_9_5_bold = TextStyle(
    color: text6,
    fontSize: 14,
    fontWeight: FontWeight.bold,
  );

  static const BoxDecoration plan_vip_box = BoxDecoration(
    image: DecorationImage(
      image: AssetImage('assets/home_img/ic_plan_vip_box.png'),
      fit: BoxFit.fill,
    ),
  );

  static const BoxDecoration plan_box_blue1_r50 = BoxDecoration(
    // color: Style.p_blue,
    gradient: LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        Color(0xFF1992EF),
        Color(0xFF61B3F5),
      ],
    ),
    borderRadius: BorderRadius.all(Radius.circular(50)),
    boxShadow: [
      BoxShadow(
        color: Color(0x88FFFFFF),
        offset: Offset(0, 3),
        blurRadius: 3,
        spreadRadius: 1,
      ),
    ],
  );

  static const TextStyle style_text6_8_5_bold = TextStyle(
    color: text6,
    fontSize: 14,
    fontWeight: FontWeight.bold,
  );
  static const BoxDecoration plan_border_blue_r7 = BoxDecoration(
    color: Color(0xFFF4F7F9),
    border: Border.fromBorderSide(BorderSide(
        width: 1, color: Color(0xFFC9C9C9), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(7)),
  );

  static const BoxDecoration style_box_white_r10 = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.all(Radius.circular(10)),
  );

  static const TextStyle plan_blue_20_bold = TextStyle(
    color: p_blue,
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle plan_blue_12_w500 = TextStyle(
    color: p_blue,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle plan_blue_16_w500 = TextStyle(
    color: p_blue,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text7_16_bold = TextStyle(
    color: text7,
    fontSize: 16,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_text7_10_w500 = TextStyle(
    color: text7,
    fontSize: 10,
    fontWeight: FontWeight.w500,
  );

  static const BoxDecoration plan_box_bg_r50 = BoxDecoration(
    color: Color(0xFFB3B3B3),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration plan_border_bg_r15 = BoxDecoration(
    color: Color(0xFFFFFFFF),
    // border: Border.fromBorderSide(
    //     BorderSide(width: 1, color: Colors.white, style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(15)),
    // boxShadow: [
    //   BoxShadow(
    //     color: Color(0x66ABC0CC),
    //     offset: Offset(-1, 1),
    //     blurRadius: 3,
    //     spreadRadius: 0.5,
    //   ),
    // ],
  );

  static const TextStyle style_text6_20_bold = TextStyle(
    color: text6,
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );

  static const BoxDecoration plan_box_blue2_r50 = BoxDecoration(
    color: Color(0xFF5AADFF),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration plan_box_blue3_r50 = BoxDecoration(
    color: Color(0xFF64B2FF),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration plan_box_blue_h_ab_r6 = BoxDecoration(
    borderRadius: BorderRadius.only(
      topLeft: Radius.circular(6),
      topRight: Radius.circular(6),
    ),
    gradient: LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [Color(0xFF1991F0),
        // Color(0xFF8CBCFD),
        Color(0xFF60B3F7)],
    ),
  );

  static const TextStyle style_text6_14_w500 = TextStyle(
    color: text6,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle plan_blue_14_bold = TextStyle(
    color: p_blue,
    fontSize: 14,
    fontWeight: FontWeight.bold,
  );

  static const BoxDecoration plan_box_blue_h_r50 = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.all(Radius.circular(50)),
    // gradient: LinearGradient(
    //   begin: Alignment.centerLeft,
    //   end: Alignment.centerRight,
    //   colors: [Color(0xFF68A6FB), Color(0xFF8CBCFD), Color(0xFFAFD1FF)],
    // ),
    // boxShadow: [
    //   BoxShadow(
    //     color: Color(0x88FFFFFF),
    //     offset: Offset(0, 3),
    //     blurRadius: 3,
    //     spreadRadius: 1,
    //   ),
    // ],
  );

  static const TextStyle style_text3_10 = TextStyle(
    color: text3,
    fontSize: 10,
  );

  static const TextStyle style_white_12 = TextStyle(
    color: Colors.white,
    fontSize: 12,
  );

  static const BoxDecoration plan_box_red_r50 = BoxDecoration(
    color: Color(0xFFFF6F36),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const TextStyle plan_red_30_bold = TextStyle(
    color: p_red,
    fontSize: 30,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle plan_red_20_bold = TextStyle(
    color: p_red,
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle plan_red_18_bold = TextStyle(
    color: p_red,
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle plan_red_16_bold = TextStyle(
    color: p_red,
    fontSize: 16,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle plan_red_15_bold = TextStyle(
    color: p_red,
    fontSize: 15,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle plan_brown_16_bold = TextStyle(
    color: p_brown,
    fontSize: 16,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle plan_brown_12_w500 = TextStyle(
    color: p_brown,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle plan_text_8 = TextStyle(
    color: p_text,
    fontSize: 8,
  );

  static const TextStyle plan_text1_7 = TextStyle(
    // color: p_text1,
    color: Color(0xffB2B2B2),
    fontSize: 12,
  );

  static const TextStyle plan_text1_h8_w500 = TextStyle(
    color: Color(0xffB2B2B2),
    fontSize: 12,
    height: 1.5,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle plan_text2_11_w500 = TextStyle(
    color: Color(0xff808080),
    fontSize: 14,
    fontWeight: FontWeight.w500,
    fontFamily: 'PingFangSC-Medium',
  );

  static const BoxDecoration plan_border_red_r8 = BoxDecoration(
    color: Color(0xFFFFF9F6),
    border: Border.fromBorderSide(
        BorderSide(width: 1, color: p_red1, style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const TextStyle jdb_red_h22_w500 = TextStyle(
    color: jdb_red,
    fontSize: 22,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle jdb_red_h20_w500 = TextStyle(
    color: jdb_red,
    fontSize: 20,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle jdb_red_h18_w500 = TextStyle(
    color: jdb_red,
    fontSize: 18,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle jdb_red_h16_w500 = TextStyle(
    color: jdb_red,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle jdb_green2_22_w500 = TextStyle(
    color: jdb_green2,
    fontSize: 22,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle jdb_green2_20_w500 = TextStyle(
    color: jdb_green2,
    fontSize: 20,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle jdb_green2_18_w500 = TextStyle(
    color: jdb_green2,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle jdb_green2_16_w500 = TextStyle(
    color: jdb_green2,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_24_bold = TextStyle(
    color: Colors.white,
    fontSize: 24,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_white_24_w500 = TextStyle(
    color: Colors.white,
    fontSize: 24,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_20_w500 = TextStyle(
    color: Colors.white,
    fontSize: 20,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_18_bold = TextStyle(
    color: Colors.white,
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_white_18_w500 = TextStyle(
    color: Colors.white,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_16_w500 = TextStyle(
    color: Colors.white,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_15_w500 = TextStyle(
    color: Colors.white,
    fontSize: 15,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_14_w500 = TextStyle(
    color: Colors.white,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_h14_w500 = TextStyle(
    color: Colors.white,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_white60_14_w500 = TextStyle(
    color: white_60,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_14 = TextStyle(
    color: Colors.white,
    fontSize: 14,
  );

  static const TextStyle style_white_13_w500 = TextStyle(
    color: Colors.white,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_12_w500 = TextStyle(
    color: Colors.white,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white60_12_w500 = TextStyle(
    color: white_60,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_10_w500 = TextStyle(
    color: Colors.white,
    fontSize: 9.8,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_9_w500 = TextStyle(
    color: Colors.white,
    fontSize: 9,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_banner = TextStyle(
    color: Colors.white,
    fontSize: 9.5,
    fontWeight: FontWeight.bold,
    letterSpacing: 0.05,
  );

  static const TextStyle style_blue_40_w500 = TextStyle(
    color: blue,
    fontSize: 40,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue_20_bold = TextStyle(
    color: blue,
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_blue_18_w500 = TextStyle(
    color: blue,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue_16_w500 = TextStyle(
    color: blue,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue_14_w500 = TextStyle(
    color: blue,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue_h14_w500 = TextStyle(
    color: blue,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_blue_14 = TextStyle(
    color: blue,
    fontSize: 14,
  );

  static const TextStyle style_blue_13_w500 = TextStyle(
    color: blue,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue_12_w500 = TextStyle(
    color: blue,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue1_12_w500 = TextStyle(
    color: blue1,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue4_18_w500 = TextStyle(
    color: blue4,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue4_12_w500 = TextStyle(
    color: blue4,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_40_w500 = TextStyle(
    color: red,
    fontSize: 40,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_28_w500 = TextStyle(
    color: red,
    fontSize: 28,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_18_w500 = TextStyle(
    color: red,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_h18_w500 = TextStyle(
    color: red,
    fontSize: 18,
    height: 1.5,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_16_w500 = TextStyle(
    color: red,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_h16_bold = TextStyle(
    color: red,
    fontSize: 16,
    height: 1.5,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_red_14_w500 = TextStyle(
    color: red,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_h14_w500 = TextStyle(
    color: red,
    fontSize: 14,
    height: 1.5,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_14 = TextStyle(
    color: red,
    fontSize: 14,
  );

  static const TextStyle style_red_13_w500 = TextStyle(
    color: red,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_12_w500 = TextStyle(
    color: red,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_12 = TextStyle(
    color: red,
    fontSize: 12,
  );

  static const TextStyle style_red_10_w500 = TextStyle(
    color: red,
    fontSize: 10,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red2_60_w500 = TextStyle(
    color: red2,
    fontSize: 60,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red2_24_w500 = TextStyle(
    color: red2,
    fontSize: 24,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red2_16_w500 = TextStyle(
    color: red2,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_yellow_12_w500 = TextStyle(
    color: yellow,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_30_bold = TextStyle(
    color: text1,
    fontSize: 30,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_text1_30_w500 = TextStyle(
    color: text1,
    fontSize: 30,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_30 = TextStyle(
    color: text1,
    fontSize: 30,
  );

  static const TextStyle style_text1_24_w500 = TextStyle(
    color: text1,
    fontSize: 24,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_20_w500 = TextStyle(
    color: text1,
    fontSize: 20,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_20_bold = TextStyle(
    color: text1,
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_text1_18_w500 = TextStyle(
    color: text1,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_16_bold = TextStyle(
    color: text1,
    fontSize: 16,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_text1_16_w500 = TextStyle(
    color: text1,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_h16_w500 = TextStyle(
    color: text1,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text1_15_w500 = TextStyle(
    color: text1,
    fontSize: 15,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_14_w500 = TextStyle(
    color: text1,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_h14_w500 = TextStyle(
    color: text1,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text1_14 = TextStyle(
    color: text1,
    fontSize: 14,
  );

  static const TextStyle style_text1_h15_14 = TextStyle(
    color: text1,
    fontSize: 14,
    height: 1.5,
  );

  static const TextStyle style_text1_13_w500 = TextStyle(
    color: text1,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_12_w500 = TextStyle(
    color: text1,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_h12_w500 = TextStyle(
    color: text1,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text1_12 = TextStyle(
    color: text1,
    fontSize: 12,
  );

  static const TextStyle style_text2_18_w500 = TextStyle(
    color: text2,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text2_16_w500 = TextStyle(
    color: text2,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text2_14_w500 = TextStyle(
    color: text2,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text2_14 = TextStyle(
    color: text2,
    fontSize: 14,
  );

  static const TextStyle style_text2_h13_14_w500 = TextStyle(
    color: text2,
    fontSize: 13.5,
    height: 1.3,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text2_h15_14 = TextStyle(
    color: text2,
    fontSize: 14,
    height: 1.5,
  );

  static const TextStyle style_text2_12_w500 = TextStyle(
    color: text2,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text2_12 = TextStyle(
    color: text2,
    fontSize: 12,
  );

  static const TextStyle style_text3_14_w500 = TextStyle(
    color: text3,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text3_h14_w500 = TextStyle(
    color: text3,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text3_14 = TextStyle(
    color: text3,
    fontSize: 14,
  );

  static const TextStyle style_text3_12_w500 = TextStyle(
    color: text3,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text3_12 = TextStyle(
    color: text3,
    fontSize: 12,
  );

  static const TextStyle style_text4_16_w500 = TextStyle(
    color: text4,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text4_12_w500 = TextStyle(
    color: text4,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text5_10_w500 = TextStyle(
    color: text5,
    fontSize: 10,
    fontWeight: FontWeight.w500,
  );

  static const BoxDecoration style_bg = BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [Colors.white, Style.bg, Style.bg, Style.bg],
    ),
  );

  static const BoxDecoration style_box_bg_r8 = BoxDecoration(
    color: Style.bg,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_box_bg_ab_r8 = BoxDecoration(
    color: Style.bg,
    borderRadius: BorderRadius.only(
      topLeft: Radius.circular(8),
      topRight: Radius.circular(8),
    ),
  );

  static const BoxDecoration style_border_dark_r50 = BoxDecoration(
    border: Border.fromBorderSide(
        BorderSide(width: 1, color: Style.dark, style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration style_box_line_r8 = BoxDecoration(
    color: Style.line,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_box_white_r50 = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration style_box_white_r12 = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.all(Radius.circular(12)),
  );

  static const BoxDecoration style_box_white_r8 = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_box_white_r4 = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.all(Radius.circular(4)),
  );

  static const BoxDecoration style_shadow_white1_r8 = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.all(Radius.circular(8)),
    boxShadow: [
      BoxShadow(
        color: Color(0x1A000000),
        offset: Offset(0, 3),
        blurRadius: 3,
        spreadRadius: 1,
      ),
    ],
  );

  static const BoxDecoration style_box_white_ab_r12 = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.only(
      topLeft: Radius.circular(12),
      topRight: Radius.circular(12),
    ),
  );

  static const BoxDecoration style_border_white_top = BoxDecoration(
    color: Colors.white,
    boxShadow: [
      BoxShadow(
        color: Color(0x11000000),
        offset: Offset(-1, -1),
        blurRadius: 2,
        spreadRadius: 1,
      ),
    ],
  );

  static const BoxDecoration style_box_red_r50 = BoxDecoration(
    color: Style.red,
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration style_box_red_r5 = BoxDecoration(
    color: Style.red,
    borderRadius: BorderRadius.all(Radius.circular(5)),
  );

  static const BoxDecoration style_border_white_bottom = BoxDecoration(
    border: Border(
        bottom: BorderSide(width: 1, color: red2, style: BorderStyle.solid)),
  );

  static const BoxDecoration style_box_blue3_r8 = BoxDecoration(
    color: Style.blue3,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_box_blue3_60_r8 = BoxDecoration(
    color: Style.blue3_60,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_box_blue4_r8 = BoxDecoration(
    color: Style.blue4,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_border_blue4_r8 = BoxDecoration(
    color: Colors.white,
    border: Border.fromBorderSide(
        BorderSide(width: 1, color: Style.blue4, style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_border_text3_r50 = BoxDecoration(
    // color: Colors.white,
    border: Border.fromBorderSide(
        BorderSide(width: 1, color: Style.text3, style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(50)),
    boxShadow: [
      BoxShadow(
        color: Color(0x66FFFFFF),
        offset: Offset(0, 3),
        blurRadius: 3,
        spreadRadius: 1,
      ),
    ],
  );

  static const BoxDecoration style_box_dark_r50 = BoxDecoration(
    color: Style.dark,
    borderRadius: BorderRadius.all(Radius.circular(50)),
    boxShadow: [
      BoxShadow(
        color: Color(0x66FFFFFF),
        offset: Offset(0, 3),
        blurRadius: 3,
        spreadRadius: 1,
      ),
    ],
  );

  static const BoxDecoration style_border_blue_r50 = BoxDecoration(
    color: Colors.white,
    border: Border.fromBorderSide(
        BorderSide(width: 1, color: Style.blue, style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration style_box_blue_r50 = BoxDecoration(
    color: Style.blue,
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration style_box_blue_v_r50 = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.all(Radius.circular(50)),
    gradient: LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [Style.blue, Style.blue1],
    ),
  );

  static const BoxDecoration style_box_blue_pay = BoxDecoration(
    color: Style.blue,
    borderRadius: BorderRadius.only(
      topLeft: Radius.circular(50),
      topRight: Radius.circular(50),
      bottomRight: Radius.circular(50),
    ),
    gradient: LinearGradient(
      begin: Alignment.bottomCenter,
      end: Alignment.topCenter,
      colors: [Color(0xFF006BE4), Color(0xFF319AFB), Color(0xFF319AFB)],
    ),
  );

  static const BoxDecoration style_box_bg_r50 = BoxDecoration(
    color: Style.bg,
    borderRadius: BorderRadius.all(Radius.circular(50)),
    boxShadow: [
      BoxShadow(
        color: Color(0x66FFFFFF),
        offset: Offset(0, 3),
        blurRadius: 3,
        spreadRadius: 1,
      ),
    ],
  );

  static const BoxDecoration style_teb_bg = BoxDecoration(
    border: Border.fromBorderSide(
        BorderSide(width: 2.5, color: Style.blue7, style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_teb_indicator = BoxDecoration(
    color: Style.blue12,
    border: Border.fromBorderSide(
        BorderSide(width: 2.5, color: Style.blue7, style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_box_sort_blue = BoxDecoration(
    borderRadius: BorderRadius.all(Radius.circular(12)),
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [Color(0xFFB3DAFC), Color(0xFF268FF7)],
    ),
  );

  static const BoxDecoration style_box_sort_yellow = BoxDecoration(
    borderRadius: BorderRadius.all(Radius.circular(12)),
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [Color(0xFFFCDE99), Color(0xFFFFBD24)],
    ),
  );

  static const BoxDecoration style_box_sort_green = BoxDecoration(
    borderRadius: BorderRadius.all(Radius.circular(12)),
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [Color(0xFF96F692), Color(0xFF45D63F)],
    ),
  );

  static const BoxDecoration style_box_sort_red = BoxDecoration(
    borderRadius: BorderRadius.all(Radius.circular(12)),
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [Color(0xFFFEAAAA), Color(0xFFFF4D4D)],
    ),
  );

  static const BoxDecoration style_box_yellow_r4 = BoxDecoration(
    color: Style.yellow,
    borderRadius: BorderRadius.all(Radius.circular(4)),
  );

  static const BoxDecoration style_box_yellow2_r50 = BoxDecoration(
    color: Color(0xFFFFCB63),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration style_btn_blue_max = BoxDecoration(
    image: DecorationImage(
      image: AssetImage('assets/train/ic_btn_max.png'),
      fit: BoxFit.fill,
    ),
  );

  static const BoxDecoration style_btn_blue_big = BoxDecoration(
    image: DecorationImage(
      image: AssetImage('assets/train/ic_btn_big.png'),
      fit: BoxFit.fill,
    ),
  );

  static const BoxDecoration style_box_green_r4 = BoxDecoration(
    color: Color(0xFF5FEC7B),
    borderRadius: BorderRadius.all(Radius.circular(4)),
  );

  static const BoxDecoration style_box_red_r4 = BoxDecoration(
    color: Style.red,
    borderRadius: BorderRadius.all(Radius.circular(4)),
  );

  static const BoxDecoration style_btn_blue_middle = BoxDecoration(
    image: DecorationImage(
      image: AssetImage('assets/train/ic_btn_middle.png'),
      fit: BoxFit.fill,
    ),
  );

  static const BoxDecoration style_btn_blue_small = BoxDecoration(
    image: DecorationImage(
      image: AssetImage('assets/train/ic_btn_small.png'),
      fit: BoxFit.fill,
    ),
  );

  static const BoxDecoration style_btn_blue_min = BoxDecoration(
    image: DecorationImage(
      image: AssetImage('assets/train/ic_btn_min.png'),
      fit: BoxFit.fill,
    ),
  );

  static const BoxDecoration style_submit_paper_box = BoxDecoration(
    image: DecorationImage(
      image: AssetImage('assets/exercise/ic_submit_paper_box.png'),
      fit: BoxFit.fill,
    ),
  );

  static const BoxDecoration style_real_video = BoxDecoration(
    image: DecorationImage(
      image: AssetImage('assets/exercise/ic_real_video.png'),
      fit: BoxFit.fill,
    ),
  );

  static const BoxDecoration style_bg_red_wrong = BoxDecoration(
    image: DecorationImage(
      image: AssetImage('assets/train/ic_bg_red_wrong.png'),
      fit: BoxFit.fill,
    ),
  );

  static const TextStyle style_trans_14_w500 = TextStyle(
    color: Colors.transparent,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_8_w500 = TextStyle(
    color: Colors.white,
    fontSize: 8,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_white_6_5_w500 = TextStyle(
    color: Colors.white,
    fontSize: 6.5,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue_22_w500 = TextStyle(
    color: blue,
    fontSize: 22,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue_20_w500 = TextStyle(
    color: blue,
    fontSize: 20,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue_h20_w500 = TextStyle(
    color: blue,
    fontSize: 20,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_blue_h18_w500 = TextStyle(
    color: blue,
    fontSize: 18,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_blue_h16_w500 = TextStyle(
    color: blue,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_blue_h12_w500 = TextStyle(
    color: blue,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_blue17_h14_w500 = TextStyle(
    color: blue17,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_blue17_12_w500 = TextStyle(
    color: blue17,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue17_10_w500 = TextStyle(
    color: blue17,
    fontSize: 10,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue17_8_w500 = TextStyle(
    color: blue17,
    fontSize: 8,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_blue19_15_w500 = TextStyle(
    color: blue19,
    fontSize: 15,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_30_bold = TextStyle(
    color: red,
    fontSize: 30,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_red_22_w500 = TextStyle(
    color: red,
    fontSize: 22,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red_20_w500 = TextStyle(
    color: red,
    fontSize: 20,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red5_15_w500 = TextStyle(
    color: red5,
    fontSize: 15,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red5_12_w500 = TextStyle(
    color: red5,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red5_12_bold = TextStyle(
    color: red5,
    fontSize: 12,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_red5_9_w500 = TextStyle(
    color: red5,
    fontSize: 9,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red5_8_w500 = TextStyle(
    color: red5,
    fontSize: 8,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_red5_6_5_w500 = TextStyle(
    color: red5,
    fontSize: 6.5,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_green1_22_w500 = TextStyle(
    color: green1,
    fontSize: 22,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_green1_20_w500 = TextStyle(
    color: green1,
    fontSize: 20,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_green1_18_w500 = TextStyle(
    color: green1,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_green1_16_w500 = TextStyle(
    color: green1,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_brown1_18_w500 = TextStyle(
    color: brown1,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_brown1_16_w500 = TextStyle(
    color: brown1,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_brown1_14_w500 = TextStyle(
    color: brown1,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_brown1_12_w500 = TextStyle(
    color: brown1,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_brown2_15_w500 = TextStyle(
    color: brown,
    fontSize: 15,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_h22_w500 = TextStyle(
    color: text1,
    fontSize: 22,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text1_22_w500 = TextStyle(
    color: text1,
    fontSize: 22,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_h20_w500 = TextStyle(
    color: text1,
    fontSize: 20,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text1_18_bold = TextStyle(
    color: text1,
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle style_text1_h18_w500 = TextStyle(
    color: text1,
    fontSize: 18,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text1_h13_w500 = TextStyle(
    color: text1,
    fontSize: 13,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text1_9_w500 = TextStyle(
    color: text1,
    fontSize: 9,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_8_w500 = TextStyle(
    color: text1,
    fontSize: 8,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_7_w500 = TextStyle(
    color: text1,
    fontSize: 7,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_6_5_w500 = TextStyle(
    color: text1,
    fontSize: 6.5,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text1_5_w500 = TextStyle(
    color: text1,
    fontSize: 5,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text2_h18_w500 = TextStyle(
    color: text2,
    fontSize: 18,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text2_h16_w500 = TextStyle(
    color: text2,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text2_h14_w500 = TextStyle(
    color: text2,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text2_h12_w500 = TextStyle(
    color: text2,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text3_h18_w500 = TextStyle(
    color: text3,
    fontSize: 18,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text3_18_w500 = TextStyle(
    color: text3,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text3_16_w500 = TextStyle(
    color: text3,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle style_text3_h16_w500 = TextStyle(
    color: text3,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const TextStyle style_text3_h12_w500 = TextStyle(
    color: text3,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  static const BoxDecoration style_border_line_r3 = BoxDecoration(
    border: Border.fromBorderSide(BorderSide(
        width: 1, color: Color(0xFFDEDEDE), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(3)),
  );

  static const BoxDecoration style_box_white_trans = BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        Color(0xCCFFFFFF),
        Color(0xEEFFFFFF),
        Color(0xF8FFFFFF),
        Color(0xFCFFFFFF),
        Color(0xFFFFFFFF),
        Color(0xFFFFFFFF),
        Color(0xFFFFFFFF),
        Color(0xFFFFFFFF),
        Color(0xFFFFFFFF),
        Color(0xFFFFFFFF)
      ],
    ),
  );

  static const BoxDecoration style_box_white_trans1 = BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [Color(0xFFFFFFFF), Color(0xFFFFFFFF)],
    ),
  );

  static const BoxDecoration style_box_brown_gradient_r50 = BoxDecoration(
    color: Style.blue,
    borderRadius: BorderRadius.all(Radius.circular(50)),
    gradient: LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [Color(0xFFE39C60), Color(0xFFFCEADA), Color(0xFFE39C60)],
    ),
  );

  static const BoxDecoration style_teb_line = BoxDecoration(
    color: Style.blue3_60,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_teb_indicator_line = BoxDecoration(
    color: Style.blue1,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_border_bg1_r50 = BoxDecoration(
    border: Border.fromBorderSide(BorderSide(
        width: 1.5, color: Color(0xFF707070), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(50)),
    boxShadow: [
      BoxShadow(
        color: Color(0x66FFFFFF),
        offset: Offset(0, 3),
        blurRadius: 3,
        spreadRadius: 1,
      ),
    ],
  );

  static const BoxDecoration option_none = BoxDecoration(
    border: Border.fromBorderSide(
        BorderSide(width: 1, color: Style.text3, style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration option_none3 = BoxDecoration(
    border: Border.fromBorderSide(
        BorderSide(width: 1, color: Style.text3, style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration option_select = BoxDecoration(
    image: DecorationImage(
      image: AssetImage('assets/exercise/ic_option_select.png'),
      fit: BoxFit.fill,
    ),
  );

  static const BoxDecoration option_correct = BoxDecoration(
    color: Style.blue,
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration option_correct3 = BoxDecoration(
    color: Style.blue,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration option_wrong = BoxDecoration(
    color: Style.red,
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration option_wrong3 = BoxDecoration(
    color: Style.red,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration num_none = BoxDecoration(
    border: Border.fromBorderSide(BorderSide(
        width: 1, color: Color(0xFF707070), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration num_none_current = BoxDecoration(
    border: Border.fromBorderSide(BorderSide(
        width: 1, color: Color(0xFF1A1A1A), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration num_correct = BoxDecoration(
    border: Border.fromBorderSide(BorderSide(
        width: 1, color: Color(0xFF45D63F), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration num_correct_current = BoxDecoration(
    border: Border.fromBorderSide(BorderSide(
        width: 1, color: Color(0xFF00CA52), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration num_wrong = BoxDecoration(
    border: Border.fromBorderSide(BorderSide(
        width: 1, color: Color(0xFFFF4D4D), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration num_wrong_current = BoxDecoration(
    border: Border.fromBorderSide(BorderSide(
        width: 1, color: Color(0xFFF23555), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration style_option_unselected_r3 = BoxDecoration(
    border: Border.fromBorderSide(BorderSide(
        width: 1, color: Color(0xFFEEEEEE), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(3)),
  );

  static const BoxDecoration style_option_selected_r3 = BoxDecoration(
    border: Border.fromBorderSide(BorderSide(
        width: 1, color: Color(0xFF0074E5), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(3)),
  );

  static const BoxDecoration style_box_blue16_r8 = BoxDecoration(
    color: Style.blue16,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration style_box_blue16_r4 = BoxDecoration(
    color: Style.blue16,
    borderRadius: BorderRadius.all(Radius.circular(4)),
  );

  static const BoxDecoration style_box_blue16_ab_r8 = BoxDecoration(
    color: Style.blue16,
    borderRadius: BorderRadius.only(
      topLeft: Radius.circular(8),
      topRight: Radius.circular(8),
    ),
  );

  static const BoxDecoration style_box_brown_cd_r8 = BoxDecoration(
    color: Color(0xFFC0AFA5),
    borderRadius: BorderRadius.only(
      bottomLeft: Radius.circular(8),
      bottomRight: Radius.circular(8),
    ),
  );

  static const BoxDecoration style_quiz_answer_r3 = BoxDecoration(
    color: Colors.white,
    border: Border.fromBorderSide(BorderSide(
        width: 1, color: Color(0xFFDEDEDE), style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(3)),
  );

  static const BoxDecoration style_border_dark_r0 = BoxDecoration(
    color: Style.bg11,
    border: Border.fromBorderSide(
        BorderSide(width: 1, color: Style.dark, style: BorderStyle.solid)),
    borderRadius: BorderRadius.all(Radius.circular(0)),
  );
}

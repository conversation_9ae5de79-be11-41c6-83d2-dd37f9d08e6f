import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';

import '../../tools.dart';

class Tools {
  //构造方法
  Tools();

  // 单例方法
  static Tools? instance;

  static Tools get() {
    if (instance == null) {
      instance = Tools();
    }
    return instance!;
  }

  //设置横屏
  static setLandscape() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: []);
    await SystemChrome.setPreferredOrientations(
        [DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    // if (Platform.isAndroid) {
    //   //关闭状态栏，与底部虚拟操作按钮
    //   AutoOrientation.landscapeAutoMode();
    //   SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
    // }
    // if (Platform.isIOS) {
    //   // _channel.invokeMethod('change_screen_orientation', ['landscapeLeft', 'landscapeRight']);
    //   SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    // }
  }

//  获取厂商
  static Future<String> getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    print('设备制造厂商: ${androidInfo.manufacturer}');
    return androidInfo.manufacturer;
  }

  //获取手机型号
  static Future<String> getPhoneModel() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    IosDeviceInfo? iosInfo;
    AndroidDeviceInfo androidInfo;
    if (Platform.isIOS) {
      iosInfo = await deviceInfo.iosInfo;
      /// 将 iOS 的设备标识符转换为可读型号（如 "iPhone14,5" -> "iPhone 13"）
      const modelMap = {
        'iPhone1,1': 'iPhone',
        'iPhone1,2': 'iPhone 3G',
        'iPhone2,1': 'iPhone 3GS',
        'iPhone3,1': 'iPhone 4',
        'iPhone4,1': 'iPhone 4s',
        'iPhone5,1': 'iPhone 5',
        'iPhone5,2': 'iPhone 5',
        'iPhone5,3': 'iPhone 5c',
        'iPhone5,4': 'iPhone 5c',
        'iPhone6,1': 'iPhone 5s',
        'iPhone6,2': 'iPhone 5s',
        'iPhone7,1': 'iPhone 6 Plus',
        'iPhone7,2': 'iPhone 6',
        'iPhone8,1': 'iPhone 6s',
        'iPhone8,2': 'iPhone 6s Plus',
        'iPhone8,4': 'iPhone SE (1st)',
        'iPhone9,1': 'iPhone 7',
        'iPhone9,2': 'iPhone 7 Plus',
        'iPhone9,3': 'iPhone 7',
        'iPhone9,4': 'iPhone 7 Plus',
        'iPhone10,1': 'iPhone 8',
        'iPhone10,2': 'iPhone 8 Plus',
        'iPhone10,3': 'iPhone X',
        'iPhone10,4': 'iPhone 8',
        'iPhone10,5': 'iPhone 8 Plus',
        'iPhone10,6': 'iPhone X',
        'iPhone11,2': 'iPhone XS',
        'iPhone11,4': 'iPhone XS Max',
        'iPhone11,6': 'iPhone XS Max',
        'iPhone11,8': 'iPhone XR',
        'iPhone12,1': 'iPhone 11',
        'iPhone12,3': 'iPhone 11 Pro',
        'iPhone12,5': 'iPhone 11 Pro Max',
        'iPhone12,8': 'iPhone SE (2nd)',
        'iPhone13,1': 'iPhone 12 mini',
        'iPhone13,2': 'iPhone 12',
        'iPhone13,3': 'iPhone 12 Pro',
        'iPhone13,4': 'iPhone 12 Pro Max',
        'iPhone14,2': 'iPhone 13 Pro',
        'iPhone14,3': 'iPhone 13 Pro Max',
        'iPhone14,4': 'iPhone 13 mini',
        'iPhone14,5': 'iPhone 13',
        'iPhone14,6': 'iPhone SE (3rd)',
        'iPhone14,7': 'iPhone 14',
        'iPhone14,8': 'iPhone 14 Plus',
        'iPhone15,2': 'iPhone 14 Pro',
        'iPhone15,3': 'iPhone 14 Pro Max',
        'iPhone15,4': 'iPhone 15',
        'iPhone15,5': 'iPhone 15 Plus',
        'iPhone16,1': 'iPhone 15 Pro',
        'iPhone16,2': 'iPhone 15 Pro Max',
      };
      return modelMap[iosInfo.utsname.machine] ?? iosInfo.utsname.machine; // 如果未知型号，返回原始标识符
    } else {
      androidInfo = await deviceInfo.androidInfo;
      return "${androidInfo.brand} ${androidInfo.model}";
    }
  }

//获取版本号
  static Future<String> getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;
    print('应用版本号: $version');
    return version;
  }

  //判断时间是否过去24小时
  static bool isPast24Hours(int timestamp) {
    // 获取当前时间的时间戳（毫秒）
    final currentTime = DateTime.now().millisecondsSinceEpoch;

    // 计算时间差（毫秒）
    final timeDifference = currentTime - timestamp;

    // 24 小时的毫秒数（1000ms * 60s * 60min * 24h）
    const twentyFourHoursInMs = 24 * 60 * 60 * 1000;

    // 如果时间差 ≥ 24 小时，返回 true
    return timeDifference >= twentyFourHoursInMs;
  }

  //设置竖屏
  static setPortrait() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    // if (Platform.isAndroid) {
    //   //显示状态栏，与底部虚拟操作按钮
    //   AutoOrientation.portraitAutoMode();
    //   SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);
    // }
    // if (Platform.isIOS) {
    //   // _channel.invokeMethod('change_screen_orientation', ['portraitUp']);
    //   SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    // }
  }

  ///跳转
  void jump(Widget widget) {
    Navigator.of(Global.appContext()).push(MaterialPageRoute(builder: (_) {
      return widget;
    }));
  }

  ///跳转
  Future<T?> jumpThen<T extends Object?>(Widget widget) {
    return Navigator.of(Global.appContext())
        .push(MaterialPageRoute(builder: (_) {
      return widget;
    }));
  }
}

class Math {
  static String getDouble(var value, int fixed) {
    String data = value.toStringAsFixed(fixed);
    String decimal = data.substring(data.lastIndexOf(".") + 1);
    String intger = data.substring(0, data.lastIndexOf("."));
    if (Math.parseDouble('0.' + decimal) == 0) {
      return intger;
    } else if (decimal.endsWith('0')) {
      return intger + '.' + decimal.substring(0, decimal.length - 1);
    } else {
      return intger + '.' + decimal;
    }
  }

  static int parseInt(var value) {
    int number = 0;
    try {
      if (value is String) {
        number = int.parse(value);
      } else if (value is int) {
        number = value;
      } else {
        number = value.toInt();
      }
    } catch (e) {}
    return number;
  }

  static double parseDouble(var value) {
    double number = 0;
    try {
      if (value is String) {
        number = double.parse(value);
      } else if (value is double) {
        number = value;
      } else {
        number = value.toDouble();
      }
    } catch (e) {}
    return number;
  }

  static double divided(var value1, var value2) {
    if (value2 == 0) {
      return 0;
    }
    try {
      return value1 / value2;
    } catch (e) {}
    return 0;
  }

  static String digitsToChinese(String numberStr) {
    String result = '';
    for (int i = 0; i < numberStr.length; i++) {
      result += numberToChinese(int.parse(numberStr[i])) + ' ';
    }
    return result.trim();
  }

  static String numberToChinese(int number) {
    const List<String> simpleChineseNumbers = [
      '零',
      '一',
      '二',
      '三',
      '四',
      '五',
      '六',
      '七',
      '八',
      '九'
    ];
    return simpleChineseNumbers[number];
  }

  static String trim(String value) {
    if (value.isEmpty) {
      return value;
    }
    return value.replaceAll(new RegExp(r"\s+\b|\b\s"), "");
  }

  static String number(int num) {
    String result = '';
    try {
      String text = num.toString();
      int start = text.length % 3;
      int length = (text.length / 3).toInt();
      List<String> list = <String>[];
      if (start > 0) {
        list.add(text.substring(0, start));
      }
      for (int i = 0; i < length; i++) {
        list.add(text.substring(start + i * 3, start + (i + 1) * 3));
      }
      for (int i = 0; i < list.length; i++) {
        if (i == 0) {
          result += list[i];
        } else {
          result += ',' + list[i];
        }
      }
    } catch (e) {}
    return result;
  }

  static String sortStr(String value) {
    String result = '';
    try {
      List<String> list = <String>[];
      for (int i = 0; i < value.length; i++) {
        list.add(value.substring(i, i + 1));
      }
      list.sort((a, b) => a.compareTo(b));
      for (int i = 0; i < list.length; i++) {
        result += list[i];
      }
    } catch (e) {
      result = value;
    }
    return result;
  }

  static String today() {
    return DateUtil.formatDate(DateTime.now(), format: DateFormats.y_mo_d);
  }
}

class FileUtils {
  //构造方法
  FileUtils();

  // 单例方法
  static FileUtils? instance;

  static FileUtils get() {
    if (instance == null) {
      instance = FileUtils();
    }
    return instance!;
  }

  /**
   * 方法：获取网络图片
   */
  Future filePath(String fileName) async {
    final extDir = await getApplicationDocumentsDirectory();
    final testDir =
        await Directory('${extDir.path}/dxjd').create(recursive: true);
    return '${testDir.path}/${fileName}';
  }
}

class ThrottleUtil {
  //构造方法
  ThrottleUtil();

  // 单例方法
  static ThrottleUtil? instance;

  static ThrottleUtil get() {
    if (instance == null) {
      instance = ThrottleUtil();
    }
    return instance!;
  }

  static const Duration _KDelay = Duration(milliseconds: 500);
  var enable = true;

  ///防止重复点击
  ///func 要执行的方法
  Function throttle(
    Function func, {
    Duration delay = _KDelay,
  }) {
    return () {
      if (enable) {
        func();
        enable = false;
        Future.delayed(delay, () {
          enable = true;
        });
      }
    };
  }
}

class Logs {
  static void e(Object value) {
    if (kDebugMode) {
      print(value);
    }
  }
}

// 自定义dialog
import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:quiz/quiz.dart';
import 'package:tools/src/view/calendar/calendar_list.dart';
import 'package:tools/tools.dart';

import '../utils/permission.dart';
import 'node_slisder.dart';
import 'scroll.dart';
import 'unit.dart';

class DialogHelper {
  static DialogTips? tips;
  static DialogThreeTips? three;
  static DialogPermission? permission;
  static DialogSelect? select;

  static UpdateQuez? update;
  static DialogBigPicture? bigPicture;
  static DialogNumList? numList;
  static DialogSubmitPaper? submit;
  static DialogFontSize? fontSize;

  static DialogShowFace? face;
  static DialogQuesAnswer? answer;
  static DialogRealSubmit? realSubmit;
  static DialogRealResult? realResult;
  static PeriodTips? period;
  static TimingNotice? timingNotice;
  static AppointExplains? explains;
  static TimingError? timingError;
  static TimingReminder? timingReminder;
  static DialogDatePicker? picker;

  static void showDialogTips(
      BuildContext context,
      int? type,
      String? title,
      String? content,
      String? cancel,
      String? confirm,
      void Function(String? type) callback) async {
    Future.delayed(Duration.zero, () {
      tips = DialogTips(
          title: title,
          content: content,
          cancel: cancel,
          confirm: confirm,
          callback: callback);
      var result = showDialog(
          barrierDismissible: type == 1 ? false : true, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return PopScope(
              canPop: type == 1 ? false : true,
              child: tips!,
            );
          });
    });
  }

  static void showDialogThreeTips(
      BuildContext context,
      String? title,
      String? content,
      String? cancel,
      String? middle,
      String? confirm,
      List<int>? flex,
      void Function(String? type) callback) async {
    Future.delayed(Duration.zero, () {
      three = DialogThreeTips(
          title: title,
          content: content,
          cancel: cancel,
          middle: middle,
          confirm: confirm,
          flex: flex,
          callback: callback);
      var result = showDialog(
          barrierDismissible: true, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return PopScope(
              canPop: true,
              child: three!,
            );
          });
    });
  }

  static void showDialogDatePicker(BuildContext context, DateTime? date,
      void Function(DateTime? date) callback) async {
    Future.delayed(Duration.zero, () {
      picker = DialogDatePicker(date: date, callback: callback);
      var result = showModalBottomSheet<int>(
          backgroundColor: Colors.transparent,
          isScrollControlled: true,
          context: context,
          builder: (context) {
            return picker!;
          });
    });
  }

  static void showDialogStudyPlanGif(
      BuildContext context, void Function(String type) callback) async {
    Future.delayed(Duration.zero, () {
      var result = showDialog(
          barrierDismissible: false, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return WillPopScope(
              onWillPop: () {
                return Future.value(false);
              },
              child: Scaffold(
                appBar: AppBar(
                  backgroundColor: Colors.transparent,
                  leading: IconButton(
                      onPressed: (){
                        // Navigator.pop(context);
                      },
                      icon: Image.asset('assets/home_img/nav_icon_back.png',width: 24.0,height: 24.0,)
                  ),
                ),
                extendBodyBehindAppBar: true,
                body: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [Color(0xffD2E1FF), Color(0xffFEFEFF)],
                        begin: Alignment.topCenter,end: Alignment.bottomCenter)
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(height: kToolbarHeight+20),
                      Text('正在生成...'),
                      SizedBox(height: 10),
                      Text('你的专属备考计划',style: TextStyle(fontSize: 18,color: Colors.black,fontFamily: 'PingFangSC-Semibold'),),
                      SizedBox(height: 20),

                      Center(
                          child: Image.asset('assets/home_img/ic_plan_loading.gif',width: 200.0,height: 200.0)),
                      SizedBox(height: 20),
                      Text('练习情况评估中...',style: TextStyle(fontSize: 14,color: Color(0xff4D6BFE),fontFamily: 'PingFangSC-Medium'),),
                      SizedBox(height: 9),
                      Container(
                        height: 8,
                        width: 219,
                        child: LinearProgressLoader(),
                      ),
                      SizedBox(height: 60),
                      Row(
                        children: [
                          SizedBox(width: 60),
                          Text('大象驾到接入',style: TextStyle(fontSize: 18,color: Color(0xff3D3D3D),fontFamily: 'PingFangSC-Semibold'),),
                          SizedBox(width: 10),
                          Image.asset('assets/home_img/genwoxue_deepseek.png',width: 144.0,height: 30.0),
                        ]
                      ),
                      SizedBox(height: 50),
                      Row(
                          children: [
                            SizedBox(width: 86),
                            Image.asset('assets/home_img/genwoxue_gouxuan.png',width: 18.0,height: 18.0),
                            SizedBox(width: 5),
                            Text('AI深度分析做题数据',style: TextStyle(fontSize: 14,color: Color(0xff3D3D3D),fontFamily: 'PingFangSC-Medium'),),
                          ]
                      ),
                      SizedBox(height: 20),
                      Row(
                          children: [
                            SizedBox(width: 86),
                            Image.asset('assets/home_img/genwoxue_gouxuan.png',width: 18.0,height: 18.0),
                            SizedBox(width: 5),
                            Text('私人定制备考计划',style: TextStyle(fontSize: 14,color: Color(0xff3D3D3D),fontFamily: 'PingFangSC-Medium'),),
                          ]
                      ),
                      SizedBox(height: 20),
                      Row(
                          children: [
                            SizedBox(width: 86),
                            Image.asset('assets/home_img/genwoxue_gouxuan.png',width: 18.0,height: 18.0),
                            SizedBox(width: 5),
                            Text('精准答疑',style: TextStyle(fontSize: 14,color: Color(0xff3D3D3D),fontFamily: 'PingFangSC-Medium'),),
                          ]
                      ),
                    ],
                  ),
                ),
              ),
            );
          });
      Future.delayed(Duration(milliseconds: 1800), () {
        Navigator.pop(context);
        callback('dismiss');
      });
    });
  }

  static Future<void> showDialogPermission(
      BuildContext context,
      String? title,
      String? content,
      // List<Permission>? type,
      Permission? type,
      void Function(String type) callback) async {
    await Future.delayed(Duration.zero, () async {
      permission = DialogPermission(
          title: title, content: content, permission: type, callback: callback);
      var result = await showDialog(
          barrierDismissible: false, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return WillPopScope(
              onWillPop: () {
                return Future.value(false);
              },
              child: permission!,
            );
          });
    });
  }

  static void showDialogSelect(BuildContext context, List<String>? data,
      void Function(int? position) callback) async {
    Future.delayed(Duration.zero, () {
      select = DialogSelect(data: data, callback: callback);
      var result = showModalBottomSheet<int>(
          backgroundColor: Colors.transparent,
          isScrollControlled: true,
          context: context,
          builder: (context) {
            return select!;
          });
    });
  }

  static void showUpdateQuez(BuildContext context, int manUpdate,
      void Function(bool status) callback) async {
    Future.delayed(Duration.zero, () {
      update = UpdateQuez(callback: callback);

      var result = showDialog(
          barrierDismissible: manUpdate != 1, //表示点击灰色背景的时候是否消失弹出框
          barrierColor: Color(0x4D000000),
          context: context,
          builder: (context) {
            return PopScope(
              canPop: manUpdate != 1,
              onPopInvoked: (bool didPop) {
                //这里可以响应物理返回键
                callback(false);
              },
              child: update!,
            );
          });
    });
  }

  static void showBigPicture(BuildContext context, String? type,
      List<String>? photos, int? current, bool? landscape) async {
    Future.delayed(Duration.zero, () {
      bigPicture = DialogBigPicture(
          type: type, photos: photos, current: current, landscape: landscape);
      var result = showDialog(
          barrierDismissible: false, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return PopScope(
              canPop: true,
              child: bigPicture!,
            );
          });
    });
  }

  static void showDialogNumList(
      BuildContext context,
      List<QuizMo> quizList,
      int currentpage,
      int correct,
      int wrong,
      void Function(String type, int position) callback,
      {bool submit = false,
      bool tovip = false}) async {
    Future.delayed(Duration.zero, () {
      numList = DialogNumList(
          quizList: quizList,
          currentpage: currentpage,
          correct: correct,
          wrong: wrong,
          submit: submit,
          tovip: tovip,
          callback: callback);
      var result = showModalBottomSheet<int>(
          backgroundColor: Colors.transparent,
          isScrollControlled: true,
          context: context,
          builder: (context) {
            return numList!;
          });
    });
  }

  static void showDialogSubmitPaper(
      BuildContext context,
      bool ignore,
      int flag,
      bool isVip,
      String time,
      int nodone,
      int wrong,
      int score,
      void Function(String? type) callback) async {
    Future.delayed(Duration.zero, () {
      submit = DialogSubmitPaper(
          ignore: ignore,
          flag: flag,
          isVip: isVip,
          time: time,
          nodone: nodone,
          wrong: wrong,
          score: score,
          callback: callback);
      var result = showDialog(
          barrierDismissible: true, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return PopScope(
              canPop: true,
              child: submit!,
            );
          });
    });
  }

  static void showDialogFontSize(BuildContext context) async {
    Future.delayed(Duration.zero, () {
      fontSize = DialogFontSize();
      var result = showModalBottomSheet<int>(
          backgroundColor: Colors.transparent,
          isScrollControlled: true,
          context: context,
          builder: (context) {
            return PopScope(
              canPop: true,
              child: fontSize!,
            );
          });
    });
  }

  static void showDialogShowFace(BuildContext context) async {
    Future.delayed(Duration.zero, () {
      face = DialogShowFace();
      var result = showDialog(
          barrierDismissible: true, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return PopScope(
              canPop: false,
              child: face!,
            );
          });
    });
  }

  static void showDialogQuesAnswer(BuildContext context, int position,
      QuizMo mo, String selection, void Function(String type) callback) async {
    Future.delayed(Duration.zero, () {
      answer = DialogQuesAnswer(
          position: position, mo: mo, selection: selection, callback: callback);
      var result = showDialog(
          barrierDismissible: false, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return PopScope(
              canPop: false,
              child: answer!,
            );
          });
    });
  }

  static void showDialogRealSubmit(
      BuildContext context,
      String title,
      String message,
      String cancel,
      String confirm,
      void Function(String type) callback) async {
    Future.delayed(Duration.zero, () {
      realSubmit = DialogRealSubmit(
          title: title,
          message: message,
          cancel: cancel,
          confirm: confirm,
          callback: callback);
      var result = showDialog(
          barrierDismissible: false, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return PopScope(
              canPop: false,
              child: realSubmit!,
            );
          });
    });
  }

  static void showDialogRealResult(BuildContext context, String title,
      int score, String confirm, void Function(String type) callback) async {
    Future.delayed(Duration.zero, () {
      realResult = DialogRealResult(
          title: title, score: score, confirm: confirm, callback: callback);
      var result = showDialog(
          barrierDismissible: false, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return PopScope(
              canPop: false,
              child: realResult!,
            );
          });
    });
  }

  static void showDialogPeriodTips(
      BuildContext context, void Function(String? type) callback) async {
    Future.delayed(Duration.zero, () {
      period = PeriodTips(callback: callback);
      var result = showDialog(
          barrierDismissible: true, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return period!;
          });
    });
  }

  static void showDialogTimingNotice(
      BuildContext context, void Function(String? type) callback) async {
    Future.delayed(Duration.zero, () {
      timingNotice = TimingNotice(callback: callback);
      var result = showDialog(
          barrierDismissible: true, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return timingNotice!;
          });
    });
  }

  static void showDialogExplains(BuildContext context, String msg,
      void Function(String? type) callback) async {
    Future.delayed(Duration.zero, () {
      explains = AppointExplains(msg: msg, callback: callback);
      var result = showDialog(
          barrierDismissible: true, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return explains!;
          });
    });
  }

  static void showDialogTimingReminder(BuildContext context, String msg,
      void Function(String? type) callback) async {
    Future.delayed(Duration.zero, () {
      timingReminder = TimingReminder(msg: msg, callback: callback);
      var result = showDialog(
          barrierDismissible: true, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return timingReminder!;
          });
    });
  }

  static void showDialogTimingError(
      BuildContext context, void Function() callback) async {
    Future.delayed(Duration.zero, () {
      timingError = TimingError(callback: callback);
      var result = showDialog(
          barrierDismissible: true, //表示点击灰色背景的时候是否消失弹出框
          context: context,
          builder: (context) {
            return timingError!;
          });
    });
  }
}

class DialogTips extends Dialog {
  String? title;
  String? content;
  String? cancel;
  String? confirm;
  Function(String? type)? callback;

  DialogTips(
      {Key? key,
      this.title = '',
      this.content = '',
      this.cancel = '',
      this.confirm = '',
      required this.callback})
      : super(key: key);

  static Future<void> dismiss() async {
    try {
      TipsWidget?.dismiss();
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return TipsWidget(
        title: title,
        content: content,
        cancel: cancel,
        confirm: confirm,
        callback: callback);
  }
}

class TipsWidget extends StatefulWidget {
  const TipsWidget(
      {Key? key,
      this.title,
      this.content,
      this.cancel,
      this.confirm,
      this.callback})
      : super(key: key);

  final String? title; //
  final String? content; //
  final String? cancel; //
  final String? confirm; //
  final Function(String? type)? callback;

  @override
  _TipsWidgetState createState() => _TipsWidgetState();

  static Future<void> dismiss() async {
    try {
      _TipsWidgetState.state?.dismiss();
    } catch (e) {}
  }
}

class _TipsWidgetState extends State<TipsWidget> {
  static _TipsWidgetState? state;

  _TipsWidgetState() {
    state = this;
  }

  @override
  void initState() {
    super.initState();
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
      state = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: UIUtils.width - UIUtils.dp(56),
          // height: 120,
          decoration: Style.style_box_white_r12,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              if (widget.title!.isNotEmpty)
                Container(
                  alignment: AlignmentDirectional.center,
                  margin: EdgeInsets.only(
                      top: UIUtils.dp(34),
                      left: UIUtils.dp(36),
                      right: UIUtils.dp(36)),
                  child: Text(
                    widget.title!,
                    style: Style.style_text1_18_w500,
                  ),
                ),
              Container(
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(32),
                    left: UIUtils.dp(36),
                    right: UIUtils.dp(36)),
                child: Text(
                  widget.content!,
                  style: Style.style_text1_h14_w500,
                  textAlign: TextAlign.left,
                ),
              ),
              Container(
                alignment: AlignmentDirectional.centerEnd,
                margin: EdgeInsets.only(
                    top: widget.title!.isNotEmpty ? 0 : UIUtils.dp(16),
                    left: UIUtils.dp(36),
                    right: UIUtils.dp(36)),
                child: Image.asset(
                  'assets/train/ic_tips_logo.png',
                  width: UIUtils.dp(60),
                  fit: BoxFit.fitWidth,
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                    bottom: UIUtils.dp(24),
                    left: UIUtils.dp(24),
                    right: UIUtils.dp(24)),
                child: widget.cancel!.isNotEmpty
                    ? Row(
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              margin: EdgeInsets.only(
                                  left: UIUtils.dp(12), right: UIUtils.dp(12)),
                              child: JkInkWell(
                                child: Container(
                                  height: UIUtils.dp(40),
                                  alignment: AlignmentDirectional.center,
                                  decoration: Style.style_border_text3_r50,
                                  child: Text(
                                    widget.cancel!,
                                    style: Style.style_text1_16_w500,
                                  ),
                                ),
                                onTap: () {
                                  dismiss();
                                  if (widget.callback != null) {
                                    widget.callback!('cancel');
                                  }
                                },
                              ),
                            ),
                          ),
                          Expanded(
                            child: Container(
                              margin: EdgeInsets.only(
                                  left: UIUtils.dp(12), right: UIUtils.dp(12)),
                              child: InkWellShadow(
                                child: Container(
                                  height: UIUtils.dp(40),
                                  alignment: AlignmentDirectional.center,
                                  decoration: Style.style_btn_blue_small,
                                  child: Text(
                                    widget.confirm!,
                                    style: Style.style_white_16_w500,
                                  ),
                                ),
                                onTap: () {
                                  dismiss();
                                  if (widget.callback != null) {
                                    widget.callback!('confirm');
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      )
                    : Container(
                        margin: EdgeInsets.only(
                            left: UIUtils.dp(12), right: UIUtils.dp(12)),
                        child: InkWellShadow(
                          child: Container(
                            width: UIUtils.width - UIUtils.dp(144),
                            height: UIUtils.dp(40),
                            alignment: AlignmentDirectional.center,
                            decoration: Style.style_btn_blue_middle,
                            child: Text(
                              widget.confirm!,
                              style: Style.style_white_16_w500,
                            ),
                          ),
                          onTap: () {
                            dismiss();
                            if (widget.callback != null) {
                              widget.callback!('confirm');
                            }
                          },
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DialogThreeTips extends Dialog {
  String? title;
  String? content;
  String? cancel;
  String? middle;
  String? confirm;
  List<int>? flex;
  Function(String? type)? callback;

  DialogThreeTips(
      {Key? key,
      this.title = '',
      this.content = '',
      this.cancel = '',
      this.middle = '',
      this.confirm = '',
      this.flex,
      required this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ThreeTipsWidget(
        title: title,
        content: content,
        cancel: cancel,
        middle: middle,
        confirm: confirm,
        flex: flex,
        callback: callback);
  }
}

class ThreeTipsWidget extends StatefulWidget {
  const ThreeTipsWidget(
      {Key? key,
      this.title,
      this.content,
      this.cancel,
      this.middle,
      this.confirm,
      this.flex,
      this.callback})
      : super(key: key);

  final String? title; //
  final String? content; //
  final String? cancel; //
  final String? middle; //
  final String? confirm; //
  final List<int>? flex; //
  final Function(String? type)? callback;

  @override
  _ThreeTipsWidgetState createState() => _ThreeTipsWidgetState();
}

class _ThreeTipsWidgetState extends State<ThreeTipsWidget> {
  @override
  void initState() {
    super.initState();
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: UIUtils.width - UIUtils.dp(56),
          // height: 120,
          decoration: Style.style_box_white_r12,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              if (widget.title!.isNotEmpty)
                Container(
                  alignment: AlignmentDirectional.center,
                  margin: EdgeInsets.only(
                      top: UIUtils.dp(34),
                      left: UIUtils.dp(36),
                      right: UIUtils.dp(36)),
                  child: Text(
                    widget.title!,
                    style: Style.style_text1_18_w500,
                  ),
                ),
              Container(
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(32),
                    left: UIUtils.dp(36),
                    right: UIUtils.dp(36)),
                child: Text(
                  widget.content!,
                  style: Style.style_text1_h14_w500,
                  textAlign: TextAlign.center,
                ),
              ),
              Container(
                alignment: AlignmentDirectional.centerEnd,
                margin: EdgeInsets.only(
                    top: widget.title!.isNotEmpty ? 0 : UIUtils.dp(16),
                    left: UIUtils.dp(36),
                    right: UIUtils.dp(36)),
                child: Image.asset(
                  'assets/train/ic_tips_logo.png',
                  width: UIUtils.dp(60),
                  fit: BoxFit.fitWidth,
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                    bottom: UIUtils.dp(24),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: Row(
                  children: <Widget>[
                    Expanded(
                      flex: widget.flex == null || widget.flex!.length < 1
                          ? 1
                          : widget.flex![0],
                      child: Container(
                        margin: EdgeInsets.only(
                            left: UIUtils.dp(6), right: UIUtils.dp(6)),
                        child: JkInkWell(
                          child: Container(
                            height: UIUtils.dp(36),
                            alignment: AlignmentDirectional.center,
                            decoration: Style.style_border_text3_r50,
                            child: Text(
                              widget.cancel!,
                              style: Style.style_text1_14_w500,
                            ),
                          ),
                          onTap: () {
                            dismiss();
                            if (widget.callback != null) {
                              widget.callback!('cancel');
                            }
                          },
                        ),
                      ),
                    ),
                    Expanded(
                      flex: widget.flex == null || widget.flex!.length < 2
                          ? 2
                          : widget.flex![1],
                      child: Container(
                        margin: EdgeInsets.only(
                            left: UIUtils.dp(6), right: UIUtils.dp(6)),
                        child: JkInkWell(
                          child: Container(
                            height: UIUtils.dp(36),
                            alignment: AlignmentDirectional.center,
                            decoration: Style.style_border_text3_r50,
                            child: Text(
                              widget.middle!,
                              style: Style.style_text1_14_w500,
                            ),
                          ),
                          onTap: () {
                            dismiss();
                            if (widget.callback != null) {
                              widget.callback!('middle');
                            }
                          },
                        ),
                      ),
                    ),
                    Expanded(
                      flex: widget.flex == null || widget.flex!.length < 3
                          ? 1
                          : widget.flex![2],
                      child: Container(
                        margin: EdgeInsets.only(
                            left: UIUtils.dp(6), right: UIUtils.dp(6)),
                        child: InkWellShadow(
                          child: Container(
                            height: UIUtils.dp(36),
                            alignment: AlignmentDirectional.center,
                            decoration: Style.style_btn_blue_min,
                            child: Text(
                              widget.confirm!,
                              style: Style.style_white_14_w500,
                            ),
                          ),
                          onTap: () {
                            dismiss();
                            if (widget.callback != null) {
                              widget.callback!('confirm');
                            }
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DialogPermission extends Dialog {
  String? title;
  String? content;
  // List<Permission>? permission;
  Permission? permission;
  Function(String type)? callback;

  DialogPermission(
      {Key? key,
      this.title = '',
      this.content = '',
      this.permission,
      required this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PermissionWidget(
        title: title,
        content: content,
        permission: permission,
        callback: callback);
  }
}

class PermissionWidget extends StatefulWidget {
  const PermissionWidget(
      {Key? key, this.title, this.content, this.permission, this.callback})
      : super(key: key);

  final String? title;
  final String? content;
  // final List<Permission>? permission;
  final Permission? permission;
  final Function(String type)? callback;

  @override
  _PermissionWidgetState createState() => _PermissionWidgetState();
}

class _PermissionWidgetState extends State<PermissionWidget> {
  @override
  void initState() {
    super.initState();
    if (widget.permission != null) {
      Future.delayed(Duration.zero, () {
        PermissionUtils.get().checkPermission(widget.permission!,
            (String type) {
          dismiss();
          if (widget.callback != null) {
            widget.callback!(type);
          }
        });
      });
    }
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Container(
        width: UIUtils.width,
        height: UIUtils.height,
        decoration: Style.style_box_bg_ab_r8,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Container(
              alignment: AlignmentDirectional.topStart,
              margin: EdgeInsets.only(
                  top: UIUtils.dp(20),
                  left: UIUtils.dp(30),
                  right: UIUtils.dp(30)),
              child: Text(
                widget.title!,
                style: Style.style_text1_16_bold,
              ),
            ),
            Container(
              alignment: AlignmentDirectional.topStart,
              margin: EdgeInsets.only(
                  top: UIUtils.dp(15),
                  left: UIUtils.dp(30),
                  right: UIUtils.dp(30)),
              child: Text(
                widget.content!,
                style: Style.style_text1_h14_w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DialogSelect extends Dialog {
  String? title;
  List<String>? data = <String>[];
  Function(int? position)? callback;

  DialogSelect({Key? key, this.title = '', this.data, required this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SelectWidget(title: title, data: data, callback: callback);
  }
}

class SelectWidget extends StatefulWidget {
  const SelectWidget({Key? key, this.title, this.data, this.callback})
      : super(key: key);

  final String? title;
  final List<String>? data;

  final Function(int? position)? callback;

  @override
  _SelectWidgetState createState() => _SelectWidgetState();
}

class _SelectWidgetState extends State<SelectWidget> {
  @override
  void initState() {
    super.initState();
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: (widget.title!.isNotEmpty ? UIUtils.dp(50) : 0) +
          widget.data!.length * UIUtils.dp(60) +
          UIUtils.dp(16) +
          UIUtils.dp(76),
      padding: EdgeInsets.only(top: UIUtils.dp(16)),
      decoration: Style.style_box_white_ab_r12,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.title!.isNotEmpty)
            SizedBox(
              height: UIUtils.dp(50),
              child: Stack(
                textDirection: TextDirection.rtl,
                children: [
                  Center(
                    child: Text(
                      widget.title!,
                      style: Style.style_text1_16_w500,
                    ),
                  ),
                  IconButton(
                      icon: Icon(Icons.close),
                      onPressed: () {
                        dismiss();
                      }),
                ],
              ),
            ),
          Container(
            height: widget.data!.length * UIUtils.dp(60),
            child: XListView(
              itemCount: widget.data!.length,
              itemBuilder: (context, index) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    JkInkWell(
                      child: Container(
                        alignment: AlignmentDirectional.center,
                        height: UIUtils.dp(60),
                        child: Text(
                          widget.data![index],
                          style: Style.style_text1_16_w500,
                        ),
                      ),
                      onTap: () {
                        dismiss();
                        if (widget.callback != null) {
                          widget.callback!(index);
                        }
                      },
                    ),
                  ],
                );
              },
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                left: UIUtils.dp(16),
                right: UIUtils.dp(16),
                top: UIUtils.dp(8),
                bottom: UIUtils.dp(22)),
            child: InkWellShadow(
              child: Container(
                alignment: AlignmentDirectional.center,
                height: UIUtils.dp(46),
                decoration: Style.style_box_bg_r50,
                child: Text(
                  '取消',
                  style: Style.style_text1_16_w500,
                ),
              ),
              onTap: () {
                dismiss();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class UpdateQuez extends Dialog {
  Function(bool status)? callback;

  UpdateQuez({Key? key, required this.callback}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UpdateQuezWidget(callback: callback);
  }
}

class UpdateQuezWidget extends StatefulWidget {
  const UpdateQuezWidget({Key? key, this.callback}) : super(key: key);

  final Function(bool status)? callback;

  @override
  _UpdateQuezWidgetState createState() => _UpdateQuezWidgetState();
}

class _UpdateQuezWidgetState extends State<UpdateQuezWidget> {
  @override
  void initState() {
    super.initState();
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: UIUtils.width - UIUtils.dp(56),
          child: Stack(
            children: <Widget>[
              Image.asset(
                'assets/train/ic_update_quez.png',
                width: UIUtils.width - UIUtils.dp(56),
                fit: BoxFit.fitWidth,
              ),
              Positioned(
                bottom: UIUtils.dp(20),
                left: UIUtils.dp(32),
                right: UIUtils.dp(32),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Container(
                      margin: EdgeInsets.only(top: UIUtils.dp(22)),
                      child: InkWellShadow(
                        child: Container(
                          height: UIUtils.dp(42),
                          alignment: AlignmentDirectional.center,
                          decoration: Style.style_box_blue_r50,
                          child: Text(
                            '立即更新',
                            style: Style.style_white_16_w500,
                          ),
                        ),
                        onTap: () {
                          dismiss();
                          if (widget.callback != null) {
                            widget.callback!(true);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DialogBigPicture extends Dialog {
  String? type;
  List<String>? photos;
  int? current = 0;
  bool? landscape;

  DialogBigPicture(
      {Key? key,
      this.type = '',
      this.photos,
      this.current = 0,
      this.landscape = false})
      : super(key: key);

  static Future<void> dismiss() async {
    try {
      BigPictureWidget?.dismiss();
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return BigPictureWidget(
        type: type, photos: photos, current: current, landscape: landscape);
  }
}

class BigPictureWidget extends StatefulWidget {
  const BigPictureWidget(
      {Key? key, this.type, this.photos, this.current, this.landscape = false})
      : super(key: key);

  final String? type;
  final List<String>? photos;
  final int? current;
  final bool? landscape;

  @override
  _BigPictureWidgetState createState() => _BigPictureWidgetState();

  static Future<void> dismiss() async {
    try {
      _BigPictureWidgetState.state?.dismiss();
    } catch (e) {}
  }
}

class _BigPictureWidgetState extends State<BigPictureWidget> {
  static _BigPictureWidgetState? state;

  _BigPictureWidgetState() {
    state = this;
  }

  int current = 0;

  @override
  void initState() {
    super.initState();
    current = widget.current!;
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
      state = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, setState) {
        return Dialog(
          insetPadding: const EdgeInsets.all(0),
          backgroundColor: Colors.black54,
          child: Container(
            decoration: const BoxDecoration(color: Colors.transparent),
            child: Stack(alignment: Alignment.topCenter, children: [
              Container(
                padding: const EdgeInsets.all(20.0),
                child: Text(
                  '${current}/${widget.photos!.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 17.0,
                    decoration: null,
                  ),
                ),
              ),
              PageView.builder(
                  controller: PageController(
                    initialPage: current,
                    viewportFraction: 1,
                  ),
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: widget.photos!.length,
                  onPageChanged: (index) {
                    print('当前实时照片的index 是$index');
                    current = index + 1;
                    setState(() {});
                  },
                  itemBuilder: (BuildContext context, int index) {
                    return Container(
                      constraints: BoxConstraints(
                        maxWidth:
                            widget.landscape! ? UIUtils.height : UIUtils.width,
                      ),
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: getSub_pic(widget.type!, widget.photos![index]),
                      ),
                    );
                  }),
            ]),
          ),
        );
      },
    );
  }

  Widget getSub_pic(String type, String path) {
    if (type == 'assets') {
      return Image.asset(
        path,
        width: widget.landscape! ? UIUtils.height : UIUtils.width,
        height: widget.landscape! ? UIUtils.width : UIUtils.height,
        fit: BoxFit.contain,
      );
    } else if (type == 'network') {
      return CachedNetworkImage(
        width: widget.landscape! ? UIUtils.height : UIUtils.width,
        height: widget.landscape! ? UIUtils.width : UIUtils.height,
        fit: BoxFit.contain,
        imageUrl: path,
        errorWidget: (context, url, error) => Container(),
      );
    }
    return Container();
  }
}

class DialogNumList extends Dialog {
  List<QuizMo> quizList = <QuizMo>[];
  int currentpage = 0;
  int correct = 0;
  int wrong = 0;
  bool submit = false;
  bool tovip = false;
  Function(String type, int position) callback;

  DialogNumList(
      {Key? key,
      required this.quizList,
      this.currentpage = 0,
      this.correct = 0,
      this.wrong = 0,
      this.submit = false,
      this.tovip = false,
      required this.callback})
      : super(key: key);

  static Future<void> dismiss() async {
    try {
      NumListWidget?.dismiss();
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return NumListWidget(
        quizList: quizList,
        currentpage: currentpage,
        correct: correct,
        wrong: wrong,
        submit: submit,
        tovip: tovip,
        callback: callback);
  }
}

class NumListWidget extends StatefulWidget {
  const NumListWidget(
      {Key? key,
      this.quizList,
      this.currentpage,
      this.correct,
      this.wrong,
      this.submit,
      this.tovip,
      this.callback})
      : super(key: key);

  final List<QuizMo>? quizList;
  final int? currentpage;
  final int? correct;
  final int? wrong;
  final bool? submit;
  final bool? tovip;
  final Function(String type, int position)? callback;

  @override
  _NumListWidgetState createState() => _NumListWidgetState();

  static Future<void> dismiss() async {
    try {
      _NumListWidgetState.state?.dismiss();
    } catch (e) {}
  }
}

class _NumListWidgetState extends State<NumListWidget> {
  static _NumListWidgetState? state;

  _NumListWidgetState() {
    state = this;
  }

  @override
  void initState() {
    super.initState();
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
      state = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: Style.style_box_white_ab_r12,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildTebWidget(),
          buildListView(),
        ],
      ),
    );
  }

  //构建底部对齐的抽屉效果视图
  Widget buildTebWidget() {
    return Container(
      height: UIUtils.dp(44),
      child: Row(
        children: <Widget>[
          Container(
            margin: EdgeInsets.only(left: UIUtils.dp(16)),
            alignment: AlignmentDirectional.center,
            child: Image.asset(
              'assets/exercise/ic_quiz_progress.png',
              width: UIUtils.dp(20),
              height: UIUtils.dp(20),
              fit: BoxFit.contain,
            ),
          ),
          Container(
            margin: EdgeInsets.only(left: UIUtils.dp(8)),
            constraints: BoxConstraints(
              minWidth: UIUtils.dp(56),
            ),
            alignment: Alignment.centerLeft,
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: '${widget.currentpage! + 1}',
                    style: Style.style_text1_12_w500,
                  ),
                  TextSpan(
                    text: '/${widget.quizList!.length}',
                    style: Style.style_text3_12_w500,
                  ),
                ],
              ),
            ),
          ),
          Image.asset(
            'assets/exercise/ic_quiz_correct.png',
            width: UIUtils.dp(20),
            height: UIUtils.dp(20),
            fit: BoxFit.contain,
          ),
          Container(
            margin: EdgeInsets.only(left: UIUtils.dp(4)),
            constraints: BoxConstraints(
              minWidth: UIUtils.dp(24),
            ),
            alignment: Alignment.centerLeft,
            child: Text(
              '${widget.correct!}',
              style: Style.style_text1_12_w500,
            ),
          ),
          Image.asset(
            'assets/exercise/ic_quiz_wrong.png',
            width: UIUtils.dp(20),
            height: UIUtils.dp(20),
            fit: BoxFit.contain,
          ),
          Container(
            margin: EdgeInsets.only(left: UIUtils.dp(4)),
            constraints: BoxConstraints(
              minWidth: UIUtils.dp(24),
            ),
            alignment: Alignment.centerLeft,
            child: Text(
              '${widget.wrong!}',
              style: Style.style_text1_12_w500,
            ),
          ),
          const Expanded(
            child: SizedBox(),
          ),
          if (widget.submit!)
            Container(
              margin:
                  EdgeInsets.only(left: UIUtils.dp(8), right: UIUtils.dp(8)),
              height: UIUtils.dp(44),
              alignment: AlignmentDirectional.center,
              child: InkWellShadow(
                child: Container(
                  padding: EdgeInsets.only(
                      left: UIUtils.dp(16), right: UIUtils.dp(16)),
                  height: UIUtils.dp(28),
                  alignment: AlignmentDirectional.center,
                  decoration: Style.style_btn_blue_min,
                  child: Text(
                    '交卷',
                    style: Style.style_white_14_w500,
                  ),
                ),
                onTap: () {
                  dismiss();
                  if (widget.callback != null) {
                    widget.callback!('submit', 0);
                  }
                },
              ),
            ),
          if (widget.tovip!)
            Container(
              margin:
                  EdgeInsets.only(left: UIUtils.dp(8), right: UIUtils.dp(8)),
              height: UIUtils.dp(44),
              alignment: AlignmentDirectional.center,
              child: InkWellShadow(
                child: Container(
                  padding: EdgeInsets.only(
                      left: UIUtils.dp(12), right: UIUtils.dp(12)),
                  height: UIUtils.dp(28),
                  alignment: AlignmentDirectional.center,
                  decoration: Style.style_btn_blue_small,
                  child: Text(
                    IQuiz.get().subject == JkKey.SUBONE ? '3步过科一' : '3步过科四',
                    style: Style.style_white_12_w500,
                  ),
                ),
                onTap: () {
                  if (widget.callback != null) {
                    widget.callback!('tovip', 0);
                  }
                },
              ),
            ),
          SizedBox(
            width: UIUtils.dp(8),
          ),
        ],
      ),
    );
  }

  ///可滑动布局构建 这里是一个列表ListView
  Widget buildListView() {
    return Container(
      constraints: BoxConstraints(
        maxHeight: UIUtils.height * 0.6,
      ),
      margin: EdgeInsets.only(
          top: UIUtils.dp(8),
          bottom: UIUtils.dp(8),
          left: UIUtils.dp(16),
          right: UIUtils.dp(16)),
      child: XGridView(
          controller: ScrollController(),
          shrinkWrap: true,
          physics: true,
          itemCount: widget.quizList!.length,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 6,
            childAspectRatio: 1,
          ),
          itemBuilder: (context, index) {
            return _buildItem(index, widget.quizList![index]);
          }),
    );
  }

  Widget _buildItem(int index, QuizMo mo) => JkInkWell(
        child: Stack(
          children: <Widget>[
            Container(
              width: (UIUtils.width - UIUtils.dp(32)) / 6,
              height: (UIUtils.width - UIUtils.dp(32)) / 6,
              alignment: Alignment.center,
              child: Container(
                width: (UIUtils.width - UIUtils.dp(32)) / 6 - UIUtils.dp(16),
                height: (UIUtils.width - UIUtils.dp(32)) / 6 - UIUtils.dp(16),
                alignment: Alignment.center,
                decoration: Option.checkNumBox(mo, index, widget.currentpage!),
                child: Text(
                  '${index + 1}',
                  style: Style.style_text1_14_w500,
                ),
              ),
            ),
            if (mo.examstate != 0 && errorcount(index) > 0)
              Positioned(
                right: UIUtils.dp(5),
                top: UIUtils.dp(5),
                child: Container(
                  width: UIUtils.dp(16),
                  height: UIUtils.dp(16),
                  alignment: Alignment.center,
                  decoration: Style.style_box_red_r50,
                  child: Text(
                    '${errorcount(index)}',
                    style: Style.style_white_9_w500,
                  ),
                ),
              ),
          ],
        ),
        onTap: () {
          //跳转做题索引
          dismiss();
          if (widget.callback != null) {
            widget.callback!('', index);
          }
        },
      );

  int errorcount(int index) {
    QuizMo? mo = widget.quizList![index];
    if (mo != null) {
      return mo.errorcount;
    }
    return 0;
  }
}

class DialogSubmitPaper extends Dialog {
  bool ignore;
  int flag;
  bool isVip;
  String time;
  int nodone;
  int wrong;
  int score;
  Function(String type)? callback;

  DialogSubmitPaper(
      {Key? key,
      this.ignore = false,
      this.flag = 0,
      this.isVip = false,
      this.time = '',
      this.nodone = 0,
      this.wrong = 0,
      this.score = 0,
      required this.callback})
      : super(key: key);

  static Future<void> dismiss() async {
    try {
      SubmitPaperWidget?.dismiss();
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return SubmitPaperWidget(
        ignore: ignore,
        flag: flag,
        isVip: isVip,
        time: time,
        nodone: nodone,
        wrong: wrong,
        score: score,
        callback: callback);
  }
}

class SubmitPaperWidget extends StatefulWidget {
  const SubmitPaperWidget(
      {Key? key,
      this.ignore,
      this.flag,
      this.isVip,
      this.time,
      this.nodone,
      this.wrong,
      this.score,
      this.callback})
      : super(key: key);

  final bool? ignore;
  final int? flag;
  final bool? isVip;
  final String? time;
  final int? nodone;
  final int? wrong;
  final int? score;
  final Function(String type)? callback;

  @override
  _SubmitPaperWidgetState createState() => _SubmitPaperWidgetState();

  static Future<void> dismiss() async {
    try {
      _SubmitPaperWidgetState.state?.dismiss();
    } catch (e) {}
  }
}

class _SubmitPaperWidgetState extends State<SubmitPaperWidget> {
  static _SubmitPaperWidgetState? state;

  _SubmitPaperWidgetState() {
    state = this;
  }

  @override
  void initState() {
    super.initState();
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
      state = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: UIUtils.width - UIUtils.dp(56),
          // height: 120,
          decoration: Style.style_box_white_r12,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(32),
                    left: UIUtils.dp(32),
                    right: UIUtils.dp(32)),
                child: Text(
                  widget.score! < 90 ? '考试不合格' : '考试合格',
                  style: widget.score! < 90
                      ? Style.style_red_18_w500
                      : Style.style_text1_18_w500,
                ),
              ),
              Container(
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(16),
                    left: UIUtils.dp(32),
                    right: UIUtils.dp(32)),
                child: Text(
                  widget.flag! == JkKey.OVERSCORE
                      ? '您已回答错了${widget.wrong!}题，考试得分${widget.score!}，成绩不合格，是否继续答题?'
                      : '剩余时间：${widget.time!}',
                  style: Style.style_blue1_12_w500,
                  textAlign: TextAlign.center,
                ),
              ),
              if (widget.score! < 90 && !widget.isVip!)
                Container(
                  alignment: AlignmentDirectional.center,
                  margin: EdgeInsets.only(
                      top: UIUtils.dp(20),
                      left: UIUtils.dp(32),
                      right: UIUtils.dp(30)),
                  child: JkInkWell(
                    child: Image.asset(
                      'assets/exercise/ic_submit_paper_vip.png',
                      width: double.infinity,
                      fit: BoxFit.fitWidth,
                    ),
                    onTap: () {
                      dismiss();
                      if (widget.callback != null) {
                        widget.callback!('tovip');
                      }
                    },
                  ),
                ),
              Container(
                margin: EdgeInsets.only(top: UIUtils.dp(20)),
                padding: EdgeInsets.only(
                    left: UIUtils.dp(32), right: UIUtils.dp(36)),
                decoration: Style.style_submit_paper_box,
                child: Row(
                  children: <Widget>[
                    buildQuesView(widget.nodone!, '未答题数', false),
                    buildQuesView(widget.wrong!, '错题数', false),
                    buildQuesView(widget.score!, '考试得分', true),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                    top: UIUtils.dp(36),
                    bottom: UIUtils.dp(24),
                    left: UIUtils.dp(24),
                    right: UIUtils.dp(24)),
                child: cancel().isNotEmpty
                    ? Row(
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              margin: EdgeInsets.only(
                                  left: UIUtils.dp(12), right: UIUtils.dp(12)),
                              child: JkInkWell(
                                child: Container(
                                  height: UIUtils.dp(40),
                                  alignment: AlignmentDirectional.center,
                                  decoration: Style.style_border_text3_r50,
                                  child: Text(
                                    cancel(),
                                    style: Style.style_text1_16_w500,
                                  ),
                                ),
                                onTap: () {
                                  dismiss();
                                  if (widget.callback != null) {
                                    widget.callback!('cancel');
                                  }
                                },
                              ),
                            ),
                          ),
                          Expanded(
                            child: Container(
                              margin: EdgeInsets.only(
                                  left: UIUtils.dp(12), right: UIUtils.dp(12)),
                              child: InkWellShadow(
                                child: Container(
                                  height: UIUtils.dp(40),
                                  alignment: AlignmentDirectional.center,
                                  decoration: Style.style_btn_blue_small,
                                  child: Text(
                                    confirm(),
                                    style: Style.style_white_16_w500,
                                  ),
                                ),
                                onTap: () {
                                  dismiss();
                                  if (widget.callback != null) {
                                    widget.callback!('confirm');
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      )
                    : Container(
                        margin: EdgeInsets.only(
                            left: UIUtils.dp(12), right: UIUtils.dp(12)),
                        child: InkWellShadow(
                          child: Container(
                            width: UIUtils.width - UIUtils.dp(144),
                            height: UIUtils.dp(40),
                            alignment: AlignmentDirectional.center,
                            decoration: Style.style_btn_blue_middle,
                            child: Text(
                              confirm(),
                              style: Style.style_white_16_w500,
                            ),
                          ),
                          onTap: () {
                            dismiss();
                            if (widget.callback != null) {
                              widget.callback!('confirm');
                            }
                          },
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildQuesView(int num, String text, bool red) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Container(
            margin: EdgeInsets.only(top: UIUtils.dp(12)),
            child: Text(
              num.toString(),
              style: red ? Style.style_red_30_bold : Style.style_text1_30_bold,
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: UIUtils.dp(8), bottom: UIUtils.dp(14)),
            child: Text(
              text,
              style: Style.style_text1_14_w500,
            ),
          ),
        ],
      ),
    );
  }

  String cancel() {
    if (widget.flag! == JkKey.CHECK) {
      return '';
    } else if (widget.flag! == JkKey.SUBMIT && widget.nodone! == 0) {
      return '';
    }
    return '继续答题';
  }

  String confirm() {
    if (widget.flag! == JkKey.BACKCLICK) {
      return '退出答题';
    } else if (widget.flag! == JkKey.CHECK) {
      return '继续答题';
    } else if (widget.flag! == JkKey.SUBMIT && widget.nodone! == 0) {
      return widget.ignore! ? '退出答题' : '现在交卷';
    }
    return widget.ignore! ? '退出答题' : '现在交卷';
  }
}

class DialogFontSize extends Dialog {
  DialogFontSize({Key? key}) : super(key: key);

  static Future<void> dismiss() async {
    try {
      FontSizeWidget?.dismiss();
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return FontSizeWidget();
  }
}

class FontSizeWidget extends StatefulWidget {
  const FontSizeWidget({Key? key}) : super(key: key);

  @override
  _FontSizeWidgetState createState() => _FontSizeWidgetState();

  static Future<void> dismiss() async {
    try {
      _FontSizeWidgetState.state?.dismiss();
    } catch (e) {}
  }
}

class _FontSizeWidgetState extends State<FontSizeWidget> {
  static _FontSizeWidgetState? state;

  _FontSizeWidgetState() {
    state = this;
  }

  int index = 1;

  @override
  void initState() {
    super.initState();
    if (ITrain.get().fontSize == 'min') {
      index = 0;
    } else if (ITrain.get().fontSize == 'small') {
      index = 1;
    } else if (ITrain.get().fontSize == 'middle') {
      index = 2;
    } else if (ITrain.get().fontSize == 'big') {
      index = 3;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  void dismiss() {
    Navigator.pop(context);
    state = null;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: Style.style_box_white_ab_r12,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Container(
                margin: EdgeInsets.only(
                    top: UIUtils.dp(24), bottom: UIUtils.dp(20)),
                alignment: AlignmentDirectional.center,
                child: Text(
                  '字体大小',
                  style: Style.style_text1_18_w500,
                ),
              ),
              Positioned(
                top: 0,
                bottom: 0,
                right: 0,
                child: JkInkWell(
                  child: Container(
                    padding: EdgeInsets.only(
                        top: UIUtils.dp(16),
                        bottom: UIUtils.dp(16),
                        left: UIUtils.dp(16),
                        right: UIUtils.dp(16)),
                    width: UIUtils.dp(50),
                    height: UIUtils.dp(50),
                    alignment: AlignmentDirectional.center,
                    child: Image.asset(
                      'assets/exercise/ic_close.png',
                      width: UIUtils.dp(36),
                      height: UIUtils.dp(36),
                      fit: BoxFit.contain,
                    ),
                  ),
                  onTap: () {
                    dismiss();
                  },
                ),
              ),
            ],
          ),
          Container(
            margin:
                EdgeInsets.only(left: UIUtils.dp(20), right: UIUtils.dp(20)),
            height: UIUtils.dp(120),
            alignment: AlignmentDirectional.center,
            child: Row(
              children: <Widget>[
                Text(
                  'A',
                  style: Style.style_text1_18_bold,
                ),
                Expanded(
                  child: NodeSlisder(
                    width: UIUtils.width - UIUtils.dp(100),
                    index: index,
                    section: 3,
                    isEnabled: true,
                    isFull: true,
                    unActiveTrackColor: Style.text3,
                    activeTrackColor: Style.blue,
                    label: ['特小', '标准', '', '特大'],
                    onChanged: (value) {
                      print("----------> value = $value");
                      if (value == 0) {
                        ITrain.get().fontSize = 'min';
                        Storage.setString(Storage.fontSize, 'min');
                      } else if (value == 1) {
                        ITrain.get().fontSize = 'small';
                        Storage.setString(Storage.fontSize, 'small');
                      } else if (value == 2) {
                        ITrain.get().fontSize = 'middle';
                        Storage.setString(Storage.fontSize, 'middle');
                      } else if (value == 3) {
                        ITrain.get().fontSize = 'big';
                        Storage.setString(Storage.fontSize, 'big');
                      }
                      IEventBus.get()
                          .post(IEvent(JkKey.EVENT_REFRESH_FONTSIZE, ''));
                    },
                  ),
                ),
                Text(
                  'A',
                  style: Style.style_text1_18_bold,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class DialogShowFace extends Dialog {
  DialogShowFace({Key? key}) : super(key: key);

  static Future<void> dismiss() async {
    try {
      ShowFaceWidget?.dismiss();
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return ShowFaceWidget();
  }
}

class ShowFaceWidget extends StatefulWidget {
  const ShowFaceWidget({Key? key}) : super(key: key);

  @override
  _ShowFaceWidgetState createState() => _ShowFaceWidgetState();

  static Future<void> dismiss() async {
    try {
      _ShowFaceWidgetState.state?.dismiss();
    } catch (e) {}
  }
}

class _ShowFaceWidgetState extends State<ShowFaceWidget> {
  static _ShowFaceWidgetState? state;

  _ShowFaceWidgetState() {
    state = this;
  }

  bool isSure = false;

  @override
  void initState() {
    super.initState();
    isSure = false;
  }

  @override
  void dispose() {
    super.dispose();
  }

  void dismiss() {
    Navigator.pop(context);
    state = null;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: UIUtils.height / 2,
          child: JkInkWell(
            child: Image.asset(
              isSure
                  ? 'assets/exercise/jk_ic_show_face_sure.png'
                  : 'assets/exercise/jk_ic_show_face.png',
              width: UIUtils.width - UIUtils.dp(56),
              fit: BoxFit.fitWidth,
            ),
            onTap: () {
              if (isSure) {
                dismiss();
              } else {
                if (mounted) {
                  setState(() {
                    isSure = true;
                  });
                }
              }
            },
          ),
        ),
      ),
    );
  }
}

class DialogQuesAnswer extends Dialog {
  int position;
  QuizMo? mo;
  String selection;
  Function(String type)? callback;

  DialogQuesAnswer(
      {Key? key,
      this.position = 0,
      this.mo,
      this.selection = '',
      required this.callback})
      : super(key: key);

  static Future<void> dismiss() async {
    try {
      QuesAnswerWidget?.dismiss();
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return QuesAnswerWidget(
        position: position, mo: mo, selection: selection, callback: callback);
  }
}

class QuesAnswerWidget extends StatefulWidget {
  const QuesAnswerWidget(
      {Key? key, this.position, this.mo, this.selection, this.callback})
      : super(key: key);

  final int? position; //
  final QuizMo? mo; //
  final String? selection; //
  final Function(String type)? callback;

  @override
  _QuesAnswerWidgetState createState() => _QuesAnswerWidgetState();

  static Future<void> dismiss() async {
    try {
      _QuesAnswerWidgetState.state?.dismiss();
    } catch (e) {}
  }
}

class _QuesAnswerWidgetState extends State<QuesAnswerWidget> {
  static _QuesAnswerWidgetState? state;

  _QuesAnswerWidgetState() {
    state = this;
  }

  late Timer? _timer; //倒计时的计时器
  int count = 0;

  @override
  void initState() {
    super.initState();
    startTime();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void dismiss() {
    DialogBigPicture.dismiss();
    Navigator.pop(context);
    state = null;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          margin: EdgeInsets.only(top: UIUtils.dp(16), bottom: UIUtils.dp(16)),
          padding: EdgeInsets.only(top: UIUtils.dp(6), bottom: UIUtils.dp(6)),
          width: UIUtils.height - UIUtils.dp(300),
          constraints: BoxConstraints(
            minHeight: UIUtils.dp(300),
          ),
          decoration: Style.style_box_blue16_r4,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                alignment: AlignmentDirectional.centerStart,
                margin:
                    EdgeInsets.only(top: UIUtils.dp(5), left: UIUtils.dp(12)),
                child: Text(
                  '错题学习',
                  style: Style.style_white_12_w500,
                ),
              ),
              Expanded(
                child: Container(
                  margin: EdgeInsets.only(
                      top: UIUtils.dp(5),
                      left: UIUtils.dp(5),
                      right: UIUtils.dp(5)),
                  padding: EdgeInsets.only(
                      top: UIUtils.dp(6),
                      bottom: UIUtils.dp(6),
                      left: UIUtils.dp(10),
                      right: UIUtils.dp(10)),
                  color: Style.bg5,
                  width: UIUtils.height - UIUtils.dp(250),
                  child: XScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          margin: EdgeInsets.only(bottom: UIUtils.dp(3)),
                          child: Text(
                            '${widget.position!}、${widget.mo!.sub_Titles}',
                            style: Style.style_text1_h14_w500,
                          ),
                        ),
                        OptionView('A、', widget.mo!.examOpta),
                        OptionView('B、', widget.mo!.examOptb),
                        if (widget.mo!.sub_type != JkKey.JUDGE)
                          OptionView('C、', widget.mo!.examOptc),
                        if (widget.mo!.sub_type != JkKey.JUDGE)
                          OptionView('D、', widget.mo!.examOptd),
                        SizedBox(
                          height: UIUtils.dp(3),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              getSub_pic(widget.mo!),
              Container(
                margin:
                    EdgeInsets.only(left: UIUtils.dp(5), right: UIUtils.dp(5)),
                padding: EdgeInsets.only(
                    top: UIUtils.dp(6),
                    bottom: UIUtils.dp(6),
                    left: UIUtils.dp(10),
                    right: UIUtils.dp(10)),
                color: Style.bg5,
                width: UIUtils.height - UIUtils.dp(250),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Text(
                      getContent(),
                      style: Style.style_text1_12_w500,
                    ),
                    Container(
                      margin: EdgeInsets.only(
                          top: UIUtils.dp(6), bottom: UIUtils.dp(6)),
                      child: InkWellShadow(
                        child: Container(
                          width: UIUtils.dp(120),
                          height: UIUtils.dp(26),
                          alignment: AlignmentDirectional.center,
                          decoration: Style.style_quiz_answer_r3,
                          child: Text(
                            '确定',
                            style: Style.style_text1_14_w500,
                          ),
                        ),
                        onTap: () {
                          cancelTimer();
                          dismiss();
                        },
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(bottom: UIUtils.dp(3)),
                      child: Text(
                        '页面将在${count}秒后自动关闭。返回考试主界面、',
                        style: Style.style_text1_12_w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String getContent() {
    String answer = widget.mo!.examAnswer.toUpperCase();
    if (widget.mo!.sub_type == JkKey.JUDGE) {
      if (answer == 'A') {
        return '正确答案：√ ，你的答案：${widget.selection!.toUpperCase() == 'A' ? '√' : '×'}';
      } else {
        return '正确答案：× ，你的答案：${widget.selection!.toUpperCase() == 'A' ? '√' : '×'}';
      }
    } else {
      return '正确答案：${answer.toUpperCase()} ，你的答案：${widget.selection!.toUpperCase()}';
    }
  }

  //倒计时
  void startTime() async {
    if (count > 0) {
      return;
    }
    if (mounted) {
      setState(() {
        count = 3;
      });
    }
    Timer(const Duration(), () {
      // 空等1秒之后再计时
      _timer = Timer.periodic(Duration(milliseconds: 1000), (v) {
        if (count <= 0) {
          cancelTimer();
          dismiss();
        } else {
          if (mounted) {
            setState(() {
              count--;
            });
          }
        }
      });
    });
  }

  void cancelTimer() {
    if (_timer != null) {
      _timer?.cancel();
    }
  }

  Widget OptionView(String option, String text) {
    return Container(
      margin: EdgeInsets.only(top: UIUtils.dp(3)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            option,
            style: Style.style_text1_h13_w500,
          ),
          Text(
            text,
            style: Style.style_text1_h13_w500,
          ),
        ],
      ),
    );
  }

  Widget getSub_pic(QuizMo? mo) {
    if (mo == null) {
      return Container(
        constraints: BoxConstraints(
          minHeight: UIUtils.dp(50),
        ),
      );
    }
    String sub_pic = Math.trim(mo.sub_pic);
    String dhlj = Math.trim(mo.dhlj);
    if (sub_pic.isNotEmpty) {
      return Container(
        margin: EdgeInsets.only(top: UIUtils.dp(5), bottom: UIUtils.dp(5)),
        alignment: AlignmentDirectional.center,
        child: JkInkWell(
          child: Image.asset(
            'assets/quiz/${sub_pic}',
            width: UIUtils.dp(240),
            height: UIUtils.dp(72),
            fit: BoxFit.contain,
            errorBuilder: (context, url, error) =>
                errorView(sub_pic, UIUtils.dp(240), UIUtils.dp(72), () {
              //点击看大图
              List<String> photos = <String>[];
              photos.add(ApiUrl.IMAGE_URL + sub_pic);
              DialogHelper.showBigPicture(context, 'network', photos, 0, true);
            }),
          ),
          onTap: () {
            //点击看大图
            List<String> photos = <String>[];
            photos.add('assets/quiz/${sub_pic}');
            DialogHelper.showBigPicture(context, 'assets', photos, 0, true);
          },
        ),
      );
    }
    if (dhlj.isNotEmpty && dhlj != '1') {
      if (dhlj.endsWith(".gif")) {
        return Container(
          margin: EdgeInsets.only(top: UIUtils.dp(5), bottom: UIUtils.dp(5)),
          alignment: AlignmentDirectional.center,
          child: JkInkWell(
            child: Image.asset(
              'assets/quiz/${dhlj}',
              width: UIUtils.dp(240),
              height: UIUtils.dp(72),
              fit: BoxFit.contain,
              errorBuilder: (context, url, error) =>
                  errorView(sub_pic, UIUtils.dp(240), UIUtils.dp(72), () {
                //点击看大图
                List<String> photos = <String>[];
                photos.add(ApiUrl.IMAGE_URL + dhlj);
                DialogHelper.showBigPicture(
                    context, 'network', photos, 0, true);
              }),
            ),
            onTap: () {
              //点击看大图
              List<String> photos = <String>[];
              photos.add('assets/quiz/${dhlj}');
              DialogHelper.showBigPicture(context, 'assets', photos, 0, true);
            },
          ),
        );
      } else {
        return Container(
          margin: EdgeInsets.only(top: UIUtils.dp(5), bottom: UIUtils.dp(5)),
          alignment: AlignmentDirectional.center,
          child: JkInkWell(
            child: Image.asset(
              'assets/quiz/${dhlj}.gif',
              width: UIUtils.dp(240),
              height: UIUtils.dp(72),
              fit: BoxFit.contain,
              errorBuilder: (context, url, error) =>
                  errorView(sub_pic, UIUtils.dp(240), UIUtils.dp(72), () {
                //点击看大图
                List<String> photos = <String>[];
                photos.add('${ApiUrl.IMAGE_URL + dhlj}.gif');
                DialogHelper.showBigPicture(
                    context, 'network', photos, 0, true);
              }),
            ),
            onTap: () {
              //点击看大图
              List<String> photos = <String>[];
              photos.add('assets/quiz/${dhlj}.gif');
              DialogHelper.showBigPicture(context, 'assets', photos, 0, true);
            },
          ),
        );
      }
    }
    return Container(
      constraints: BoxConstraints(
        minHeight: UIUtils.dp(50),
      ),
    );
  }

  Widget errorView(
      String fileName, double? width, double? height, Function()? onTap) {
    return JkInkWell(
      child: CachedNetworkImage(
        fit: BoxFit.contain,
        width: width,
        height: height,
        imageUrl: ApiUrl.IMAGE_URL + fileName,
        errorWidget: (context, url, error) => Container(
          constraints: BoxConstraints(
            minHeight: UIUtils.dp(50),
          ),
        ),
      ),
      onTap: () {
        if (onTap != null) {
          onTap();
        }
      },
    );
  }
}

class DialogRealSubmit extends Dialog {
  String title;
  String message;
  String cancel;
  String confirm;
  Function(String type)? callback;

  DialogRealSubmit(
      {Key? key,
      this.title = '',
      this.message = '',
      this.cancel = '',
      this.confirm = '',
      required this.callback})
      : super(key: key);

  static Future<void> dismiss() async {
    try {
      RealSubmitWidget?.dismiss();
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return RealSubmitWidget(
        title: title,
        message: message,
        cancel: cancel,
        confirm: confirm,
        callback: callback);
  }
}

class RealSubmitWidget extends StatefulWidget {
  const RealSubmitWidget(
      {Key? key,
      this.title,
      this.message,
      this.cancel,
      this.confirm,
      this.callback})
      : super(key: key);

  final String? title;
  final String? message;
  final String? cancel;
  final String? confirm;
  final Function(String type)? callback;

  @override
  _RealSubmitWidgetState createState() => _RealSubmitWidgetState();

  static Future<void> dismiss() async {
    try {
      _RealSubmitWidgetState.state?.dismiss();
    } catch (e) {}
  }
}

class _RealSubmitWidgetState extends State<RealSubmitWidget> {
  static _RealSubmitWidgetState? state;

  _RealSubmitWidgetState() {
    state = this;
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void dismiss() {
    Navigator.pop(context);
    state = null;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          margin: EdgeInsets.only(top: UIUtils.dp(40), bottom: UIUtils.dp(40)),
          width: UIUtils.height / 2,
          constraints: BoxConstraints(
            minHeight: UIUtils.dp(300),
          ),
          decoration: Style.style_box_white_r8,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                height: UIUtils.dp(50),
                alignment: AlignmentDirectional.center,
                decoration: Style.style_box_blue16_ab_r8,
                child: Text(
                  '考试确认窗口',
                  style: Style.style_white_16_w500,
                ),
              ),
              Expanded(
                child: Container(
                  padding: EdgeInsets.only(
                      top: UIUtils.dp(16),
                      bottom: UIUtils.dp(16),
                      left: UIUtils.dp(20),
                      right: UIUtils.dp(20)),
                  width: UIUtils.height / 2,
                  child: XScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          child: Text(
                            '操作提示：',
                            style: Style.style_blue19_15_w500,
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: UIUtils.dp(8)),
                          child: Text(
                            widget.message!,
                            style: Style.style_blue19_15_w500,
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: UIUtils.dp(8)),
                          child: Text(
                            '1：点击【确认交卷】，将提交考试成绩，结束考试！',
                            style: Style.style_blue19_15_w500,
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: UIUtils.dp(8)),
                          child: Text(
                            '2：点击【继续考试】，将关闭本窗口，继续考试！',
                            style: Style.style_blue19_15_w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.only(
                    top: UIUtils.dp(15),
                    bottom: UIUtils.dp(15),
                    left: UIUtils.dp(15),
                    right: UIUtils.dp(15)),
                decoration: Style.style_box_brown_cd_r8,
                child: Row(
                  children: <Widget>[
                    Expanded(
                      child: Container(
                        margin: EdgeInsets.only(
                            left: UIUtils.dp(10), right: UIUtils.dp(10)),
                        child: InkWellShadow(
                          child: Container(
                            height: UIUtils.dp(44),
                            alignment: AlignmentDirectional.center,
                            decoration: Style.style_box_blue16_r8,
                            child: Text(
                              widget.confirm!,
                              style: Style.style_white_16_w500,
                            ),
                          ),
                          onTap: () {
                            dismiss();
                            if (widget.callback != null) {
                              widget.callback!('confirm');
                            }
                          },
                        ),
                      ),
                    ),
                    Expanded(
                      child: Container(
                        margin: EdgeInsets.only(
                            left: UIUtils.dp(10), right: UIUtils.dp(10)),
                        child: JkInkWell(
                          child: Container(
                            height: UIUtils.dp(44),
                            alignment: AlignmentDirectional.center,
                            decoration: Style.style_box_white_r8,
                            child: Text(
                              widget.cancel!,
                              style: Style.style_text1_16_w500,
                            ),
                          ),
                          onTap: () {
                            dismiss();
                            if (widget.callback != null) {
                              widget.callback!('cancel');
                            }
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DialogRealResult extends Dialog {
  String title;
  int score;
  String confirm;
  Function(String type)? callback;

  DialogRealResult(
      {Key? key,
      this.title = '',
      this.score = 0,
      this.confirm = '',
      required this.callback})
      : super(key: key);

  static Future<void> dismiss() async {
    try {
      RealResultWidget?.dismiss();
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return RealResultWidget(
        title: title, score: score, confirm: confirm, callback: callback);
  }
}

class RealResultWidget extends StatefulWidget {
  const RealResultWidget(
      {Key? key, this.title, this.score, this.confirm, this.callback})
      : super(key: key);

  final String? title;
  final int? score;
  final String? confirm;
  final Function(String type)? callback;

  @override
  _RealResultWidgetState createState() => _RealResultWidgetState();

  static Future<void> dismiss() async {
    try {
      _RealResultWidgetState.state?.dismiss();
    } catch (e) {}
  }
}

class _RealResultWidgetState extends State<RealResultWidget> {
  static _RealResultWidgetState? state;

  _RealResultWidgetState() {
    state = this;
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void dismiss() {
    Navigator.pop(context);
    state = null;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          margin: EdgeInsets.only(top: UIUtils.dp(40), bottom: UIUtils.dp(40)),
          width: UIUtils.height / 2,
          constraints: BoxConstraints(
            minHeight: UIUtils.dp(300),
          ),
          decoration: Style.style_box_white_r8,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                height: UIUtils.dp(50),
                alignment: AlignmentDirectional.center,
                decoration: Style.style_box_blue16_ab_r8,
                child: Text(
                  '信息提示',
                  style: Style.style_white_16_w500,
                ),
              ),
              Expanded(
                child: Container(
                  padding: EdgeInsets.only(
                      top: UIUtils.dp(16),
                      bottom: UIUtils.dp(16),
                      left: UIUtils.dp(20),
                      right: UIUtils.dp(20)),
                  width: UIUtils.height / 2,
                  child: XScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          child: Text(
                            '尊敬的驾考学员您好！',
                            style: Style.style_text1_15_w500,
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: UIUtils.dp(8)),
                          child: Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: '您的本次模拟考试成绩为：',
                                  style: Style.style_text1_15_w500,
                                ),
                                TextSpan(
                                  text: '${widget.score!}分',
                                  style: Style.style_red5_15_w500,
                                ),
                              ],
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: UIUtils.dp(8)),
                          child: Text(
                            widget.score! < 90
                                ? '考试不合格，还需要努力！！'
                                : widget.score! < 95
                                ? '考试合格，还需继续努力'
                                : '恭喜你考试合格，可以预约考试了',
                            style: Style.style_brown2_15_w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.only(
                    top: UIUtils.dp(15),
                    bottom: UIUtils.dp(15),
                    left: UIUtils.dp(15),
                    right: UIUtils.dp(15)),
                decoration: Style.style_box_brown_cd_r8,
                child: Container(
                  margin: EdgeInsets.only(
                      left: UIUtils.dp(60), right: UIUtils.dp(60)),
                  child: JkInkWell(
                    child: Container(
                      height: UIUtils.dp(44),
                      alignment: AlignmentDirectional.center,
                      decoration: Style.style_box_white_r8,
                      child: Text(
                        widget.confirm!,
                        style: Style.style_text1_16_w500,
                      ),
                    ),
                    onTap: () {
                      dismiss();
                      if (widget.callback != null) {
                        widget.callback!('confirm');
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PeriodTips extends Dialog {
  Function(String? type)? callback;

  PeriodTips({Key? key, required this.callback}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PeriodTipsWidget(callback: callback);
  }
}

class PeriodTipsWidget extends StatefulWidget {
  const PeriodTipsWidget({Key? key, this.callback}) : super(key: key);

  final Function(String? type)? callback;

  @override
  _PeriodTipsWidgetState createState() => _PeriodTipsWidgetState();
}

class _PeriodTipsWidgetState extends State<PeriodTipsWidget> {
  @override
  void initState() {
    super.initState();
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: UIUtils.width - UIUtils.dp(56),
          // height: 120,
          decoration: Style.style_box_white_r12,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(34),
                    bottom: UIUtils.dp(24),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: Text(
                  '1.“学习时长”：指本次您计时拍照签到--拍照签退期间的学习时长\n'
                  '2.“有效时长”：指“学习时长”上传至您属地监管平台后，监管平台按学时审核规则，判定的有效学时。'
                  '有效学时进度100%才能约考。\n\n'
                  '提示：\n'
                      '1、大象驾到负责上传学时至监管平台，有效学时以监管平台审核结果为准。\n'
                  '2、审核需要一定的时间，一般为工作日审核。\n'
                  '3、为避免审核不通过，计时拍照时，请衣着整洁、正向摄像头、避免逆光/黑暗环境、不要躺着拍照!!!',
                  style: Style.style_text1_h14_w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class TimingNotice extends Dialog {
  Function(String? type)? callback;

  TimingNotice({Key? key, required this.callback}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TimingNoticeWidget(callback: callback);
  }
}

class TimingNoticeWidget extends StatefulWidget {
  const TimingNoticeWidget({Key? key, this.callback}) : super(key: key);

  final Function(String? type)? callback;

  @override
  _TimingNoticeWidgetState createState() => _TimingNoticeWidgetState();
}

class _TimingNoticeWidgetState extends State<TimingNoticeWidget> {
  late Timer? _timer; //倒计时的计时器
  int count = 0;

  @override
  void initState() {
    super.initState();
    startTime();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
    }
  }

  void refreshUI() async {
    try {
      if (mounted) {
        setState(() {});
      }
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: UIUtils.width - UIUtils.dp(56),
          decoration: Style.style_box_white_r12,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(34),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: Text(
                  '关于驾培学时的严正通告',
                  style: Style.style_text1_18_w500,
                ),
              ),
              Container(
                constraints: BoxConstraints(
                  maxHeight: UIUtils.dp(330),
                ),
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(16),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: XScrollView(
                  child: Text(
                    '学员您好：\n'
                    '近期发现个别驾校（教练员）疑似通过不法人员采用技术手段诱导学员配合刷卡制造“假学时”（本人未实地学习），为切实保障学员利益，现将有关事项严正通告如下：\n'
                    '1、拒绝诱导，如实培训。“假学时”是扰乱驾校及行业正常教学秩序的严重违规行为，一经发现，学时将判定无效，涉及责任人或将移交司法部门处置，请广大学员拒绝诱导，如实培训。\n'
                    '2、学时异常，立即举报。学员若发现手机端学时记录明显少于实际培训时长时，应引起警觉，及时向驾校（教练员）查明原因，若存在违规行为请立即向行业举报。\n'
                    '3、配合造假，涉嫌违法。学员若同意或配合个别驾校（教练员）刷卡制造“假学时”，同属涉嫌扰乱驾培行业正常经营秩序行为，一经查实，涉事学员将可能按共同犯罪行为处置。\n'
                    '4、学时记录，长期保存。驾培学时记录是学员档案（电子）的重要组成部分，系统中将长期保存。随着技术升级和人工抽检，“假学时”或将判定为无效，同时涉及个人诚信，影响学员获取的驾照甚至涉嫌“共犯”违法。\n'
                    '5、抵制造假，营造氛围。“学会开车”是考取驾照的前提，也是杜绝“马路杀手”的基础。为保障他人生命财产安全，为了您自身及家人平安，请广大学员自觉抵制学时造假，诚信参加驾考培训，共同营造风清气正氛围。\n'
                    '特此通告',
                    style: Style.style_text1_h14_w500,
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                    top: UIUtils.dp(24),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: InkWellShadow(
                  child: Container(
                    height: UIUtils.dp(46),
                    alignment: AlignmentDirectional.center,
                    decoration: count > 0
                        ? Style.style_box_dark_r50
                        : Style.style_btn_blue_big,
                    child: Text(
                      count > 0 ? '阅读并同意（${count}秒）' : '我已阅读',
                      style: Style.style_white_16_w500,
                    ),
                  ),
                  onTap: () {
                    if (count <= 0) {
                      dismiss();
                      if (widget.callback != null) {
                        widget.callback!('confirm');
                      }
                    }
                  },
                ),
              ),
              JkInkWell(
                child: Container(
                  margin: EdgeInsets.only(
                      left: UIUtils.dp(16), right: UIUtils.dp(16)),
                  height: UIUtils.dp(46),
                  alignment: AlignmentDirectional.center,
                  child: Text(
                    '不同意并退出',
                    style: Style.style_text3_14_w500,
                  ),
                ),
                onTap: () {
                  dismiss();
                  if (widget.callback != null) {
                    widget.callback!('cancel');
                  }
                },
              ),
              Container(
                height: UIUtils.dp(14),
              ),
            ],
          ),
        ),
      ),
    );
  }

  //倒计时
  void startTime() async {
    if (count > 0) {
      return;
    }
    count = 5;
    refreshUI();
    Timer(const Duration(), () {
      // 空等1秒之后再计时
      _timer = Timer.periodic(Duration(milliseconds: 1000), (v) {
        if (count <= 0) {
          cancelTimer();
          count = 0;
          refreshUI();
        } else {
          count--;
          refreshUI();
        }
      });
    });
  }

  void cancelTimer() {
    if (_timer != null) {
      _timer?.cancel();
    }
  }
}

class AppointExplains extends Dialog {
  String? msg;
  Function(String? type)? callback;

  AppointExplains({Key? key, this.msg, required this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppointExplainsWidget(msg: msg, callback: callback);
  }
}

class AppointExplainsWidget extends StatefulWidget {
  const AppointExplainsWidget({Key? key, this.msg, this.callback})
      : super(key: key);

  final String? msg;
  final Function(String? type)? callback;

  @override
  _AppointExplainsWidgetState createState() => _AppointExplainsWidgetState();
}

class _AppointExplainsWidgetState extends State<AppointExplainsWidget> {
  late Timer? _timer; //倒计时的计时器
  int count = 0;

  @override
  void initState() {
    super.initState();
    startTime();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
    }
  }

  void refreshUI() async {
    try {
      if (mounted) {
        setState(() {});
      }
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: UIUtils.width - UIUtils.dp(56),
          decoration: Style.style_box_white_r12,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(30),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: Text(
                  '文明交通志愿服务预约须知',
                  style: Style.style_blue_18_w500,
                ),
              ),
              Container(
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(16),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: XScrollView(
                  child: Text(
                    widget.msg ??
                        '上岗前须备好:红色警心、红色帽子、伸缩警棍\n\n在岗位上保证自己人身安全的情况下负责管理制止过往的行人，非机动车闯红灯的、行人不走斑马线横穿马路的、非机动车不在非机动车道内行驶、乱窜的',
                    style: Style.style_text1_h13_w500,
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                    top: UIUtils.dp(24),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: InkWellShadow(
                  child: Container(
                    height: UIUtils.dp(46),
                    alignment: AlignmentDirectional.center,
                    decoration: count > 0
                        ? Style.style_box_dark_r50
                        : Style.style_btn_blue_big,
                    child: Text(
                      count > 0 ? '阅读并同意（${count}秒）' : '知道了',
                      style: Style.style_white_16_w500,
                    ),
                  ),
                  onTap: () {
                    if (count <= 0) {
                      dismiss();
                      if (widget.callback != null) {
                        widget.callback!('confirm');
                      }
                    }
                  },
                ),
              ),
              Container(
                height: UIUtils.dp(24),
              ),
            ],
          ),
        ),
      ),
    );
  }

  //倒计时
  void startTime() async {
    if (count > 0) {
      return;
    }
    count = 5;
    refreshUI();
    Timer(const Duration(), () {
      // 空等1秒之后再计时
      _timer = Timer.periodic(Duration(milliseconds: 1000), (v) {
        if (count <= 0) {
          cancelTimer();
          count = 0;
          refreshUI();
        } else {
          count--;
          refreshUI();
        }
      });
    });
  }

  void cancelTimer() {
    if (_timer != null) {
      _timer?.cancel();
    }
  }
}

class TimingReminder extends Dialog {
  String? msg;
  Function(String? type)? callback;

  TimingReminder({Key? key, this.msg, required this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TimingReminderWidget(msg: msg, callback: callback);
  }
}

class TimingReminderWidget extends StatefulWidget {
  const TimingReminderWidget({Key? key, this.msg, this.callback})
      : super(key: key);

  final String? msg;
  final Function(String? type)? callback;

  @override
  _TimingReminderWidgetState createState() => _TimingReminderWidgetState();
}

class _TimingReminderWidgetState extends State<TimingReminderWidget> {
  late Timer? _timer; //倒计时的计时器
  int count = 0;

  @override
  void initState() {
    super.initState();
    startTime();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
    }
  }

  void refreshUI() async {
    try {
      if (mounted) {
        setState(() {});
      }
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: UIUtils.width - UIUtils.dp(56),
          decoration: Style.style_box_white_r12,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(30),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: const Text(
                  '温馨提示',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Container(
                // alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(16),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: DefaultTextStyle.of(context).style,
                    children: const <TextSpan>[
                      TextSpan(
                        text: '您当前发起学习的是',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: '科目一\n',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF268FF7),
                        ),
                      ),
                      TextSpan(
                        text: '您的科一学时已达标\n',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: '是否',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: '继续学习科目一',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFFFF4D4D),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                    top: UIUtils.dp(24),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: InkWellShadow(
                  child: Container(
                    height: UIUtils.dp(46),
                    alignment: AlignmentDirectional.center,
                    decoration: count > 0
                        ? Style.style_box_dark_r50
                        : Style.style_btn_blue_big,
                    child: Text(
                      count > 0 ? '我已确认（${count}秒）' : '我已确认',
                      style: Style.style_white_16_w500,
                    ),
                  ),
                  onTap: () {
                    if (count <= 0) {
                      dismiss();
                      if (widget.callback != null) {
                        widget.callback!('confirm');
                      }
                    }
                  },
                ),
              ),
              Container(
                height: UIUtils.dp(24),
              ),
            ],
          ),
        ),
      ),
    );
  }

  //倒计时
  void startTime() async {
    if (count > 0) {
      return;
    }
    count = 3;
    refreshUI();
    Timer(const Duration(), () {
      // 空等1秒之后再计时
      _timer = Timer.periodic(Duration(milliseconds: 1000), (v) {
        if (count <= 0) {
          cancelTimer();
          count = 0;
          refreshUI();
        } else {
          count--;
          refreshUI();
        }
      });
    });
  }

  void cancelTimer() {
    if (_timer != null) {
      _timer?.cancel();
    }
  }
}

class TimingError extends Dialog {
  TimingError({Key? key, this.callback}) : super(key: key);
  final Function()? callback;

  @override
  Widget build(BuildContext context) {
    return TimingErrorWidget(callback: callback);
  }
}

class TimingErrorWidget extends StatefulWidget {
  const TimingErrorWidget({Key? key, this.callback}) : super(key: key);
  final Function()? callback;

  @override
  _TimingErrorWidgetState createState() => _TimingErrorWidgetState();
}

class _TimingErrorWidgetState extends State<TimingErrorWidget> {
  bool check = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
    }
  }

  void refreshUI() async {
    try {
      if (mounted) {
        setState(() {});
      }
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: UIUtils.width - UIUtils.dp(56),
          decoration: Style.style_box_white_r12,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(34),
                    bottom: UIUtils.dp(12),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: Text(
                  '这些错误示例\n会影响你的“学时审核”',
                  style: Style.style_red_h18_w500,
                  textAlign: TextAlign.center,
                ),
              ),
              Container(
                constraints: BoxConstraints(
                  maxHeight: UIUtils.height - UIUtils.dp(360),
                ),
                child: XScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Container(
                        alignment: AlignmentDirectional.center,
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(12),
                            left: UIUtils.dp(16),
                            right: UIUtils.dp(16)),
                        child: Image.asset(
                          'assets/camera/icon_tangzhe.png',
                          width: UIUtils.dp(160),
                          fit: BoxFit.fitWidth,
                        ),
                      ),
                      Container(
                        alignment: AlignmentDirectional.center,
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(4),
                            left: UIUtils.dp(16),
                            right: UIUtils.dp(16)),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Image.asset(
                              'assets/camera/icon_red_selected.png',
                              width: UIUtils.dp(18),
                              fit: BoxFit.fitWidth,
                            ),
                            Container(
                              margin: EdgeInsets.only(left: UIUtils.dp(8)),
                              child: Text(
                                '拿正手机、不要躺着',
                                style: Style.style_text2_16_w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        alignment: AlignmentDirectional.center,
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(16),
                            left: UIUtils.dp(16),
                            right: UIUtils.dp(16)),
                        child: Image.asset(
                          'assets/camera/icon_guangxian.png',
                          width: UIUtils.dp(160),
                          fit: BoxFit.fitWidth,
                        ),
                      ),
                      Container(
                        alignment: AlignmentDirectional.center,
                        margin: EdgeInsets.only(
                            top: UIUtils.dp(4),
                            bottom: UIUtils.dp(12),
                            left: UIUtils.dp(16),
                            right: UIUtils.dp(16)),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Image.asset(
                              'assets/camera/icon_red_selected.png',
                              width: UIUtils.dp(18),
                              fit: BoxFit.fitWidth,
                            ),
                            Container(
                              margin: EdgeInsets.only(left: UIUtils.dp(8)),
                              child: Text(
                                '请在光线充足的地方拍照',
                                style: Style.style_text2_16_w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                    top: UIUtils.dp(12),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: InkWellShadow(
                  child: Container(
                    height: UIUtils.dp(46),
                    alignment: AlignmentDirectional.center,
                    decoration: Style.style_btn_blue_middle,
                    child: Text(
                      '知道了',
                      style: Style.style_white_16_w500,
                    ),
                  ),
                  onTap: () {
                    Storage.setBool('TimingError', check);
                    dismiss();
                    if (widget.callback != null) {
                      widget.callback!();
                    }
                  },
                ),
              ),
              Container(
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(
                    top: UIUtils.dp(12),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                child: JkInkWell(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Image.asset(
                        check
                            ? 'assets/camera/icon_selected.png'
                            : 'assets/camera/icon_normal.png',
                        width: UIUtils.dp(20),
                        height: UIUtils.dp(20),
                      ),
                      Container(
                        margin: EdgeInsets.only(left: UIUtils.dp(4)),
                        child: Text(
                          '下次不再提醒',
                          style: Style.style_text3_14_w500,
                        ),
                      ),
                    ],
                  ),
                  onTap: () {
                    check = !check;
                    refreshUI();
                  },
                ),
              ),
              Container(
                height: UIUtils.dp(24),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DialogDatePicker extends Dialog {
  DateTime? date;
  Function(DateTime? date)? callback;

  DialogDatePicker({Key? key, this.date, required this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DatePickerWidget(date: date, callback: callback);
  }
}

class DatePickerWidget extends StatefulWidget {
  const DatePickerWidget({Key? key, this.date, this.callback})
      : super(key: key);

  final DateTime? date;

  final Function(DateTime? date)? callback;

  @override
  _DatePickerWidgetState createState() => _DatePickerWidgetState();
}

class _DatePickerWidgetState extends State<DatePickerWidget> {
  DateTime? firstDate;
  DateTime? lastDate;
  List<String> monthNames = <String>[];

  @override
  void initState() {
    super.initState();
    DateTime now = DateTime.now();
    firstDate = DateTime(now.year, now.month);
    lastDate = DateTime(now.year + 1, now.month);
    for (int i = 0; i < 12; i++) {
      monthNames.add(DateUtil.formatDate(DateTime(now.year, now.month + i),
          format: DateFormats.zh_y_mo));
    }
  }

  void dismiss() {
    if (context != null) {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: Style.style_box_white_ab_r12,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Container(
            margin: EdgeInsets.only(
                top: UIUtils.dp(18),
                left: UIUtils.dp(18),
                right: UIUtils.dp(18)),
            child: Row(
              children: <Widget>[
                SizedBox(width: UIUtils.dp(24)),
                Expanded(
                  child: Container(
                    alignment: AlignmentDirectional.center,
                    child: Text(
                      '您科目一预约考试的日期是？',
                      style: Style.style_text1_18_bold,
                    ),
                  ),
                ),
                JkInkWell(
                  child: Image.asset(
                    'assets/home_img/ic_close_gray.png',
                    width: UIUtils.dp(24),
                    fit: BoxFit.fitWidth,
                  ),
                  onTap: () {
                    dismiss();
                  },
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: UIUtils.dp(5)),
            child: Text(
              '向您推荐更高效的学习方案',
              style: Style.style_text3_14,
            ),
          ),
          Container(
            constraints: BoxConstraints(
              maxHeight: UIUtils.dp(440),
            ),
            alignment: AlignmentDirectional.center,
            child: CalendarList(
              firstDate: firstDate,
              lastDate: lastDate,
              date: widget.date,
              monthNames: monthNames,
              callback: (date) {
                Logs.e("选中 date = ${date}");
                dismiss();
                if (widget.callback != null) {
                  widget.callback!(date);
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}



class LinearProgressLoader extends StatefulWidget {
  const LinearProgressLoader({super.key});

  @override
  State<LinearProgressLoader> createState() => _LinearProgressLoaderState();
}

class _LinearProgressLoaderState extends State<LinearProgressLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    // 创建动画控制器，时长2秒
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    // 创建0→1的线性动画
    _animation = Tween<double>(begin: 0, end: 1).animate(_controller)
      ..addListener(() {
        setState(() {}); // 动画值更新时重绘UI
      })
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          print("进度完成！");
        }
      });

    _controller.forward(); // 启动动画
  }

  @override
  void dispose() {
    _controller.dispose(); // 释放控制器
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 线性进度条
        LinearProgressIndicator(
          value: _animation.value, // 绑定动画值
          backgroundColor: Color(0xFFCFDEFF),
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2565F0)),
          minHeight: 8,
          borderRadius: BorderRadius.circular(5),
        ),
        // const SizedBox(height: 20),
        // // 显示百分比文本
        // Text(
        //   "${(_animation.value * 100).toStringAsFixed(0)}%",
        //   style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        // )
      ],
    );
  }
}
import 'package:component_library/component_library.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:user_repository/src/models/user_account_dm.dart';

extension DarkModePreferenceCMToDomain on DarkModePreferenceCM {
  DarkModePreference toDomainModel() {
    switch (this) {
      case DarkModePreferenceCM.alwaysDark:
        return DarkModePreference.alwaysDark;
      case DarkModePreferenceCM.alwaysLight:
        return DarkModePreference.alwaysLight;
      case DarkModePreferenceCM.accordingToSystemPreferences:
        return DarkModePreference.useSystemSettings;
    }
  }
}

extension UserCMToDomain on UserAccountCM {
  UserAccount toDoMain() {
    return UserAccount(
        uid: accountUid,
        sid: accountSid,
        name: name,
        bindMobile: bindMobile,
        mobile: mobile,
        birth: birth,
        gender: gender,
        reqisterDivision: registerDivision,
        isBind: isBind,
        platCode: platCode,
        realAuthstatus: realAuthStatus,
        isVip: isVip,
        isRecorded: isRecorded,
        image: image,
        platSchoolId: platSchoolId,
        platSchoolName: platSchoolName,
        platTrainType: platTrainType,
        topicType: topicType,
        platRegisterDate: platRegisterDate,
        registerDate: registerDate,
        idCard: idCard,
        vip1ExpireValue: vip1ExpireValue,
        vip2ExpireValue: vip2ExpireValue,
        vip3ExpireValue: vip3ExpireValue,
        vip4ExpireValue: vip4ExpireValue,
        platGradStatus: platGradStatus,);
  }
}

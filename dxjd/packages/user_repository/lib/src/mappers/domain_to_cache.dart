import 'package:key_value_storage/key_value_storage.dart';
import 'package:user_repository/src/models/user_account_dm.dart';

extension DoMainToCache on UserAccount {
  UserAccountCM doMainToCacheModel() {
    return UserAccountCM(
        accountUid: uid,
        accountSid: sid,
        birth: birth,
        name: name,
        image: image,
        gender: gender,
        isBind: isBind,
        platSchoolId: platSchoolId,
        platSchoolName: platSchoolName,
        platTrainType: platTrainType,
        topicType: topicType,
        idCard: idCard,
        bindMobile: bindMobile,
        mobile: mobile,
        realAuthStatus: realAuthstatus,
        registerDivision: reqisterDivision,
        isRecorded: isRecorded,
        isVip: isVip,
        platRegisterDate: platRegisterDate,
        registerDate: registerDate,
        platCode: platCode,
        vip1ExpireValue: vip1ExpireValue,
        vip2ExpireValue: vip2ExpireValue,
        vip3ExpireValue: vip3ExpireValue,
        vip4ExpireValue: vip4ExpireValue,
        platGradStatus: platGradStatus,);
  }
}

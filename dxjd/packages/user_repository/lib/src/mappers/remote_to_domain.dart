import 'package:api/api.dart';
import 'package:user_repository/src/models/user_account_dm.dart';

extension UserAccountRMToDoMain on UserAccountRm {
  UserAccount toDoMainModels() {
    return UserAccount(
        uid: uid,
        sid: sid,
        name: name,
        bindMobile: bindMobile,
        mobile: mobile,
        birth: birth,
        gender: gender,
        reqisterDivision: registerDivision,
        isBind: isBind,
        platCode: platCode,
        realAuthstatus: realAuthstatus,
        isVip: isVip,
        isRecorded: isRecorded,
        image: image,
        platSchoolId: platSchoolId,
        platSchoolName: platSchoolName,
        platTrainType: platTrainType,
        topicType: topicType,
        platRegisterDate: platRegisterDate,
        registerDate: registerDate,
        idCard: idCard,
        vip1ExpireValue: vip1ExpireValue,
        vip2ExpireValue: vip2ExpireValue,
        vip3ExpireValue: vip3ExpireValue,
        vip4ExpireValue: vip4ExpireValue,
        platGradStatus: platGradStatus,);
  }
}

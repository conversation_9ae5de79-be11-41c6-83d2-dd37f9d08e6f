class UserAccount {
  String uid;
  String? sid;
  String? name;
  String? bindMobile;
  String? mobile;
  int? birth;
  int? gender;
  int? reqisterDivision;
  int? isBind;
  int? platCode;
  int? realAuthstatus;
  int? isVip;
  int? isRecorded;
  String? image;
  String? platSchoolId;
  String? platSchoolName;
  String? platTrainType;
  String? topicType;
  String? idCard;
  int? platRegisterDate;
  int? registerDate;
  int? vip1ExpireValue;
  int? vip2ExpireValue;
  int? vip3ExpireValue;
  int? vip4ExpireValue;
  int? platGradStatus;

  UserAccount({
    required this.uid,
    required this.sid,
    required this.name,
    required this.bindMobile,
    required this.mobile,
    required this.birth,
    required this.gender,
    required this.reqisterDivision,
    required this.isBind,
    required this.platCode,
    required this.realAuthstatus,
    required this.isVip,
    required this.isRecorded,
    required this.image,
    required this.platSchoolId,
    required this.platSchoolName,
    required this.platTrainType,
    required this.topicType,
    required this.idCard,
    required this.platRegisterDate,
    required this.registerDate,
    required this.vip1ExpireValue,
    required this.vip2ExpireValue,
    required this.vip3ExpireValue,
    required this.vip4ExpireValue,
    required this.platGradStatus,
  });

  // 将 UserAccount 对象转换为 Map
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'sid': sid,
      'name': name,
      'bindMobile': bindMobile,
      'mobile': mobile,
      'birth': birth,
      'gender': gender,
      'reqisterDivision': reqisterDivision,
      'isBind': isBind,
      'platCode': platCode,
      'realAuthstatus': realAuthstatus,
      'isVip': isVip,
      'isRecorded': isRecorded,
      'image': image,
      'platSchoolId': platSchoolId,
      'platSchoolName': platSchoolName,
      'platTrainType': platTrainType,
      'topicType': topicType,
      'idCard': idCard,
      'platRegisterDate': platRegisterDate,
      'registerDate': registerDate,
      'vip1ExpireValue': vip1ExpireValue,
      'vip2ExpireValue': vip2ExpireValue,
      'vip3ExpireValue': vip3ExpireValue,
      'vip4ExpireValue': vip4ExpireValue,
      'platGradStatus': platGradStatus,
    };
  }

  @override
  String toString() {
    // TODO: implement toString
    return 'UserAccount{uid: $uid, sid: $sid, name: $name, bindMobile: $bindMobile, mobile: $mobile, birth: $birth, gender: $gender, reqisterDivision: $reqisterDivision, isBind: $isBind, platCode: $platCode, realAuthstatus: $realAuthstatus, isVip: $isVip, isRecorded: $isRecorded, image: $image, platSchoolId: $platSchoolId, platSchoolName: $platSchoolName, platTrainType: $platTrainType, topicType: $topicType, idCard: $idCard}';
  }
}

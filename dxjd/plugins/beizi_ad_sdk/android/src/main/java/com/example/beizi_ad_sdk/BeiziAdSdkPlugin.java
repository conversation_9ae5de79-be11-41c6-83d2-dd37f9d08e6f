package com.example.beizi_ad_sdk;

import static android.content.ContentValues.TAG;

import static androidx.core.content.ContextCompat.startActivity;
import static xyz.adscope.amps.common.AMPSConstants.AMPS_LOG_TAG;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import java.util.Map;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;
import xyz.adscope.amps.AMPSSDK;
import xyz.adscope.amps.ad.interstitial.AMPSInterstitialAd;
import xyz.adscope.amps.ad.interstitial.AMPSInterstitialLoadEventListener;
import xyz.adscope.amps.ad.reward.AMPSRewardVideoAd;
import xyz.adscope.amps.ad.reward.AMPSRewardVideoLoadEventListener;
import xyz.adscope.amps.ad.splash.AMPSSplashAd;
import xyz.adscope.amps.ad.splash.AMPSSplashLoadEventListener;
import xyz.adscope.amps.common.AMPSConstants;
import xyz.adscope.amps.common.AMPSError;
import xyz.adscope.amps.config.AMPSPrivacyConfig;
import xyz.adscope.amps.config.AMPSRequestParameters;
import xyz.adscope.amps.init.AMPSInitConfig;
import xyz.adscope.amps.init.inter.IAMPSInitCallback;
import xyz.adscope.amps.tool.util.AMPSScreenUtil;
import xyz.adscope.common.tool.LogUtil;

/**
 * BeiziAdSdkPlugin
 */
public class BeiziAdSdkPlugin implements FlutterPlugin, ActivityAware, MethodCallHandler {
    /// The MethodChannel that will the communication between Flutter and native Android
    ///
    /// This local reference serves to register the plugin with the Flutter Engine and unregister it
    /// when the Flutter Engine is detached from the Activity
    public static MethodChannel channel;

    private Context mContext;
    private Activity mActivity;
    private AMPSSplashAd mSplashAd;
    private AMPSSplashLoadEventListener mSplashLoadEventListener;

    private AMPSInterstitialAd mAMPSInterstitialAd;

    private AMPSRewardVideoAd mAMPSRewardVideoAd;

    private MyAndroidBannerViewFactory mBannerViewFactory;

    private FlutterPluginBinding pluginBinding;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "xw.dxjk.beizi_ad");
        channel.setMethodCallHandler(this);
        pluginBinding = flutterPluginBinding;


//        mBannerViewFactory =new MyAndroidBannerViewFactory();
////        for (int i = 0; i < 4; i++) {
//
//            flutterPluginBinding.getPlatformViewRegistry().
//                    registerViewFactory("plugins.beizi_ad_sdk/BannerAd",
//                            mBannerViewFactory);
////        }

    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        switch (call.method) {
            case "getPlatformVersion":
                result.success("Android " + android.os.Build.VERSION.RELEASE);
                break;
            case "cjSdkSetup":
                initBeiAd(call.argument("configId"));
                result.success("initBeiAd success");
                break;
            case "cjLoadAndShowSplashMethod":
              showSplashAd(call.argument("advertId"));
//        showInsetAd();
//                showRewardAd();

                result.success("showSplashAd success");
                break;
            case "cjLoadAndShowInterstitialAdMethod":
                showInsetAd(call.argument("advertId"));
                result.success("showInsetAd success");
                break;
            case "cjLoadAndShowRewardVideoMethod":
                showRewardAd(call.argument("advertId"));
                result.success("showInsetAd success");
                break;
            case "cjLoadAndShowBannerMethod":
                mBannerViewFactory.updateBannerView(mActivity, call.argument("advertId"));
//                showBannerAd(call.argument("advertId"));
                result.success("BannerAd success");
                break;
            case "cjPreLoadSplashMethod":
//                mSplashAd.addPreLoadAdInfo();
                mSplashAd.preLoad();
//                mSplashAd.getPreloadAMPSOpenApiManager().preLoad();
                result.success("preLoadSplashAd success");
                break;
            default:
                result.notImplemented();
                break;
        }
    }

    private void showBannerAd(String advertId) {
        Intent intent = new Intent(mActivity, UnifiedNativeAdActivity.class);
        intent.putExtra("advertId",advertId);
        mActivity.startActivity(intent);
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        mActivity = binding.getActivity();
        mContext = mActivity.getBaseContext();
        // 注册视图工厂（确保在 Activity 可用时注册）
        registerViewFactories();
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {

    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        mActivity=binding.getActivity();
    }

    @Override
    public void onDetachedFromActivity() {
        mActivity = null;
        mSplashAd.destroy();
        mSplashAd = null;

    }

    private void registerViewFactories() {
        if (pluginBinding != null && mActivity != null) {
            // 使用 Activity 的 Context 创建视图工厂
            mBannerViewFactory =new MyAndroidBannerViewFactory(mActivity);
//        for (int i = 0; i < 4; i++) {

            pluginBinding.getPlatformViewRegistry().
                    registerViewFactory("plugins.beizi_ad_sdk/BannerAd",
                            mBannerViewFactory);
//        }
        }
    }


    //初始化广告
    private void initBeiAd(String configId) {
        //建议在application里面调用；假如App有功能引导，也可点击"立即体验"按钮中调用

        AMPSInitConfig config = new AMPSInitConfig.Builder()
                .setAppId(configId)
                .setAppName("testAppName")
                .setAMPSPrivacyConfig(new AMPSPrivacyConfig() {

                    @Override
                    public boolean isCanUsePhoneState() {
                        return super.isCanUsePhoneState();
                    }

                    @Override
                    public boolean isCanUseAndroidId() {
                        return false;
                    }

                    @Override
                    public boolean isCanUseAppList() {
                        return false;
                    }
                })
                .build();
        AMPSSDK.init(mContext, config, new IAMPSInitCallback() {
            @Override
            public void successCallback() {
                Log.e(TAG, "--------------successCallback: init success");
                channel.invokeMethod("setupSuccess", null);
            }

            @Override
            public void failCallback(AMPSError ampsError) {
                Log.e(TAG, "--------------failCallback: "+ampsError.getMessage() );
                channel.invokeMethod("setupFailed", null);
            }
        });

    }

    //开屏广告
    private void showSplashAd(String advertId) {
        Log.e(TAG, "showSplashAd: --==================" );

//        Intent intent = new Intent();
////        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        intent.setClass(mActivity,SplashActivity.class);
////        startActivity(mActivity,intent,null);
//        mActivity.startActivity(intent);
//        Intent intent = new Intent(mActivity, SplashActivity.class);
//        intent.putExtra("advertId",advertId);
//        mActivity.startActivity(intent);
//        Log.e(TAG, "showSplashAd: ------------------"+mActivity.getLocalClassName() );

    View layout = LayoutInflater.from(mContext).inflate(R.layout.splash_layout, null);
    RelativeLayout fl_splash = layout.findViewById(R.id.ads_rl);
    RelativeLayout splash_rl = layout.findViewById(R.id.splash_rl);
    LinearLayout splash_ll = layout.findViewById(R.id.splash_ll);
//      LinearLayout ll_logo = layout.findViewById(R.id.ll_logo);
//      ll_logo.setVisibility(View.INVISIBLE);
//      ParamsUtils.get().layoutParams(fl_splash, UIUtils.width, (int) (UIUtils.height * 8.2f / 10));
//      ParamsUtils.get().layoutParams(ll_logo, UIUtils.width, (int) (UIUtils.height * 1.8f / 10));
    ((ViewGroup) mActivity.getWindow().getDecorView()).addView(layout);
        AMPSRequestParameters parameter = new AMPSRequestParameters.Builder()
                .setSpaceId(advertId)//广告位Id，由后台配置或运营提供
                .setTimeOut(5000)//超时时长
                .setWidth(AMPSScreenUtil.getScreenWidth(mContext))//广告展示的宽,单位PX
                .setHeight(AMPSScreenUtil.getScreenHeight(mContext))//广告展示的高,单位PX
                .build();
        //2，构建开屏请求实例

        mSplashAd = new AMPSSplashAd(mActivity, parameter, new AMPSSplashLoadEventListener() {
            @Override
            public void onAmpsAdLoaded() {
                Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAdLoad");
                if (mSplashAd != null) {
                    mSplashAd.show(fl_splash);
                    channel.invokeMethod("splashAdLoadSuccess", null);
                }

            }

            @Override
            public void onAmpsAdFailed(AMPSError ampsError) {
                Log.e(AMPSConstants.AMPS_LOG_TAG,
                        TAG + " onAdError code:" + ampsError.getCode() + ";" +
                                " message:" + ampsError.getMessage());
                fl_splash.setVisibility(View.GONE);
                splash_rl.setVisibility(View.GONE);
                splash_ll.setVisibility(View.GONE);
                channel.invokeMethod("splashAdLoadFailed", null);

            }

            @Override
            public void onAmpsAdShow() {
                Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdShow");
                channel.invokeMethod("splashAdOnShow", null);

            }

            @Override
            public void onAmpsAdClicked() {
                Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdClicked");
            }

            @Override
            public void onAmpsAdDismiss() {
                Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdDismiss");
//        handler.removeCallbacks(null);
//        finishActivity();
//          mActivity.finishActivity(0);

                channel.invokeMethod("splashAdOnClose", null);
                if (mSplashAd != null) {
                    mSplashAd.destroy();
                }
                mSplashAd = null;
                fl_splash.setVisibility(View.GONE);
                splash_rl.setVisibility(View.GONE);
                splash_ll.setVisibility(View.GONE);
            }
        });//整体开屏广告回调
        //3，加载广告
        if (mSplashAd.isReady()) {
            mSplashAd.show(fl_splash);
        }else {
            mSplashAd.loadAd();
        }
    }

    private void showInsetAd(String advertId) {
        mAMPSInterstitialAd = null;
        AMPSRequestParameters parameter = new AMPSRequestParameters.Builder()
                .setSpaceId(advertId)
                .setTimeOut(5000)
                .setWidth(600)
                .setHeight(600)
                .build();
        mAMPSInterstitialAd = new AMPSInterstitialAd(mActivity, parameter,
                new AMPSInterstitialLoadEventListener() {
                    @Override
                    public void onAmpsSkippedAd() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsSkippedAd");
                    }

                    @Override
                    public void onAmpsAdLoaded() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAdLoad");
//                        showView.setVisibility(View.VISIBLE);
                        mAMPSInterstitialAd.show(mActivity);
                        channel.invokeMethod("interstitialAdLoadSuccess", null);
                    }

                    @Override
                    public void onAmpsAdFailed(AMPSError ampsError) {
                        Log.e(AMPSConstants.AMPS_LOG_TAG,
                                TAG + " onAdError code:" + ampsError.getCode() + ";" +
                                        " message:" + ampsError.getMessage());
                        channel.invokeMethod("interstitialAdLoadFailed", null);

                    }

                    @Override
                    public void onAmpsAdShow() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdShow");
                        channel.invokeMethod("interstitialAdOnShow", null);
                    }

                    @Override
                    public void onAmpsAdClicked() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdClicked");
                    }

                    @Override
                    public void onAmpsAdDismiss() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdDismiss");
//                        showView.setVisibility(View.GONE);
                        channel.invokeMethod("interstitialAdOnClose", null);
                    }

                    @Override
                    public void onAmpsVideoPlayStart() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsVideoPlayStart");
                    }

                    @Override
                    public void onAmpsVideoPlayEnd() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsVideoPlayEnd");
                    }
                });

        mAMPSInterstitialAd.loadAd();
    }

    //激励视频
    private void showRewardAd(String advertId) {
        AMPSRequestParameters parameter = new AMPSRequestParameters.Builder()
                .setSpaceId(advertId)//激励视频广告位id
                .setTimeOut(5000)//超时时间
                .setAdCount(1)//设置请求广告条数
                .build();

        mAMPSRewardVideoAd = new AMPSRewardVideoAd(mActivity, parameter,
                new AMPSRewardVideoLoadEventListener() {
                    @Override
                    public void onAmpsAdLoad() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdLoad");
//                showView.setVisibility(View.VISIBLE);
                        mAMPSRewardVideoAd.show(mActivity);
                        channel.invokeMethod("rewardVideoAdLoadSuccess", null);

                    }

                    @Override
                    public void onAmpsAdCached() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdCached");
                    }

                    @Override
                    public void onAmpsAdFailed(AMPSError error) {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdFailed");
                        channel.invokeMethod("rewardVideoAdLoadFailed", null);

                    }

                    @Override
                    public void onAmpsAdShow() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdShow");
                        channel.invokeMethod("rewardVideoAdOnShow", null);
                    }

                    @Override
                    public void onAmpsAdDismiss() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdClose");
//                showView.setVisibility(View.GONE);
                        channel.invokeMethod("rewardVideoAdOnClose", null);
                    }

                    @Override
                    public void onAmpsAdVideoClick() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdVideoClick");
                    }

                    @Override
                    public void onAmpsAdVideoComplete() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdVideoComplete");
                    }

                    @Override
                    public void onAmpsAdVideoError() {
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG + " onAmpsAdVideoError");
                        channel.invokeMethod("rewardVideoAdShowFailed", null);
                    }

                    @Override
                    public void onAmpsAdRewardArrived(boolean isRewardValid, int rewardType,
                                                      Map<String, Object> extraInfo) {
                        if (isRewardValid){
                            channel.invokeMethod("rewardVideoAdBeRewarded", null);
                        }
                        Log.e(AMPSConstants.AMPS_LOG_TAG, TAG +"----"+isRewardValid+ " onAmpsAdRewardArrived--"+rewardType+"--"+extraInfo);

                    }
                });//激励视频回调

        mAMPSRewardVideoAd.loadAd();
    }
}


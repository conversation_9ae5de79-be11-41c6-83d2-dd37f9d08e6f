package com.example.beizi_ad_sdk;

import static com.example.beizi_ad_sdk.BeiziAdSdkPlugin.channel;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;

import com.bumptech.glide.Glide;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.flutter.plugin.platform.PlatformView;
import xyz.adscope.amps.ad.unified.AMPSUnifiedNativeAd;
import xyz.adscope.amps.ad.unified.AMPSUnifiedNativeLoadEventListener;
import xyz.adscope.amps.ad.unified.inter.AMPSUnifiedNativeItem;
import xyz.adscope.amps.ad.unified.inter.AMPSUnifiedPattern;
import xyz.adscope.amps.ad.unified.view.AMPSUnifiedRootContainer;
import xyz.adscope.amps.ad.unified.view.AMPSUnifiedView;
import xyz.adscope.amps.common.AMPSConstants;
import xyz.adscope.amps.common.AMPSError;
import xyz.adscope.amps.config.AMPSRequestParameters;
import xyz.adscope.amps.tool.util.AMPSScreenUtil;

public class MyAndroidBannerView implements PlatformView {

    private final View view;

    private AMPSUnifiedRootContainer container;
    private RelativeLayout adView;
    private RelativeLayout adLogoRl;
    private RelativeLayout actionRl;
    private RelativeLayout ad_action_rl_iconUrl;
    private LinearLayout ad_logo_rl_all;
    private TextView titleTv;
    private TextView descTv;
    private ImageView iconCloseIv;

    private AMPSUnifiedNativeAd mAMPSUnifiedNativeAd;
    private List<AMPSUnifiedNativeItem> nativeItems;
    private List<AMPSUnifiedNativeItem> useNativeItems = new ArrayList<>();
    private CardView cardView;

    public MyAndroidBannerView(Context context, int viewId, Map<String, Object> creationParams) {
        // 创建原生 View（例如一个自定义的布局）
//        view = new View(context);
//        view.setBackgroundColor(0xFFFF0000); // 示例：红色背景

        view = LayoutInflater.from(context).inflate(R.layout.activity_unified_native_ad
                , null);
        container = view.findViewById(R.id.main_root_container);
        adView = view.findViewById(R.id.ad_main_image_container);
        adLogoRl = view.findViewById(R.id.ad_logo_rl);
//        TextView adDownInfoTv = itemView.findViewById(R.id.ad_download_info_tv);
//        FrameLayout adShakeViewFl = itemView.findViewById(R.id.ad_shake_view_fl);
        actionRl = view.findViewById(R.id.ad_action_rl);
        titleTv = view.findViewById(R.id.ad_title);
        descTv = view.findViewById(R.id.ad_desc);

        ad_action_rl_iconUrl = view.findViewById(R.id.ad_action_rl_iconUrl);
        ad_logo_rl_all = view.findViewById(R.id.ad_logo_rl_all);
        cardView = view.findViewById(R.id.ad_main_image_container_banner);
        iconCloseIv = view.findViewById(R.id.imv_close_write);
        iconCloseIv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                channel.invokeMethod("nativeAdOnTapClose", null);
            }
        });

        Log.e("TAG", "MyAndroidBannerView: =========" );
        loadAd((String) creationParams.get("avrId"),  context);
        // 将 View 保存到管理器
//        ViewManager.saveView(viewId, view);
    }




    @Nullable
    @Override
    public View getView() {
        return view;
    }

    @Override
    public void dispose() {

    }


    public void loadAd(String avrId, Context context) {

        AMPSRequestParameters parameter = new AMPSRequestParameters.Builder()
                .setSpaceId(avrId)
                .setWidth(AMPSScreenUtil.getScreenWidth(context))
                .setHeight(100)
                .setTimeOut(5000)
                .setAdCount(1)
                .build();

        mAMPSUnifiedNativeAd = new AMPSUnifiedNativeAd(context, parameter,
                new AMPSUnifiedNativeLoadEventListener() {
                    @Override
                    public void onAmpsAdLoad(List<AMPSUnifiedNativeItem> list) {
                        Toast.makeText(context, "广告加载成功", Toast.LENGTH_SHORT).show();
                        Log.e(AMPSConstants.AMPS_LOG_TAG, "TAG" + " onAmpsAdLoad==="+list.size());
                        cardView.setVisibility(View.GONE);
                        nativeItems = list;

                        AMPSUnifiedNativeItem unifiedNativeItem = null;
                        if (nativeItems != null && nativeItems.size() > 0) {
                            //每次取第0个位置的广告对象
                            unifiedNativeItem = nativeItems.get(0);
                            //从列表中移除广告对象
                            nativeItems.remove(0);
                        }
                        if (unifiedNativeItem == null) {
                            return;
                        }
                        useNativeItems.add(unifiedNativeItem);
                        showUnified(unifiedNativeItem, context);
                    }

                    @Override
                    public void onAmpsAdFailed(AMPSError ampsError) {
                        Toast.makeText(context, "广告加载失败，错误码："+ampsError.getCode()+"，错误信息："+ampsError.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(AMPSConstants.AMPS_LOG_TAG,
                                "TAG" + " onAdError code:" + ampsError.getCode() + ";" +
                                        " message:" + ampsError.getMessage());
                        cardView.setVisibility(View.VISIBLE);
                    }
                });

        mAMPSUnifiedNativeAd.loadAd();
    }


    private void showUnified(AMPSUnifiedNativeItem unifiedItem,Context context) {
        if (unifiedItem == null) {
            return;
        }
        if (!unifiedItem.isValid()) {
            return;
        }
        if (unifiedItem.isExpressAd()) {
            //渲染模板广告
            Log.e("TAG", "这是模板广告onAmpsAdLoad: express ad" );

//            renderNativeExpressAd(unifiedItem);
        } else {
            Log.e("TAG", "这是自渲染广告onAmpsAdLoad: express ad" );
            //自渲染广告
            renderUnifiedNativeAd(unifiedItem, context);
        }
    }

    //渲染自选染广告
    private void renderUnifiedNativeAd(AMPSUnifiedNativeItem unifiedItem, Context context) {
        //获取广告类型
        AMPSUnifiedPattern pattern = unifiedItem.getAdPattern();
        if (AMPSUnifiedPattern.AD_PATTERN_UNKNOWN.equals(pattern)) {
            return;
        }
        Log.e(AMPSConstants.AMPS_LOG_TAG, "TAG" + "show unified " + pattern);
        View itemView = null;
        if (AMPSUnifiedPattern.AD_PATTERN_TEXT_IMAGE.equals(pattern)) {
            //渲染图文广告
//            itemView = inflateImageText(unifiedItem);
            Log.e("TAG", "渲染图文广告onAmpsAdLoad: ");
            inflateImageText2(unifiedItem, context);
        } else if (AMPSUnifiedPattern.AD_PATTERN_3_IMAGES.equals(pattern)) {
            //渲染组图广告
            Log.e("TAG", "这是组图广告onAmpsAdLoad: ");
//            itemView = inflateGroupImage(unifiedItem);
            inflateImageText2(unifiedItem, context);
        } else if (AMPSUnifiedPattern.AD_PATTERN_VIDEO.equals(pattern)) {
            //渲染视频广告
//            itemView = inflateVideoView(unifiedItem);
            inflateImageText2(unifiedItem, context);
            Log.e("TAG", "renderUnifiedNativeAd: 这是视频广告");
        }
//        containerFl.removeAllViews();
//        containerFl.addView(itemView);
    }

    private void inflateImageText2(AMPSUnifiedNativeItem unifiedItem, Context context) {

        List<View> clickViews = new ArrayList<>();
        clickViews.add(adView);
        List<View> actionViews = new ArrayList<>();
        actionViews.add(actionRl);

        //渲染标题
        String adTitle = unifiedItem.getTitle();
        if (!TextUtils.isEmpty(adTitle)) {
            clickViews.add(titleTv);
            titleTv.setText(adTitle);
        }

        //渲染描述
        String adDesc = unifiedItem.getDesc();
        if (!TextUtils.isEmpty(adDesc)) {
            clickViews.add(descTv);
            descTv.setText(adDesc);
        }


        //渲染角标
        renderAdLogo(unifiedItem, adLogoRl, context);
        //渲染操作按钮,需支持文本和图片两种情况
        renderActionButton(unifiedItem, actionRl,context);
        //渲染广告类APP信息Icon
        renderAdAppIcon(unifiedItem, ad_action_rl_iconUrl, context);
        //渲染下载类广告六要素信息
//        renderDownloadInfo(unifiedItem, adDownInfoTv);
//        //渲染渠道摇一摇交互view
//        renderOptimizeView(unifiedItem, adShakeViewFl);
        //设置广告交互监听
//        setNativeAdEventListener(unifiedItem);
        //渲染图片
        if (unifiedItem.isViewObject()) {
            AMPSUnifiedView ampsUnifiedView = unifiedItem.getMainImageView();
            if (ampsUnifiedView != null) {
                View imageView = ampsUnifiedView.getView();
                if (imageView != null) {
                    adView.addView(imageView,
                            new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT,
                                    FrameLayout.LayoutParams.WRAP_CONTENT));
                }
            }
        } else {
            Log.e(AMPSConstants.AMPS_LOG_TAG,
                    "TAG" + "main image url " + unifiedItem.getMainImageUrl());
            ImageView imageView = new ImageView(context);
            imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
            Glide.with(context).load(unifiedItem.getMainImageUrl()).into(imageView);
            adView.addView(imageView,
                    new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT,
                            FrameLayout.LayoutParams.MATCH_PARENT));
        }

        unifiedItem.bindAdToRootContainer((Activity) context, container, clickViews, actionViews);

    }

    private void renderAdAppIcon(AMPSUnifiedNativeItem unifiedItem, RelativeLayout adActionRlIconUrl, Context context) {
        String adSourceIconUrl = unifiedItem.getIconUrl();
        Log.e("TAG", "加载广告来源Icon: " );
        if (!TextUtils.isEmpty(adSourceIconUrl)) {
            Log.e("TAG", "renderAdAppIcon: " + adSourceIconUrl );
            ImageView adSourceIcon = new ImageView(context);
            //设置圆形图片
            adSourceIcon.setScaleType(ImageView.ScaleType.CENTER_CROP);
            Glide.with(context).load(adSourceIconUrl).into(adSourceIcon);
            ad_action_rl_iconUrl.addView(adSourceIcon,
                    new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                            RelativeLayout.LayoutParams.WRAP_CONTENT));
        }
    }

    /**
     * 渲染广告角标
     *
     * @param unifiedItem
     * @param adLogoRl
     */
    private void renderAdLogo(AMPSUnifiedNativeItem unifiedItem, RelativeLayout adLogoRl, Context context) {
        String adSourceLogoUrl = unifiedItem.getAdSourceLogoUrl();
        if (TextUtils.isEmpty(adSourceLogoUrl)) {
            View adSourceLogo = unifiedItem.getAdSourceLogo();
            if (adSourceLogo != null) {
                Log.e("TAG", "renderAdLogo: adSourceLogo" );
                adLogoRl.addView(adSourceLogo,
                        new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                                RelativeLayout.LayoutParams.WRAP_CONTENT));
                ad_logo_rl_all.setVisibility(View.VISIBLE);
            }
        } else {
            Log.e(AMPSConstants.AMPS_LOG_TAG,
                    "TAG" + " adSourceLogoUrl: " + adSourceLogoUrl);
            ImageView logoIv = new ImageView(context);
            Glide.with(context).load(adSourceLogoUrl).into(logoIv);
            adLogoRl.addView(logoIv,
                    new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                            RelativeLayout.LayoutParams.WRAP_CONTENT));
            ad_logo_rl_all.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 渲染操作按钮
     *
     * @param unifiedItem
     * @param actionRl
     */
    private void renderActionButton(AMPSUnifiedNativeItem unifiedItem, RelativeLayout actionRl, Context context) {
        String actionButtonText = unifiedItem.getActionButtonText();
        Log.e("TAG", "renderActionButton: 加载操作按钮: " );
        if (!TextUtils.isEmpty(actionButtonText)) {
            Log.e("TAG", "renderActionButton: actionButtonText:不为空 " );
            if (actionButtonText.startsWith("http")) {
                Log.e(AMPSConstants.AMPS_LOG_TAG,
                        "TAG" + " actionButtonText: " + actionButtonText);
                ImageView actionIv = new ImageView(context);
                Glide.with(context).load(actionButtonText).into(actionIv);
                actionRl.addView(actionIv,
                        new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                                RelativeLayout.LayoutParams.MATCH_PARENT));
            } else {
                Log.e("TAG", "renderActionButton: 不是http链接,渲染文本按钮: " );
                TextView textView = new TextView(context);
                textView.setText(actionButtonText);
                textView.setTextColor(Color.parseColor("#FF333333"));
//                textView.setPadding(20, 10, 20, 10);
                //设置圆角背景
                textView.setBackgroundResource(R.drawable.bg_rounded_corner);
//                textView.setBackgroundColor(Color.parseColor("#FF03DAC5"));
                textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
                textView.setGravity(Gravity.CENTER);
                actionRl.addView(textView,
                        new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                                RelativeLayout.LayoutParams.MATCH_PARENT));
            }
        }
    }

}

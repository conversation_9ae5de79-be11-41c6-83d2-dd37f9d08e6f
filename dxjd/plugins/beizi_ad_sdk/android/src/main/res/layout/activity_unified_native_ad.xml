<?xml version="1.0" encoding="utf-8"?>
<xyz.adscope.amps.ad.unified.view.AMPSUnifiedRootContainer xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_root_container"
    android:layout_width="match_parent"
    android:layout_height="130dp"
    tools:context=".UnifiedNativeAdActivity">


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/ad_main_image_container"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="130dp"
                android:layout_weight="1"
                android:background="@drawable/gradient_background">

                <TextView
                    android:id="@+id/ad_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="加载中..."
                    android:textColor="#FFFFFF"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.067"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.021" />

                <TextView
                    android:id="@+id/ad_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/ad_title"
                    android:layout_marginStart="10dp"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text="加载中..."
                    android:textColor="#FFFFFF"
                    android:textSize="11sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.064"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ad_title"
                    app:layout_constraintVertical_bias="0.094" />

                <RelativeLayout
                    android:id="@+id/ad_action_rl_iconUrl"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="5dp"
                    android:layout_marginBottom="30dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.06"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.94">

                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/ad_logo_rl_all"
                    android:layout_width="35dp"
                    android:layout_height="14dp"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginBottom="30dp"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.446"
                    app:layout_constraintStart_toEndOf="@+id/ad_action_rl"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.943">

                                        <RelativeLayout
                                            android:id="@+id/ad_logo_rl"
                                            android:layout_width="0dp"
                                            android:layout_height="match_parent"
                                            android:layout_gravity="center"
                                            android:layout_weight="1" />

                    <!--                    <TextView-->
                    <!--                        android:id="@+id/textView"-->
                    <!--                        android:layout_width="wrap_content"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_gravity="center"-->
                    <!--                        android:layout_weight="1"-->
                    <!--                        android:text="广告"-->
                    <!--                        android:textSize="8sp"-->
                    <!--                        android:textStyle="bold" />-->
                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/ad_action_rl"
                    android:layout_width="74dp"
                    android:layout_height="20dp"
                    android:layout_marginBottom="30dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.154"
                    app:layout_constraintStart_toEndOf="@+id/ad_action_rl_iconUrl"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.94" />


            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <ImageView
            android:layout_gravity="end"
            android:layout_marginEnd="5dp"
            android:layout_marginTop="5dp"
            android:id="@+id/imv_close_write"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/icon_close_write" />
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:visibility="gone"
        android:id="@+id/ad_main_image_container_banner"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <ImageView
            android:id="@+id/imageView2"
            android:layout_width="match_parent"
            android:scaleType="fitXY"
            android:layout_height="match_parent"
            android:src="@drawable/dxjd_home_banner_default" />
    </androidx.cardview.widget.CardView>

</xyz.adscope.amps.ad.unified.view.AMPSUnifiedRootContainer>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/splash_ll"
    android:background="@drawable/img_qidong_zuoti"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_weight="1"
        android:id="@+id/ads_rl"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@android:color/transparent">

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/splash_rl"
        android:background="#ffffff"
        android:layout_width="match_parent"
        android:layout_height="175dp">

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="230dp"
            android:layout_height="60dp"
            android:layout_centerInParent="true"
            android:src="@drawable/splash_bg_logo" />
    </RelativeLayout>

    <!--    <ImageView-->
<!--        android:id="@+id/imageAvater"-->
<!--        android:layout_width="40dp"-->
<!--        android:layout_height="40dp"-->
<!--        android:scaleType="fitXY"-->
<!--        android:src="@drawable/adscope_app_icon" />-->

<!--    <TextView-->
<!--        android:id="@+id/jixushiyong"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_alignTop="@id/imageAvater"-->
<!--        android:layout_toRightOf="@+id/imageAvater"-->
<!--        android:text="我是标题" />-->

<!--    <TextView-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_alignBottom="@id/imageAvater"-->
<!--        android:layout_toRightOf="@+id/imageAvater"-->
<!--        android:text="AdScope" />-->


</LinearLayout>
import Flutter
import UIKit
import AMPSAdSDK

public class BeiziAdSdkPlugin: NSObject, FlutterPlugin {
    private var channel: FlutterMethodChannel?
    private var splash: AMPSSplashAd?
    private var interstitialAd: AMPSInterstitialAd?
    private var rewardedVideoAd: AMPSRewardedVideoAd?
    private var nativeManager: AMPSUnifiedNativeManager?
    private var registrar: FlutterPluginRegistrar?
    static var flutterChannel: FlutterMethodChannel?
    // Banner广告相关
    private var bannerViewFactories: [FlutterPlatformViewFactory] = []
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "xw.dxjk.beizi_ad", binaryMessenger: registrar.messenger())
        let instance = BeiziAdSdkPlugin()
        instance.channel = channel
        BeiziAdSdkPlugin.flutterChannel = channel
        instance.registrar = registrar
        registrar.addMethodCallDelegate(instance, channel: channel)
        
        // 注册banner视图工厂
        instance.registerBannerViewFactories()
    }
    
    // 注册banner视图工厂
    private func registerBannerViewFactories() {
        guard let registrar = registrar else { return }
        let factory = BeiziNativeViewFactory(messenger: registrar.messenger(), plugin: self)
        bannerViewFactories.append(factory)
        registrar.register(factory, withId: "beizi_banner_view")
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        let arguments = call.arguments as? [String: Any] ?? [:]
        
        switch call.method {
        case "getPlatformVersion":
            result("iOS " + UIDevice.current.systemVersion)
        case "cjSdkSetup":
            if let configId = arguments["configId"] as? String {
                initBeizi(appId: configId)
                result("initBeiAd success")
            } else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "configId is required", details: nil))
            }
        case "cjLoadAndShowSplashMethod":
            if let advertId = arguments["advertId"] as? String {
                setSplash(splashId: advertId)
                loadOrShowSplashAd()
                result("showSplashAd success")
            } else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "advertId is required", details: nil))
            }
        case "cjPreLoadSplashMethod":
            if let advertId = arguments["advertId"] as? String {
                setSplash(splashId: advertId)
                preloadSplashAd()
                result("preloadSplashAd success")
            } else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "advertId is required", details: nil))
            }
        case "cjLoadAndShowInterstitialAdMethod":
            if let advertId = arguments["advertId"] as? String {
                setInterstitialAd(interstitialID: advertId)
                loadInterstitialAd()
                result("showInsetAd success")
            } else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "advertId is required", details: nil))
            }
        case "cjLoadAndShowRewardVideoMethod":
            if let advertId = arguments["advertId"] as? String {
                setRewardedVideoAd(rewardedVideoAdID: advertId)
                loadRewardedVideoAdAd()
                result("showRewardAd success")
            } else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "advertId is required", details: nil))
            }
        case "cjLoadAndShowBannerMethod":
            if let advertId = arguments["advertId"] as? String {
                setNativeManager(spaceId: advertId)
                result("showBannerAd success")
            } else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "advertId is required", details: nil))
            }
        case "cjRefreshAndShowBannerMethod":
            loadNativeAd()
            result("refreshBannerAd success")
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    // MARK: - 初始化广告SDK
    private func initBeizi(appId: String) {
        print("初始化广告SDK appId: \(appId)")
        // 开屏使用启动图
        AMPSAdSDKManager.sharedInstance().showLaunchImage = true
        // 使用传入的图片作为开屏的启动图背景
        if let image = UIImage(named: "img_qidong_zuoti") {
            AMPSAdSDKManager.sharedInstance().launchImage = image
        }
        AMPSAdSDKManager.sharedInstance().startAsync(withAppId: appId) { [weak self] statusResult in
            print("初始化广告 状态 \(statusResult)")
            if statusResult.rawValue == 2 {
                self?.channel?.invokeMethod("setupSuccess", arguments: "初始化成功")
            } else {
                self?.channel?.invokeMethod("setupFailed", arguments: "初始化失败")
            }
        }
    }
    
    // MARK: - 开屏广告
    private func setSplash(splashId: String) {
        if(splash != nil){
            return
        }
        guard let rootViewController = UIApplication.shared.keyWindow?.rootViewController else {
            return
        }
        
        let cfg = AMPSAdConfiguration()
        let screenWidth = rootViewController.view.frame.size.width
        let screenHeight = rootViewController.view.frame.size.height
        let containerView = UIView(frame: CGRect(x: 0, y: 0, width: screenWidth, height: screenHeight * 0.19))
        containerView.backgroundColor = .white
        containerView.translatesAutoresizingMaskIntoConstraints = false
        
        let logoView = UIImageView(frame: CGRect(x: (screenWidth - 230)/2, y: (screenHeight * 0.19-60)/2, width: 230, height: 60))
        logoView.image = UIImage(named: "logo")
        containerView.addSubview(logoView)
        cfg.bottomView = containerView
        
        splash = AMPSSplashAd(spaceId: splashId, adConfiguration: cfg)
        splash?.delegate = self
    }
    
    private func loadOrShowSplashAd() {
        guard let splash = splash else { return }
        
        if splash.isReadyAd() {
            splash.showSplashView(in: UIApplication.shared.keyWindow!)
        } else {
            splash.load()
        }
        
        for info in splash.failAdInfoList {
            print("失败渠道：\(info)")
        }
    }
    
    private func preloadSplashAd(){
        splash?.preloadAd()
    }
    
    // MARK: - 插屏广告
    private func setInterstitialAd(interstitialID: String) {
        if(interstitialAd != nil){
           return
        }
        interstitialAd = AMPSInterstitialAd(spaceId: interstitialID, adConfiguration: AMPSAdConfiguration())
        interstitialAd?.delegate = self
    }
    
    private func loadInterstitialAd() {
        interstitialAd?.load()
    }
    
    // MARK: - 激励视频广告
    private func setRewardedVideoAd(rewardedVideoAdID: String) {
        if(rewardedVideoAd != nil){
            return
        }
        rewardedVideoAd = AMPSRewardedVideoAd(spaceId: rewardedVideoAdID, adConfiguration: AMPSAdConfiguration())
        rewardedVideoAd?.delegate = self
    }
    
    private func loadRewardedVideoAdAd() {
        rewardedVideoAd?.load()
    }
    
    // MARK: - 信息流广告
    private func setNativeManager(spaceId: String) {
        let cfg = AMPSAdConfiguration()
        cfg.adSize = CGSize(width: 500, height: 200)
        cfg.adCount = 1
        nativeManager = AMPSUnifiedNativeManager(spaceId: spaceId, adConfiguration: cfg)
        nativeManager?.delegate = self
    }
    
    public func loadNativeAd() {
        nativeManager?.load()
    }
}

// MARK: - AMPSSplashAdDelegate
extension BeiziAdSdkPlugin: AMPSSplashAdDelegate {
    public func ampsSplashAdLoadSuccess(_ splashAd: AMPSSplashAd) {
        if splashAd.isReadyAd() {
            splashAd.showSplashView(in: UIApplication.shared.keyWindow!)
        }
        channel?.invokeMethod("splashAdLoadSuccess", arguments: "闪屏页加载成功")
    }
    
    public func ampsSplashAdLoadFail(_ splashAd: AMPSSplashAd, error: Error?) {
        channel?.invokeMethod("splashAdLoadFailed", arguments: "闪屏页初始化失败")
    }
    
    public func ampsSplashAdDidShow(_ splashAd: AMPSSplashAd) {
        channel?.invokeMethod("splashAdOnShow", arguments: "闪屏页已经展示")
    }
    
    public func ampsSplashAdExposured(_ splashAd: AMPSSplashAd) {
        // 曝光回调
    }
    
    public func ampsSplashAdDidClick(_ splashAd: AMPSSplashAd) {
        // 点击回调
    }
    
    public func ampsSplashAdDidClose(_ splashAd: AMPSSplashAd) {
        channel?.invokeMethod("splashAdOnClose", arguments: "闪屏页已经关闭")
        splashAd.remove()
    }
}

// MARK: - AMPSInterstitialAdDelegate
extension BeiziAdSdkPlugin: AMPSInterstitialAdDelegate {
    public func ampsInterstitialAdLoadSuccess(_ interstitialAd: AMPSInterstitialAd) {
        guard let rootViewController = UIApplication.shared.keyWindow?.rootViewController else { return }
        interstitialAd.show(withRootViewController: rootViewController)
        channel?.invokeMethod("interstitialAdLoadSuccess", arguments: "插屏广告加载成功")
    }
    
    public func ampsInterstitialAdLoadFail(_ interstitialAd: AMPSInterstitialAd, error: Error?) {
        channel?.invokeMethod("interstitialAdLoadFailed", arguments: "插屏广告加载失败")
    }
    
    public func ampsInterstitialAdShowFail(_ interstitialAd: AMPSInterstitialAd, error: Error?) {
        channel?.invokeMethod("interstitialAdShowFailed", arguments: "插屏广告显示失败")
    }
    
    public func ampsInterstitialAdDidShow(_ interstitialAd: AMPSInterstitialAd) {
        channel?.invokeMethod("interstitialAdOnShow", arguments: "插屏广告已经显示")
    }
    
    public func ampsInterstitialAdDidClick(_ interstitialAd: AMPSInterstitialAd) {
        // 点击回调
    }
    
    public func ampsInterstitialAdDidClose(_ interstitialAd: AMPSInterstitialAd) {
        channel?.invokeMethod("interstitialAdOnClose", arguments: "插屏广告已经关闭")
        interstitialAd.remove()
    }
}

// MARK: - AMPSRewardedVideoAdDelegate
extension BeiziAdSdkPlugin: AMPSRewardedVideoAdDelegate {
    public func ampsRewardedVideoAdLoadSuccess(_ rewardedVideoAd: AMPSRewardedVideoAd) {
        guard let rootViewController = UIApplication.shared.keyWindow?.rootViewController else { return }
        rewardedVideoAd.show(withRootViewController: rootViewController)
        channel?.invokeMethod("rewardVideoAdLoadSuccess", arguments: "激励视频加载成功")
    }
    
    public func ampsRewardedVideoAdLoadFail(_ rewardedVideoAd: AMPSRewardedVideoAd, error: Error?) {
        channel?.invokeMethod("rewardVideoAdLoadFailed", arguments: "激励视频加载失败")
    }
    
    public func ampsRewardedVideoAdDidShow(_ rewardedVideoAd: AMPSRewardedVideoAd) {
        channel?.invokeMethod("rewardVideoAdOnShow", arguments: "激励视频已经显示")
    }
    
    public func ampsRewardedVideoAdDidClick(_ rewardedVideoAd: AMPSRewardedVideoAd) {
        // 点击回调
    }
    
    public func ampsRewardedVideoAdDidClose(_ rewardedVideoAd: AMPSRewardedVideoAd) {
        channel?.invokeMethod("rewardVideoAdOnClose", arguments: "激励视频已经关闭")
        rewardedVideoAd.remove()
    }
    
    public func ampsRewardedVideoAdDidPlayFinish(_ rewardedVideoAd: AMPSRewardedVideoAd) {
        // 播放完成回调
    }
    
    public func ampsRewardedVideoAdDidRewardEffective(_ rewardedVideoAd: AMPSRewardedVideoAd) {
        channel?.invokeMethod("rewardVideoAdBeRewarded", arguments: "激励视频达成奖励")
    }
}

// MARK: - AMPSUnifiedNativeManagerDelegate
extension BeiziAdSdkPlugin: AMPSUnifiedNativeManagerDelegate {
    public func ampsNativeAdLoadSuccess(_ nativeAd: AMPSUnifiedNativeManager) {
        print("ampsNativeAdLoadSuccess:%ld", nativeAd.adArray.count)
        print("成功渠道：%@-----id---%@", nativeAd.successAdInfo.adapterClassName, nativeAd.successAdInfo.adapterSpaceId)
        for info in nativeAd.failAdInfoList {
            print("失败渠道：%@，原因：%@", info.adapterClassName, info.errorMsg)
        }
        if nativeAd.adArray.count == 0 {
            return
        }
        let channelName = convertSDKNameToDisplayName(nativeAd.successAdInfo.adapterClassName)
        channel?.invokeMethod("nativeAdLoadSuccess", arguments: "信息流视频加载成功")
        let mNativeAd = nativeAd.adArray[0] as AMPSUnifiedNativeAd
        let view = AMPSUnifiedNativeView()
        view.delegate = self
        view.viewController = UIApplication.shared.keyWindow?.rootViewController
        view.refreshData(mNativeAd)
        NotificationCenter.default.post(name: NSNotification.Name(rawValue: "BeiziBannerAdDidLoadNotification"), object: UnifiedNative(bannerAd: nativeAd, view: view, name: channelName))
        print("价格：%ld--------%@", view.eCPM, view)
    }
    
    public func ampsNativeAdLoadFail(_ nativeAd: AMPSUnifiedNativeManager, error: Error?) {
        if let error = error {
            print("---------\(error)")
        }
        channel?.invokeMethod("nativeAdLoadFail", arguments: "信息流视频加载失败")
    }
    
    // SDK名称转换方法
    private func convertSDKNameToDisplayName(_ sdkName: String) -> String {
        let mapping = [
            "AMPSGDTUnifiedNativeAdapter": "优量汇",
            "AMPSBZUnifiedNativeAdapter": "倍孜",
            "AMPSCSJUnifiedNativeAdapter": "穿山甲",
            "AMPSKSUnifiedNativeAdapter": "快手",
            "AMPSASNPUnifiedNativeAdapter": "asnp",
            "AMPSGMUnifiedNativeAdapter": "gromore",
            "AMPSBDUnifiedNativeAdapter": "百度",
            "AMPSSGUnifiedNativeAdapter": "sigmob",
            "AMPSJDUnifiedNativeAdapter": "京东",
            "AMPSQMUnifiedNativeAdapter": "趣盟",
        ]
        
        return mapping[sdkName] ?? "其他"
    }
    
}

// MARK: - AMPSUnifiedNativeViewDelegate
extension BeiziAdSdkPlugin: AMPSUnifiedNativeViewDelegate {
    public func ampsNativeAdRenderSuccess(_ nativeView: AMPSUnifiedNativeView) {
        channel?.invokeMethod("nativeAdRenderSuccess", arguments: "信息流视频渲染成功")
    }
    
    public func ampsNativeAdRenderFail(_ nativeView: AMPSUnifiedNativeView, error: Error?) {
        channel?.invokeMethod("nativeAdRenderFail", arguments: "信息流视频渲染失败")
    }
    
    public func ampsNativeAdExposured(_ nativeView: AMPSUnifiedNativeView) {
        print("ampsNativeAdExposured")
    }
    
    public func ampsNativeAdDidClick(_ nativeView: AMPSUnifiedNativeView) {
        print("ampsNativeAdDidClick")
    }
    
    public func ampsNativeAdDidClose(_ nativeView: AMPSUnifiedNativeView) {
        nativeView.unregisterNativeAdDataObject()
        channel?.invokeMethod("nativeAdDidClose", arguments: "信息流视频关闭")
    }
}

// MARK: - 辅助类
// 自定义类用来传参
public class UnifiedNative {
    var bannerAd: AMPSUnifiedNativeManager
    var view: AMPSUnifiedNativeView
    var channelName: String
    
    init(bannerAd: AMPSUnifiedNativeManager, view: AMPSUnifiedNativeView, name: String) {
        self.bannerAd = bannerAd
        self.view = view
        self.channelName = name
    }
}

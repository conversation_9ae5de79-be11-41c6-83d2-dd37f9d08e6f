# Multi Camera Plugin

一个支持多种摄像头实现方案的Flutter插件，提供最大的设备兼容性和自动降级功能。

## 功能特性

### 🎯 核心功能
- **多种实现方案**: 支持CameraAwesome、Flutter Camera和原生实现
- **自动降级**: 当某个实现方案失败时，自动切换到备选方案
- **手动切换**: 允许用户手动选择摄像头实现方案
- **统一API**: 提供一致的API接口，屏蔽底层实现差异

### 📷 摄像头功能
- **拍照**: 高质量图片捕获
- **前后摄像头切换**: 支持前置和后置摄像头
- **闪光灯控制**: 支持关闭、开启、自动、常亮模式
- **预览窗口**: 可自定义大小、形状（圆形、正方形、矩形）
- **实时预览**: 流畅的摄像头预览

### 🔧 高级特性
- **事件监听**: 状态变化、错误、实现方案切换等事件
- **用户偏好**: 自动保存和恢复用户选择的实现方案
- **平台优化**: 针对iOS和Android的不同优化策略
- **错误处理**: 完善的错误处理和用户反馈

## 安装

在 `pubspec.yaml` 中添加依赖：

```yaml
dependencies:
  multi_camera_plugin: ^1.0.0
```

然后运行：

```bash
flutter pub get
```

## 快速开始

### 1. 基本使用

```dart
import 'package:multi_camera_plugin/multi_camera_plugin.dart';

class CameraPage extends StatefulWidget {
  @override
  _CameraPageState createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> {
  final MultiCameraPlugin _plugin = MultiCameraPlugin.instance;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    final config = CameraConfig(
      lensDirection: CameraLensDirection.back,
      enableAudio: false,
      imageQuality: 0.8,
      isCircular: false,
      isSquare: false,
    );

    await _plugin.initialize(config: config);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MultiCameraPreview(
        cameraConfig: CameraConfig(
          lensDirection: CameraLensDirection.back,
          enableAudio: false,
          imageQuality: 0.8,
          isCircular: false,
          isSquare: false,
        ),
        previewConfig: const CameraPreviewConfig(),
        onCameraReady: () {
          print('摄像头已就绪');
        },
        onError: (error) {
          print('摄像头错误: ${error.message}');
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          try {
            final result = await _plugin.takePicture();
            print('拍照成功: ${result.imagePath}');
          } catch (e) {
            print('拍照失败: $e');
          }
        },
        child: const Icon(Icons.camera),
      ),
    );
  }
}
```

### 2. 高级配置

```dart
// 自定义实现方案优先级
final priority = CameraImplementationPriority(
  iosPriority: [
    CameraImplementationType.native,
    CameraImplementationType.cameraAwesome,
    CameraImplementationType.flutterCamera,
  ],
  androidPriority: [
    CameraImplementationType.cameraAwesome,
    CameraImplementationType.flutterCamera,
    CameraImplementationType.native,
  ],
  enableAutoFallback: true,
  maxRetryCount: 3,
);

// 初始化时指定优先级和首选实现
await _plugin.initialize(
  config: config,
  priority: priority,
  preferredImplementation: CameraImplementationType.cameraAwesome,
);
```

### 3. 自定义预览窗口

```dart
// 圆形预览窗口
MultiCameraPreview(
  cameraConfig: config,
  previewConfig: CameraPreviewConfig.circular(
    size: 200,
    showBorder: true,
    borderColor: 0xFF2196F3,
    borderWidth: 2.0,
  ),
)

// 正方形预览窗口
MultiCameraPreview(
  cameraConfig: config,
  previewConfig: CameraPreviewConfig.square(
    size: 300,
    borderRadius: 16.0,
    showBorder: true,
  ),
)

// 矩形预览窗口
MultiCameraPreview(
  cameraConfig: config,
  previewConfig: CameraPreviewConfig.rectangle(
    width: 320,
    height: 240,
    borderRadius: 8.0,
  ),
)
```

### 4. 事件监听

```dart
// 监听状态变化
_plugin.statusStream.listen((status) {
  print('摄像头状态: $status');
});

// 监听错误
_plugin.errorStream.listen((error) {
  print('摄像头错误: ${error.message}');
});

// 监听事件
_plugin.eventStream.listen((event) {
  if (event is CameraImplementationSwitchedEvent) {
    print('实现方案已切换: ${event.oldImplementation} → ${event.newImplementation}');
    print('切换原因: ${event.reason}');
  } else if (event is CameraCaptureCompletedEvent) {
    print('拍照完成: ${event.result.imagePath}');
  }
});
```

## API 参考

### MultiCameraPlugin

主要的插件类，提供所有摄像头功能。

#### 方法

- `initialize()`: 初始化摄像头
- `takePicture()`: 拍照
- `switchCamera()`: 切换摄像头方向
- `setFlashMode()`: 设置闪光灯模式
- `switchImplementation()`: 手动切换实现方案
- `dispose()`: 释放资源

#### 属性

- `status`: 当前状态
- `currentImplementation`: 当前实现类型
- `availableImplementations`: 可用的实现方案
- `eventStream`: 事件流
- `statusStream`: 状态流
- `errorStream`: 错误流

### MultiCameraPreview

摄像头预览Widget，提供丰富的自定义选项。

#### 参数

- `cameraConfig`: 摄像头配置
- `previewConfig`: 预览配置
- `priority`: 实现方案优先级配置
- `preferredImplementation`: 首选实现方案
- `onCameraReady`: 摄像头准备就绪回调
- `onError`: 错误回调
- `onStatusChanged`: 状态变化回调
- `onImplementationSwitched`: 实现方案切换回调
- `showStatusIndicator`: 是否显示状态指示器
- `showImplementationIndicator`: 是否显示实现方案指示器
- `showErrorMessage`: 是否显示错误信息

## 实现方案对比

| 实现方案 | 优势 | 劣势 | 推荐场景 |
|---------|------|------|----------|
| CameraAwesome | 功能丰富、性能优秀 | 包体积较大 | 需要高级功能的应用 |
| Flutter Camera | 官方支持、稳定性好 | 功能相对简单 | 一般应用场景 |
| 原生实现 | 性能最佳、完全控制 | 开发复杂度高 | 对性能要求极高的场景 |

## 平台支持

- ✅ iOS 11.0+
- ✅ Android API 21+
- 🚧 macOS (开发中)
- ❌ Web (暂不支持)
- ❌ Windows (暂不支持)
- ❌ Linux (暂不支持)

## 许可证

MIT License


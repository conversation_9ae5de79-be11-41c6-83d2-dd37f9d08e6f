group = "com.dxjd.multi_camera_plugin"
version = "1.0-SNAPSHOT"

buildscript {
    ext.kotlin_version = "1.8.22"
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath("com.android.tools.build:gradle:8.1.0")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: "com.android.library"
apply plugin: "kotlin-android"

android {
    if (project.android.hasProperty("namespace")) {
        namespace = "com.dxjd.multi_camera_plugin"
    }

    compileSdk = 34

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    sourceSets {
        main.java.srcDirs += "src/main/kotlin"
        test.java.srcDirs += "src/test/kotlin"
    }

    defaultConfig {
        minSdk = 21
    }

    dependencies {
        // CameraX dependencies for modern camera functionality
        implementation "androidx.camera:camera-core:1.3.1"
        implementation "androidx.camera:camera-camera2:1.3.1"
        implementation "androidx.camera:camera-lifecycle:1.3.1"
        implementation "androidx.camera:camera-view:1.3.1"

        // Kotlin coroutines for async operations
        implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"
        implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3"

        // AndroidX lifecycle components
        implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
        implementation "androidx.lifecycle:lifecycle-common-java8:2.7.0"

        // Core AndroidX libraries
        implementation "androidx.core:core-ktx:1.12.0"
        implementation "androidx.concurrent:concurrent-futures-ktx:1.1.0"

        testImplementation("org.jetbrains.kotlin:kotlin-test")
        testImplementation("org.mockito:mockito-core:5.0.0")
    }

    testOptions {
        unitTests.all {
            useJUnitPlatform()

            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}

// Autogenerated from <PERSON><PERSON> (v22.7.4), do not edit directly.
// See also: https://pub.dev/packages/pigeon
@file:Suppress("UNCHECKED_CAST", "ArrayInDataClass")


import android.util.Log
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MessageCodec
import io.flutter.plugin.common.StandardMethodCodec
import io.flutter.plugin.common.StandardMessageCodec
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer

private fun wrapResult(result: Any?): List<Any?> {
  return listOf(result)
}

private fun wrapError(exception: Throwable): List<Any?> {
  return if (exception is MultiCameraFlutterError) {
    listOf(
      exception.code,
      exception.message,
      exception.details
    )
  } else {
    listOf(
      exception.javaClass.simpleName,
      exception.toString(),
      "Cause: " + exception.cause + ", Stacktrace: " + Log.getStackTraceString(exception)
    )
  }
}

private fun createConnectionError(channelName: String): MultiCameraFlutterError {
  return MultiCameraFlutterError("channel-error",  "Unable to establish connection on channel: '$channelName'.", "")}

/**
 * Error class for passing custom error details to Flutter via a thrown PlatformException.
 * @property code The error code.
 * @property message The error message.
 * @property details The error details. Must be a datatype supported by the api codec.
 */
class MultiCameraFlutterError (
  val code: String,
  override val message: String? = null,
  val details: Any? = null
) : Throwable()

/** 摄像头实现类型枚举 */
enum class CameraImplementationType(val raw: Int) {
  /** CameraAwesome插件实现 */
  CAMERA_AWESOME(0),
  /** Flutter Camera插件实现 */
  FLUTTER_CAMERA(1),
  /** 原生实现 */
  NATIVE(2);

  companion object {
    fun ofRaw(raw: Int): CameraImplementationType? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/** 摄像头状态枚举 */
enum class CameraStatus(val raw: Int) {
  /** 未初始化 */
  UNINITIALIZED(0),
  /** 初始化中 */
  INITIALIZING(1),
  /** 已初始化 */
  INITIALIZED(2),
  /** 拍照中 */
  TAKING_PICTURE(3),
  /** 错误状态 */
  ERROR(4),
  /** 已释放 */
  DISPOSED(5);

  companion object {
    fun ofRaw(raw: Int): CameraStatus? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/** 摄像头方向枚举 */
enum class CameraLensDirection(val raw: Int) {
  /** 前置摄像头 */
  FRONT(0),
  /** 后置摄像头 */
  BACK(1),
  /** 外部摄像头 */
  EXTERNAL(2);

  companion object {
    fun ofRaw(raw: Int): CameraLensDirection? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/** 闪光灯模式枚举 */
enum class FlashMode(val raw: Int) {
  /** 关闭 */
  OFF(0),
  /** 开启 */
  ON(1),
  /** 自动 */
  AUTO(2),
  /** 常亮 */
  TORCH(3);

  companion object {
    fun ofRaw(raw: Int): FlashMode? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/**
 * 摄像头配置
 *
 * Generated class from Pigeon that represents data sent in messages.
 */
data class CameraConfig (
  /** 摄像头方向 */
  val lensDirection: CameraLensDirection,
  /** 是否启用音频 */
  val enableAudio: Boolean,
  /** 图片质量 (0.0-1.0) */
  val imageQuality: Double,
  /** 摄像头窗口宽度 */
  val width: Double? = null,
  /** 摄像头窗口高度 */
  val height: Double? = null,
  /** 是否为圆形窗口 */
  val isCircular: Boolean,
  /** 是否为正方形窗口 */
  val isSquare: Boolean
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): CameraConfig {
      val lensDirection = pigeonVar_list[0] as CameraLensDirection
      val enableAudio = pigeonVar_list[1] as Boolean
      val imageQuality = pigeonVar_list[2] as Double
      val width = pigeonVar_list[3] as Double?
      val height = pigeonVar_list[4] as Double?
      val isCircular = pigeonVar_list[5] as Boolean
      val isSquare = pigeonVar_list[6] as Boolean
      return CameraConfig(lensDirection, enableAudio, imageQuality, width, height, isCircular, isSquare)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      lensDirection,
      enableAudio,
      imageQuality,
      width,
      height,
      isCircular,
      isSquare,
    )
  }
}

/**
 * 拍照结果
 *
 * Generated class from Pigeon that represents data sent in messages.
 */
data class CaptureResult (
  /** 图片文件路径 */
  val imagePath: String,
  /** 拍照时间戳 */
  val timestamp: Long,
  /** 图片大小（字节） */
  val fileSize: Long
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): CaptureResult {
      val imagePath = pigeonVar_list[0] as String
      val timestamp = pigeonVar_list[1] as Long
      val fileSize = pigeonVar_list[2] as Long
      return CaptureResult(imagePath, timestamp, fileSize)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      imagePath,
      timestamp,
      fileSize,
    )
  }
}

/**
 * 摄像头错误信息
 *
 * Generated class from Pigeon that represents data sent in messages.
 */
data class CameraError (
  /** 错误代码 */
  val code: String,
  /** 错误消息 */
  val message: String,
  /** 错误详情 */
  val details: String? = null
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): CameraError {
      val code = pigeonVar_list[0] as String
      val message = pigeonVar_list[1] as String
      val details = pigeonVar_list[2] as String?
      return CameraError(code, message, details)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      code,
      message,
      details,
    )
  }
}

/**
 * 摄像头能力信息
 *
 * Generated class from Pigeon that represents data sent in messages.
 */
data class CameraCapabilities (
  /** 支持的实现类型列表 */
  val supportedImplementations: List<CameraImplementationType>,
  /** 是否支持前置摄像头 */
  val hasFrontCamera: Boolean,
  /** 是否支持后置摄像头 */
  val hasBackCamera: Boolean,
  /** 是否支持闪光灯 */
  val hasFlash: Boolean,
  /** 是否支持变焦 */
  val hasZoom: Boolean
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): CameraCapabilities {
      val supportedImplementations = pigeonVar_list[0] as List<CameraImplementationType>
      val hasFrontCamera = pigeonVar_list[1] as Boolean
      val hasBackCamera = pigeonVar_list[2] as Boolean
      val hasFlash = pigeonVar_list[3] as Boolean
      val hasZoom = pigeonVar_list[4] as Boolean
      return CameraCapabilities(supportedImplementations, hasFrontCamera, hasBackCamera, hasFlash, hasZoom)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      supportedImplementations,
      hasFrontCamera,
      hasBackCamera,
      hasFlash,
      hasZoom,
    )
  }
}
private open class CameraGeneratedApisPigeonCodec : StandardMessageCodec() {
  override fun readValueOfType(type: Byte, buffer: ByteBuffer): Any? {
    return when (type) {
      129.toByte() -> {
        return (readValue(buffer) as Long?)?.let {
          CameraImplementationType.ofRaw(it.toInt())
        }
      }
      130.toByte() -> {
        return (readValue(buffer) as Long?)?.let {
          CameraStatus.ofRaw(it.toInt())
        }
      }
      131.toByte() -> {
        return (readValue(buffer) as Long?)?.let {
          CameraLensDirection.ofRaw(it.toInt())
        }
      }
      132.toByte() -> {
        return (readValue(buffer) as Long?)?.let {
          FlashMode.ofRaw(it.toInt())
        }
      }
      133.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          CameraConfig.fromList(it)
        }
      }
      134.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          CaptureResult.fromList(it)
        }
      }
      135.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          CameraError.fromList(it)
        }
      }
      136.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          CameraCapabilities.fromList(it)
        }
      }
      else -> super.readValueOfType(type, buffer)
    }
  }
  override fun writeValue(stream: ByteArrayOutputStream, value: Any?)   {
    when (value) {
      is CameraImplementationType -> {
        stream.write(129)
        writeValue(stream, value.raw)
      }
      is CameraStatus -> {
        stream.write(130)
        writeValue(stream, value.raw)
      }
      is CameraLensDirection -> {
        stream.write(131)
        writeValue(stream, value.raw)
      }
      is FlashMode -> {
        stream.write(132)
        writeValue(stream, value.raw)
      }
      is CameraConfig -> {
        stream.write(133)
        writeValue(stream, value.toList())
      }
      is CaptureResult -> {
        stream.write(134)
        writeValue(stream, value.toList())
      }
      is CameraError -> {
        stream.write(135)
        writeValue(stream, value.toList())
      }
      is CameraCapabilities -> {
        stream.write(136)
        writeValue(stream, value.toList())
      }
      else -> super.writeValue(stream, value)
    }
  }
}


/**
 * 主机端API接口（Flutter调用原生）
 *
 * Generated interface from Pigeon that represents a handler of messages from Flutter.
 */
interface MultiCameraHostApi {
  /** 获取摄像头能力信息 */
  fun getCameraCapabilities(callback: (Result<CameraCapabilities>) -> Unit)
  /** 初始化摄像头 */
  fun initializeCamera(config: CameraConfig, implementationType: CameraImplementationType, callback: (Result<Unit>) -> Unit)
  /** 切换摄像头实现方案 */
  fun switchImplementation(implementationType: CameraImplementationType, callback: (Result<Unit>) -> Unit)
  /** 切换摄像头方向（前置/后置） */
  fun switchCamera(direction: CameraLensDirection, callback: (Result<Unit>) -> Unit)
  /** 拍照 */
  fun takePicture(callback: (Result<CaptureResult>) -> Unit)
  /** 设置闪光灯模式 */
  fun setFlashMode(mode: FlashMode, callback: (Result<Unit>) -> Unit)
  /** 开始预览 */
  fun startPreview(callback: (Result<Unit>) -> Unit)
  /** 停止预览 */
  fun stopPreview(callback: (Result<Unit>) -> Unit)
  /** 释放摄像头资源 */
  fun dispose(callback: (Result<Unit>) -> Unit)
  /** 检查特定实现是否可用 */
  fun isImplementationAvailable(implementationType: CameraImplementationType, callback: (Result<Boolean>) -> Unit)

  companion object {
    /** The codec used by MultiCameraHostApi. */
    val codec: MessageCodec<Any?> by lazy {
      CameraGeneratedApisPigeonCodec()
    }
    /** Sets up an instance of `MultiCameraHostApi` to handle messages through the `binaryMessenger`. */
    @JvmOverloads
    fun setUp(binaryMessenger: BinaryMessenger, api: MultiCameraHostApi?, messageChannelSuffix: String = "") {
      val separatedMessageChannelSuffix = if (messageChannelSuffix.isNotEmpty()) ".$messageChannelSuffix" else ""
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.getCameraCapabilities$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            api.getCameraCapabilities{ result: Result<CameraCapabilities> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.initializeCamera$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val configArg = args[0] as CameraConfig
            val implementationTypeArg = args[1] as CameraImplementationType
            api.initializeCamera(configArg, implementationTypeArg) { result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.switchImplementation$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val implementationTypeArg = args[0] as CameraImplementationType
            api.switchImplementation(implementationTypeArg) { result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.switchCamera$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val directionArg = args[0] as CameraLensDirection
            api.switchCamera(directionArg) { result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.takePicture$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            api.takePicture{ result: Result<CaptureResult> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.setFlashMode$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val modeArg = args[0] as FlashMode
            api.setFlashMode(modeArg) { result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.startPreview$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            api.startPreview{ result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.stopPreview$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            api.stopPreview{ result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.dispose$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            api.dispose{ result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.isImplementationAvailable$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val implementationTypeArg = args[0] as CameraImplementationType
            api.isImplementationAvailable(implementationTypeArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }
}
/**
 * Flutter端API接口（原生调用Flutter）
 *
 * Generated class from Pigeon that represents Flutter messages that can be called from Kotlin.
 */
class MultiCameraFlutterApi(private val binaryMessenger: BinaryMessenger, private val messageChannelSuffix: String = "") {
  companion object {
    /** The codec used by MultiCameraFlutterApi. */
    val codec: MessageCodec<Any?> by lazy {
      CameraGeneratedApisPigeonCodec()
    }
  }
  /** 摄像头状态变化回调 */
  fun onCameraStatusChanged(statusArg: CameraStatus, callback: (Result<Unit>) -> Unit)
{
    val separatedMessageChannelSuffix = if (messageChannelSuffix.isNotEmpty()) ".$messageChannelSuffix" else ""
    val channelName = "dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onCameraStatusChanged$separatedMessageChannelSuffix"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(statusArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(MultiCameraFlutterError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(createConnectionError(channelName)))
      } 
    }
  }
  /** 摄像头错误回调 */
  fun onCameraError(errorArg: CameraError, callback: (Result<Unit>) -> Unit)
{
    val separatedMessageChannelSuffix = if (messageChannelSuffix.isNotEmpty()) ".$messageChannelSuffix" else ""
    val channelName = "dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onCameraError$separatedMessageChannelSuffix"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(errorArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(MultiCameraFlutterError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(createConnectionError(channelName)))
      } 
    }
  }
  /** 实现方案自动切换回调 */
  fun onImplementationSwitched(newImplementationArg: CameraImplementationType, reasonArg: String, callback: (Result<Unit>) -> Unit)
{
    val separatedMessageChannelSuffix = if (messageChannelSuffix.isNotEmpty()) ".$messageChannelSuffix" else ""
    val channelName = "dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onImplementationSwitched$separatedMessageChannelSuffix"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(newImplementationArg, reasonArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(MultiCameraFlutterError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(createConnectionError(channelName)))
      } 
    }
  }
}

package com.dxjd.multi_camera_plugin

import android.content.Context
import android.os.Environment
import androidx.annotation.NonNull
import androidx.camera.core.ImageCapture
import androidx.camera.core.CameraSelector
import androidx.lifecycle.LifecycleOwner
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.*
import java.io.File

/** MultiCameraPlugin */
class MultiCameraPlugin: FlutterPlugin, MethodCallHandler, ActivityAware, MultiCameraHostApi {
    private lateinit var channel: MethodChannel
    private var context: Context? = null
    private var lifecycleOwner: LifecycleOwner? = null
    private var nativeCameraManager: NativeCameraManager? = null
    private var flutterApi: MultiCameraFlutterApi? = null
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        context = flutterPluginBinding.applicationContext
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "multi_camera_plugin")
        channel.setMethodCallHandler(this)

        // 设置Pigeon API
        MultiCameraHostApi.setUp(flutterPluginBinding.binaryMessenger, this)
        flutterApi = MultiCameraFlutterApi(flutterPluginBinding.binaryMessenger)
    }

    override fun onAttachedToActivity(binding: ActivityPluginBinding) {
        lifecycleOwner = binding.activity as? LifecycleOwner
    }

    override fun onDetachedFromActivityForConfigChanges() {
        lifecycleOwner = null
    }

    override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
        lifecycleOwner = binding.activity as? LifecycleOwner
    }

    override fun onDetachedFromActivity() {
        lifecycleOwner = null
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "getPlatformVersion" -> {
                result.success("Android ${android.os.Build.VERSION.RELEASE}")
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
        MultiCameraHostApi.setUp(binding.binaryMessenger, null)
        coroutineScope.cancel()
        nativeCameraManager?.dispose()
    }

    // ========== Pigeon API 实现 ==========

    override fun getCameraCapabilities(callback: (Result<CameraCapabilities>) -> Unit) {
        coroutineScope.launch {
            try {
                val context = <EMAIL> ?: run {
                    callback(Result.failure(MultiCameraFlutterError("NO_CONTEXT", "Context not available", null)))
                    return@launch
                }

                val manager = getNativeCameraManager()
                val capabilities = CameraCapabilities(
                    supportedImplementations = listOf(CameraImplementationType.NATIVE),
                    hasFrontCamera = manager.hasFrontCamera(),
                    hasBackCamera = manager.hasBackCamera(),
                    hasFlash = manager.hasFlash(),
                    hasZoom = false // 暂不支持变焦
                )
                callback(Result.success(capabilities))
            } catch (e: Exception) {
                callback(Result.failure(MultiCameraFlutterError("GET_CAPABILITIES_FAILED", e.message ?: "Unknown error", null)))
            }
        }
    }

    override fun initializeCamera(
        config: CameraConfig,
        implementationType: CameraImplementationType,
        callback: (Result<Unit>) -> Unit
    ) {
        coroutineScope.launch {
            try {
                val manager = getNativeCameraManager()
                val lensFacing = when (config.lensDirection) {
                    CameraLensDirection.FRONT -> CameraSelector.LENS_FACING_FRONT
                    CameraLensDirection.BACK -> CameraSelector.LENS_FACING_BACK
                    else -> CameraSelector.LENS_FACING_BACK
                }

                manager.initialize(lensFacing, object : NativeCameraManager.CameraCallback {
                    override fun onInitialized() {
                        flutterApi?.onCameraStatusChanged(CameraStatus.INITIALIZED) {}
                        callback(Result.success(Unit))
                    }

                    override fun onError(error: String) {
                        val cameraError = CameraError("INITIALIZATION_FAILED", error, null)
                        flutterApi?.onCameraError(cameraError) {}
                        callback(Result.failure(MultiCameraFlutterError("INITIALIZATION_FAILED", error, null)))
                    }

                    override fun onPhotoTaken(imagePath: String, fileSize: Long) {
                        // 处理拍照完成
                    }
                })
            } catch (e: Exception) {
                callback(Result.failure(MultiCameraFlutterError("INITIALIZATION_FAILED", e.message ?: "Unknown error", null)))
            }
        }
    }

    override fun switchImplementation(
        implementationType: CameraImplementationType,
        callback: (Result<Unit>) -> Unit
    ) {
        // 原生实现只支持一种类型，直接返回成功
        callback(Result.success(Unit))
    }

    override fun switchCamera(
        direction: CameraLensDirection,
        callback: (Result<Unit>) -> Unit
    ) {
        coroutineScope.launch {
            try {
                val manager = getNativeCameraManager()
                manager.switchCamera()
                callback(Result.success(Unit))
            } catch (e: Exception) {
                callback(Result.failure(MultiCameraFlutterError("SWITCH_CAMERA_FAILED", e.message ?: "Unknown error", null)))
            }
        }
    }

    override fun takePicture(callback: (Result<CaptureResult>) -> Unit) {
        coroutineScope.launch {
            try {
                val context = <EMAIL> ?: run {
                    callback(Result.failure(MultiCameraFlutterError("NO_CONTEXT", "Context not available", null)))
                    return@launch
                }

                val manager = getNativeCameraManager()
                val outputDir = File(context.getExternalFilesDir(Environment.DIRECTORY_PICTURES), "multi_camera")
                if (!outputDir.exists()) {
                    outputDir.mkdirs()
                }

                // 设置拍照回调
                manager.callback = object : NativeCameraManager.CameraCallback {
                    override fun onInitialized() {}
                    override fun onError(error: String) {
                        callback(Result.failure(MultiCameraFlutterError("CAPTURE_FAILED", error, null)))
                    }
                    override fun onPhotoTaken(imagePath: String, fileSize: Long) {
                        val result = CaptureResult(
                            imagePath = imagePath,
                            timestamp = System.currentTimeMillis(),
                            fileSize = fileSize
                        )
                        callback(Result.success(result))
                    }
                }

                manager.takePicture(outputDir)
            } catch (e: Exception) {
                callback(Result.failure(MultiCameraFlutterError("CAPTURE_FAILED", e.message ?: "Unknown error", null)))
            }
        }
    }

    override fun setFlashMode(mode: FlashMode, callback: (Result<Unit>) -> Unit) {
        try {
            val manager = getNativeCameraManager()
            val flashMode = when (mode) {
                FlashMode.OFF -> ImageCapture.FLASH_MODE_OFF
                FlashMode.ON -> ImageCapture.FLASH_MODE_ON
                FlashMode.AUTO -> ImageCapture.FLASH_MODE_AUTO
                FlashMode.TORCH -> ImageCapture.FLASH_MODE_OFF // CameraX不直接支持torch模式
            }
            manager.setFlashMode(flashMode)
            callback(Result.success(Unit))
        } catch (e: Exception) {
            callback(Result.failure(MultiCameraFlutterError("SET_FLASH_MODE_FAILED", e.message ?: "Unknown error", null)))
        }
    }

    override fun startPreview(callback: (Result<Unit>) -> Unit) {
        // CameraX自动处理预览
        callback(Result.success(Unit))
    }

    override fun stopPreview(callback: (Result<Unit>) -> Unit) {
        // CameraX自动处理预览
        callback(Result.success(Unit))
    }

    override fun dispose(callback: (Result<Unit>) -> Unit) {
        nativeCameraManager?.dispose()
        nativeCameraManager = null
        callback(Result.success(Unit))
    }

    override fun isImplementationAvailable(
        implementationType: CameraImplementationType,
        callback: (Result<Boolean>) -> Unit
    ) {
        coroutineScope.launch {
            try {
                val manager = getNativeCameraManager()
                val isAvailable = manager.isAvailable()
                callback(Result.success(isAvailable))
            } catch (e: Exception) {
                callback(Result.success(false))
            }
        }
    }

    // ========== 私有方法 ==========

    private fun getNativeCameraManager(): NativeCameraManager {
        if (nativeCameraManager == null) {
            val context = this.context ?: throw IllegalStateException("Context not available")
            val lifecycleOwner = this.lifecycleOwner ?: throw IllegalStateException("LifecycleOwner not available")
            nativeCameraManager = NativeCameraManager(context, lifecycleOwner)
        }
        return nativeCameraManager!!
    }
}

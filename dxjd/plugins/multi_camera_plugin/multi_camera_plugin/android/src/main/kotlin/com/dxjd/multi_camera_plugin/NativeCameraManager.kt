package com.dxjd.multi_camera_plugin

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.ImageFormat
import android.hardware.camera2.*
import android.media.Image
import android.media.ImageReader
import android.os.Handler
import android.os.HandlerThread
import android.util.Log
import android.util.Size
import android.view.Surface
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.nio.ByteBuffer
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 原生摄像头管理器
 * 
 * 使用CameraX作为主要实现，Camera2作为降级方案
 * 提供最佳的兼容性和性能
 */
class NativeCameraManager(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner
) {
    companion object {
        private const val TAG = "NativeCameraManager"
        private const val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
    }

    // CameraX相关
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var preview: Preview? = null
    private var imageCapture: ImageCapture? = null
    private var cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()
    
    // 当前配置
    private var currentLensFacing = CameraSelector.LENS_FACING_BACK
    private var currentFlashMode = ImageCapture.FLASH_MODE_OFF
    private var isInitialized = false
    
    // 回调接口
    interface CameraCallback {
        fun onInitialized()
        fun onError(error: String)
        fun onPhotoTaken(imagePath: String, fileSize: Long)
    }
    
    private var callback: CameraCallback? = null

    /**
     * 检查摄像头是否可用
     */
    fun isAvailable(): Boolean {
        return try {
            val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
            val cameraIds = cameraManager.cameraIdList
            cameraIds.isNotEmpty() && hasPermissions()
        } catch (e: Exception) {
            Log.e(TAG, "检查摄像头可用性失败", e)
            false
        }
    }

    /**
     * 检查权限
     */
    private fun hasPermissions(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 初始化摄像头
     */
    suspend fun initialize(
        lensFacing: Int = CameraSelector.LENS_FACING_BACK,
        callback: CameraCallback? = null
    ) = withContext(Dispatchers.Main) {
        <EMAIL> = callback
        
        if (!hasPermissions()) {
            callback?.onError("缺少摄像头权限")
            return@withContext
        }

        try {
            currentLensFacing = lensFacing
            
            // 获取CameraProvider
            val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
            cameraProvider = cameraProviderFuture.get()
            
            setupCamera()
            isInitialized = true
            callback?.onInitialized()
            
        } catch (e: Exception) {
            Log.e(TAG, "初始化摄像头失败", e)
            callback?.onError("初始化摄像头失败: ${e.message}")
        }
    }

    /**
     * 设置摄像头
     */
    private fun setupCamera() {
        val cameraProvider = this.cameraProvider ?: return

        // 创建预览用例
        preview = Preview.Builder()
            .build()

        // 创建拍照用例
        imageCapture = ImageCapture.Builder()
            .setFlashMode(currentFlashMode)
            .build()

        // 选择摄像头
        val cameraSelector = CameraSelector.Builder()
            .requireLensFacing(currentLensFacing)
            .build()

        try {
            // 解绑之前的用例
            cameraProvider.unbindAll()

            // 绑定用例到生命周期
            camera = cameraProvider.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageCapture
            )

        } catch (e: Exception) {
            Log.e(TAG, "绑定摄像头用例失败", e)
            callback?.onError("绑定摄像头用例失败: ${e.message}")
        }
    }

    /**
     * 绑定预览到PreviewView
     */
    fun bindPreview(previewView: PreviewView) {
        preview?.setSurfaceProvider(previewView.surfaceProvider)
    }

    /**
     * 拍照
     */
    suspend fun takePicture(outputDirectory: File) = withContext(Dispatchers.IO) {
        val imageCapture = <EMAIL> ?: run {
            callback?.onError("摄像头未初始化")
            return@withContext
        }

        // 创建输出文件
        val name = SimpleDateFormat(FILENAME_FORMAT, Locale.US)
            .format(System.currentTimeMillis())
        val photoFile = File(outputDirectory, "$name.jpg")

        // 创建输出选项
        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

        // 拍照
        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageSavedCallback {
                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    val fileSize = photoFile.length()
                    callback?.onPhotoTaken(photoFile.absolutePath, fileSize)
                }

                override fun onError(exception: ImageCaptureException) {
                    Log.e(TAG, "拍照失败", exception)
                    callback?.onError("拍照失败: ${exception.message}")
                }
            }
        )
    }

    /**
     * 切换摄像头
     */
    suspend fun switchCamera() = withContext(Dispatchers.Main) {
        currentLensFacing = if (currentLensFacing == CameraSelector.LENS_FACING_BACK) {
            CameraSelector.LENS_FACING_FRONT
        } else {
            CameraSelector.LENS_FACING_BACK
        }
        
        if (isInitialized) {
            setupCamera()
        }
    }

    /**
     * 设置闪光灯模式
     */
    fun setFlashMode(flashMode: Int) {
        currentFlashMode = flashMode
        imageCapture?.flashMode = flashMode
    }

    /**
     * 获取当前摄像头方向
     */
    fun getCurrentLensFacing(): Int = currentLensFacing

    /**
     * 获取当前闪光灯模式
     */
    fun getCurrentFlashMode(): Int = currentFlashMode

    /**
     * 检查是否有前置摄像头
     */
    fun hasFrontCamera(): Boolean {
        return try {
            val cameraProvider = this.cameraProvider ?: return false
            cameraProvider.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查是否有后置摄像头
     */
    fun hasBackCamera(): Boolean {
        return try {
            val cameraProvider = this.cameraProvider ?: return false
            cameraProvider.hasCamera(CameraSelector.DEFAULT_BACK_CAMERA)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查是否支持闪光灯
     */
    fun hasFlash(): Boolean {
        return try {
            val camera = this.camera ?: return false
            camera.cameraInfo.hasFlashUnit()
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 释放资源
     */
    fun dispose() {
        cameraProvider?.unbindAll()
        cameraExecutor.shutdown()
        isInitialized = false
        callback = null
    }
}

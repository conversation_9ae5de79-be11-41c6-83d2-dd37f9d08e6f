import 'package:flutter/material.dart';
import 'dart:async';
import 'package:multi_camera_plugin/multi_camera_plugin.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '多摄像头插件示例',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const CameraExamplePage(),
    );
  }
}

class CameraExamplePage extends StatefulWidget {
  const CameraExamplePage({super.key});

  @override
  State<CameraExamplePage> createState() => _CameraExamplePageState();
}

class _CameraExamplePageState extends State<CameraExamplePage> {
  final MultiCameraPlugin _plugin = MultiCameraPlugin.instance;

  CameraStatus _status = CameraStatus.uninitialized;
  CameraImplementationType? _currentImplementation;
  List<CameraImplementationType> _availableImplementations = [];
  CameraLensDirection _currentDirection = CameraLensDirection.back;
  FlashMode _currentFlashMode = FlashMode.off;
  String? _lastCapturedImage;
  String _statusMessage = '';

  late StreamSubscription _statusSubscription;
  late StreamSubscription _errorSubscription;
  late StreamSubscription _eventSubscription;

  @override
  void initState() {
    super.initState();
    _setupListeners();
    _initializeCamera();
  }

  @override
  void dispose() {
    _statusSubscription.cancel();
    _errorSubscription.cancel();
    _eventSubscription.cancel();
    _plugin.dispose();
    super.dispose();
  }

  /// 设置监听器
  void _setupListeners() {
    _statusSubscription = _plugin.statusStream.listen((status) {
      setState(() {
        _status = status;
        _currentImplementation = _plugin.currentImplementation;
        _availableImplementations = _plugin.availableImplementations;
      });
    });

    _errorSubscription = _plugin.errorStream.listen((error) {
      setState(() {
        _statusMessage = '错误: ${error.message}';
      });
      _showSnackBar('摄像头错误: ${error.message}', isError: true);
    });

    _eventSubscription = _plugin.eventStream.listen((event) {
      if (event is CameraImplementationSwitchedEvent) {
        setState(() {
          _statusMessage =
              '已切换到: ${_getImplementationName(event.newImplementation)}';
        });
        _showSnackBar(
          '摄像头实现已切换: ${_getImplementationName(event.oldImplementation)} → ${_getImplementationName(event.newImplementation)}\n原因: ${event.reason}',
        );
      } else if (event is CameraCaptureCompletedEvent) {
        setState(() {
          _lastCapturedImage = event.result.imagePath;
          _statusMessage = '拍照成功: ${event.result.imagePath}';
        });
        _showSnackBar('拍照成功！');
      }
    });
  }

  /// 初始化摄像头
  Future<void> _initializeCamera() async {
    try {
      setState(() {
        _statusMessage = '正在初始化摄像头...';
      });

      final config = CameraConfig(
        lensDirection: _currentDirection,
        enableAudio: false,
        imageQuality: 0.8,
        isCircular: false,
        isSquare: false,
      );

      await _plugin.initialize(config: config);

      setState(() {
        _statusMessage = '摄像头初始化成功';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '初始化失败: $e';
      });
    }
  }

  /// 拍照
  Future<void> _takePicture() async {
    try {
      setState(() {
        _statusMessage = '正在拍照...';
      });

      await _plugin.takePicture();
    } catch (e) {
      setState(() {
        _statusMessage = '拍照失败: $e';
      });
      _showSnackBar('拍照失败: $e', isError: true);
    }
  }

  /// 切换摄像头方向
  Future<void> _switchCamera() async {
    try {
      final newDirection = _currentDirection == CameraLensDirection.back
          ? CameraLensDirection.front
          : CameraLensDirection.back;

      await _plugin.switchCamera(newDirection);

      setState(() {
        _currentDirection = newDirection;
        _statusMessage =
            '已切换到${newDirection == CameraLensDirection.front ? '前置' : '后置'}摄像头';
      });
    } catch (e) {
      _showSnackBar('切换摄像头失败: $e', isError: true);
    }
  }

  /// 切换闪光灯模式
  Future<void> _toggleFlash() async {
    try {
      final modes = [
        FlashMode.off,
        FlashMode.on,
        FlashMode.auto,
        FlashMode.torch
      ];
      final currentIndex = modes.indexOf(_currentFlashMode);
      final newMode = modes[(currentIndex + 1) % modes.length];

      await _plugin.setFlashMode(newMode);

      setState(() {
        _currentFlashMode = newMode;
        _statusMessage = '闪光灯模式: ${_getFlashModeName(newMode)}';
      });
    } catch (e) {
      _showSnackBar('设置闪光灯失败: $e', isError: true);
    }
  }

  /// 手动切换实现方案
  Future<void> _switchImplementation(
      CameraImplementationType implementation) async {
    try {
      await _plugin.switchImplementation(implementation);
      _showSnackBar('已切换到: ${_getImplementationName(implementation)}');
    } catch (e) {
      _showSnackBar('切换实现方案失败: $e', isError: true);
    }
  }

  /// 显示SnackBar
  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : null,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 获取实现方案名称
  String _getImplementationName(CameraImplementationType implementation) {
    switch (implementation) {
      case CameraImplementationType.cameraAwesome:
        return 'CameraAwesome';
      case CameraImplementationType.flutterCamera:
        return 'Flutter Camera';
      case CameraImplementationType.native:
        return '原生实现';
    }
  }

  /// 获取闪光灯模式名称
  String _getFlashModeName(FlashMode mode) {
    switch (mode) {
      case FlashMode.off:
        return '关闭';
      case FlashMode.on:
        return '开启';
      case FlashMode.auto:
        return '自动';
      case FlashMode.torch:
        return '常亮';
    }
  }

  /// 获取状态名称
  String _getStatusName(CameraStatus status) {
    switch (status) {
      case CameraStatus.uninitialized:
        return '未初始化';
      case CameraStatus.initializing:
        return '初始化中';
      case CameraStatus.initialized:
        return '已就绪';
      case CameraStatus.takingPicture:
        return '拍照中';
      case CameraStatus.error:
        return '错误';
      case CameraStatus.disposed:
        return '已释放';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('多摄像头插件示例'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // 状态信息面板
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('状态: ${_getStatusName(_status)}'),
                if (_currentImplementation != null)
                  Text(
                      '当前实现: ${_getImplementationName(_currentImplementation!)}'),
                Text(
                    '摄像头方向: ${_currentDirection == CameraLensDirection.front ? '前置' : '后置'}'),
                Text('闪光灯: ${_getFlashModeName(_currentFlashMode)}'),
                if (_statusMessage.isNotEmpty) Text('消息: $_statusMessage'),
                if (_lastCapturedImage != null)
                  Text('最后拍照: $_lastCapturedImage'),
              ],
            ),
          ),

          // 摄像头预览
          Expanded(
            child: _status == CameraStatus.initialized
                ? MultiCameraPreview(
                    cameraConfig: CameraConfig(
                      lensDirection: _currentDirection,
                      enableAudio: false,
                      imageQuality: 0.8,
                      isCircular: false,
                      isSquare: false,
                    ),
                    previewConfig: const CameraPreviewConfig(),
                    showStatusIndicator: true,
                    showImplementationIndicator: true,
                    onCameraReady: () {
                      setState(() {
                        _statusMessage = '摄像头预览已就绪';
                      });
                    },
                    onError: (error) {
                      setState(() {
                        _statusMessage = '预览错误: ${error.message}';
                      });
                    },
                    onImplementationSwitched: (old, newImpl, reason) {
                      setState(() {
                        _statusMessage =
                            '实现已切换: ${_getImplementationName(old)} → ${_getImplementationName(newImpl)}';
                      });
                    },
                  )
                : Container(
                    color: Colors.black,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (_status == CameraStatus.initializing)
                            const CircularProgressIndicator(
                                color: Colors.white),
                          const SizedBox(height: 16),
                          Text(
                            _status == CameraStatus.uninitialized
                                ? '点击初始化按钮开始'
                                : _getStatusName(_status),
                            style: const TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
          ),

          // 控制按钮
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // 第一行按钮
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: _status == CameraStatus.uninitialized
                          ? _initializeCamera
                          : null,
                      child: const Text('初始化'),
                    ),
                    ElevatedButton(
                      onPressed: _status == CameraStatus.initialized
                          ? _takePicture
                          : null,
                      child: const Text('拍照'),
                    ),
                    ElevatedButton(
                      onPressed: _status == CameraStatus.initialized
                          ? _switchCamera
                          : null,
                      child: const Text('切换摄像头'),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // 第二行按钮
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: _status == CameraStatus.initialized
                          ? _toggleFlash
                          : null,
                      child: const Text('切换闪光灯'),
                    ),
                    ElevatedButton(
                      onPressed: () => _showImplementationDialog(),
                      child: const Text('切换实现'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 显示实现方案选择对话框
  void _showImplementationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择摄像头实现方案'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _availableImplementations.map((impl) {
            final isSelected = impl == _currentImplementation;
            return ListTile(
              title: Text(_getImplementationName(impl)),
              leading: Radio<CameraImplementationType>(
                value: impl,
                groupValue: _currentImplementation,
                onChanged: (value) {
                  Navigator.of(context).pop();
                  if (value != null) {
                    _switchImplementation(value);
                  }
                },
              ),
              trailing: isSelected
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }
}

import Flutter
import UIKit
import AVFoundation

public class MultiCameraPlugin: NSObject, FlutterPlugin {
    private var nativeCameraManager: NativeCameraManager?
    private var flutterApi: MultiCameraFlutterApi?

    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "multi_camera_plugin", binaryMessenger: registrar.messenger())
        let instance = MultiCameraPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)

        // 设置Pigeon API
        MultiCameraHostApiSetup.setUp(binaryMessenger: registrar.messenger(), api: instance)
        instance.flutterApi = MultiCameraFlutterApi(binaryMessenger: registrar.messenger())
    }

    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "getPlatformVersion":
            result("iOS " + UIDevice.current.systemVersion)
        default:
            result(FlutterMethodNotImplemented)
        }
    }

    func setFlashMode(mode: FlashMode, completion: @escaping (Result<Void, Error>) -> Void) {
        let manager = getNativeCameraManager()

        let flashMode: AVCaptureDevice.FlashMode = {
            switch mode {
            case .off:
                return .off
            case .on:
                return .on
            case .auto:
                return .auto
            case .torch:
                return .on // iOS中torch模式需要特殊处理，这里简化为on
            }
        }()

        manager.setFlashMode(flashMode)
        completion(.success(()))
    }

    func startPreview(completion: @escaping (Result<Void, Error>) -> Void) {
        // AVFoundation自动处理预览
        completion(.success(()))
    }

    func stopPreview(completion: @escaping (Result<Void, Error>) -> Void) {
        // AVFoundation自动处理预览
        completion(.success(()))
    }

    func dispose(completion: @escaping (Result<Void, Error>) -> Void) {
        nativeCameraManager?.dispose()
        nativeCameraManager = nil
        completion(.success(()))
    }

    func isImplementationAvailable(
        implementationType: CameraImplementationType,
        completion: @escaping (Result<Bool, Error>) -> Void
    ) {
        let manager = getNativeCameraManager()
        let isAvailable = manager.isAvailable()
        completion(.success(isAvailable))
    }

    // MARK: - Private Methods

    private func getNativeCameraManager() -> NativeCameraManager {
        if nativeCameraManager == nil {
            nativeCameraManager = NativeCameraManager()
        }
        return nativeCameraManager!
    }
}

// MARK: - MultiCameraHostApi Implementation

extension MultiCameraPlugin: MultiCameraHostApi {

    func getCameraCapabilities(completion: @escaping (Result<CameraCapabilities, Error>) -> Void) {
        let manager = getNativeCameraManager()

        let capabilities = CameraCapabilities(
            supportedImplementations: [CameraImplementationType.native],
            hasFrontCamera: manager.hasFrontCamera(),
            hasBackCamera: manager.hasBackCamera(),
            hasFlash: manager.hasFlash(),
            hasZoom: false // 暂不支持变焦
        )

        completion(.success(capabilities))
    }

    func initializeCamera(
        config: CameraConfig,
        implementationType: CameraImplementationType,
        completion: @escaping (Result<Void, Error>) -> Void
    ) {
        let manager = getNativeCameraManager()

        let position: AVCaptureDevice.Position = {
            switch config.lensDirection {
            case .front:
                return .front
            case .back:
                return .back
            default:
                return .back
            }
        }()

        manager.initialize(position: position) { [weak self] success, error in
            if success {
                self?.flutterApi?.onCameraStatusChanged(status: .initialized) { _ in }
                completion(.success(()))
            } else {
                let cameraError = CameraError(
                    code: "INITIALIZATION_FAILED",
                    message: error ?? "初始化失败",
                    details: nil
                )
                self?.flutterApi?.onCameraError(error: cameraError) { _ in }
                completion(.failure(MultiCameraFlutterError(
                    code: "INITIALIZATION_FAILED",
                    message: error ?? "初始化失败",
                    details: nil
                )))
            }
        }
    }

    func switchImplementation(
        implementationType: CameraImplementationType,
        completion: @escaping (Result<Void, Error>) -> Void
    ) {
        // 原生实现只支持一种类型，直接返回成功
        completion(.success(()))
    }

    func switchCamera(
        direction: CameraLensDirection,
        completion: @escaping (Result<Void, Error>) -> Void
    ) {
        let manager = getNativeCameraManager()

        let position: AVCaptureDevice.Position = {
            switch direction {
            case .front:
                return .front
            case .back:
                return .back
            default:
                return .back
            }
        }()

        // 由于AVFoundation的限制，我们需要重新初始化来切换摄像头
        manager.switchCamera { success, error in
            if success {
                completion(.success(()))
            } else {
                completion(.failure(MultiCameraFlutterError(
                    code: "SWITCH_CAMERA_FAILED",
                    message: error ?? "切换摄像头失败",
                    details: nil
                )))
            }
        }
    }

    func takePicture(completion: @escaping (Result<CaptureResult, Error>) -> Void) {
        let manager = getNativeCameraManager()

        manager.takePicture { imagePath, fileSize, error in
            if let imagePath = imagePath {
                let result = CaptureResult(
                    imagePath: imagePath,
                    timestamp: Int64(Date().timeIntervalSince1970 * 1000),
                    fileSize: fileSize
                )
                completion(.success(result))
            } else {
                completion(.failure(MultiCameraFlutterError(
                    code: "CAPTURE_FAILED",
                    message: error ?? "拍照失败",
                    details: nil
                )))
            }
        }
    }

import AVFoundation
import UIKit

/**
 * iOS原生摄像头管理器
 * 
 * 使用AVFoundation框架提供稳定可靠的摄像头功能
 * 支持iOS 12.0+，兼容性极佳
 */
class NativeCameraManager: NSObject {
    
    // MARK: - Properties
    
    private var captureSession: AVCaptureSession?
    private var photoOutput: AVCapturePhotoOutput?
    private var videoPreviewLayer: AVCaptureVideoPreviewLayer?
    private var currentCameraDevice: AVCaptureDevice?
    private var currentCameraInput: AVCaptureDeviceInput?
    
    // 当前配置
    private var currentPosition: AVCaptureDevice.Position = .back
    private var currentFlashMode: AVCaptureDevice.FlashMode = .off
    private var isInitialized = false
    
    // 回调
    typealias InitializationCallback = (Bool, String?) -> Void
    typealias CaptureCallback = (String?, Int64, String?) -> Void
    
    private var initializationCallback: InitializationCallback?
    private var captureCallback: CaptureCallback?
    
    // MARK: - Public Methods
    
    /**
     * 检查摄像头是否可用
     */
    func isAvailable() -> Bool {
        return AVCaptureDevice.authorizationStatus(for: .video) == .authorized &&
               !AVCaptureDevice.DiscoverySession(
                   deviceTypes: [.builtInWideAngleCamera],
                   mediaType: .video,
                   position: .unspecified
               ).devices.isEmpty
    }
    
    /**
     * 初始化摄像头
     */
    func initialize(position: AVCaptureDevice.Position = .back, callback: @escaping InitializationCallback) {
        self.initializationCallback = callback
        self.currentPosition = position
        
        // 检查权限
        checkCameraPermission { [weak self] granted in
            if granted {
                self?.setupCamera()
            } else {
                callback(false, "缺少摄像头权限")
            }
        }
    }
    
    /**
     * 拍照
     */
    func takePicture(callback: @escaping CaptureCallback) {
        guard let photoOutput = self.photoOutput else {
            callback(nil, 0, "摄像头未初始化")
            return
        }
        
        self.captureCallback = callback
        
        let settings = AVCapturePhotoSettings()
        settings.flashMode = currentFlashMode
        
        // 设置图片格式
        if photoOutput.availablePhotoCodecTypes.contains(.jpeg) {
            settings.photoQualityPrioritization = .quality
        }
        
        photoOutput.capturePhoto(with: settings, delegate: self)
    }
    
    /**
     * 切换摄像头
     */
    func switchCamera(callback: @escaping (Bool, String?) -> Void) {
        guard let session = captureSession else {
            callback(false, "摄像头未初始化")
            return
        }
        
        let newPosition: AVCaptureDevice.Position = (currentPosition == .back) ? .front : .back
        
        guard let newCamera = getCameraDevice(for: newPosition) else {
            callback(false, "未找到指定方向的摄像头")
            return
        }
        
        do {
            let newInput = try AVCaptureDeviceInput(device: newCamera)
            
            session.beginConfiguration()
            
            // 移除旧输入
            if let currentInput = currentCameraInput {
                session.removeInput(currentInput)
            }
            
            // 添加新输入
            if session.canAddInput(newInput) {
                session.addInput(newInput)
                currentCameraInput = newInput
                currentCameraDevice = newCamera
                currentPosition = newPosition
            } else {
                // 恢复旧输入
                if let currentInput = currentCameraInput {
                    session.addInput(currentInput)
                }
                callback(false, "无法添加新的摄像头输入")
                session.commitConfiguration()
                return
            }
            
            session.commitConfiguration()
            callback(true, nil)
            
        } catch {
            callback(false, "切换摄像头失败: \(error.localizedDescription)")
        }
    }
    
    /**
     * 设置闪光灯模式
     */
    func setFlashMode(_ mode: AVCaptureDevice.FlashMode) {
        currentFlashMode = mode
        
        guard let device = currentCameraDevice, device.hasFlash else {
            return
        }
        
        do {
            try device.lockForConfiguration()
            if device.isFlashModeSupported(mode) {
                device.flashMode = mode
            }
            device.unlockForConfiguration()
        } catch {
            print("设置闪光灯模式失败: \(error)")
        }
    }
    
    /**
     * 获取预览层
     */
    func getPreviewLayer() -> AVCaptureVideoPreviewLayer? {
        return videoPreviewLayer
    }
    
    /**
     * 检查是否有前置摄像头
     */
    func hasFrontCamera() -> Bool {
        return getCameraDevice(for: .front) != nil
    }
    
    /**
     * 检查是否有后置摄像头
     */
    func hasBackCamera() -> Bool {
        return getCameraDevice(for: .back) != nil
    }
    
    /**
     * 检查是否支持闪光灯
     */
    func hasFlash() -> Bool {
        return currentCameraDevice?.hasFlash ?? false
    }
    
    /**
     * 释放资源
     */
    func dispose() {
        captureSession?.stopRunning()
        captureSession = nil
        photoOutput = nil
        videoPreviewLayer = nil
        currentCameraDevice = nil
        currentCameraInput = nil
        isInitialized = false
    }
    
    // MARK: - Private Methods
    
    private func checkCameraPermission(completion: @escaping (Bool) -> Void) {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            completion(true)
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    completion(granted)
                }
            }
        case .denied, .restricted:
            completion(false)
        @unknown default:
            completion(false)
        }
    }
    
    private func setupCamera() {
        guard let camera = getCameraDevice(for: currentPosition) else {
            initializationCallback?(false, "未找到指定方向的摄像头")
            return
        }
        
        do {
            // 创建会话
            let session = AVCaptureSession()
            session.sessionPreset = .photo
            
            // 创建输入
            let input = try AVCaptureDeviceInput(device: camera)
            if session.canAddInput(input) {
                session.addInput(input)
                currentCameraInput = input
                currentCameraDevice = camera
            } else {
                initializationCallback?(false, "无法添加摄像头输入")
                return
            }
            
            // 创建照片输出
            let photoOutput = AVCapturePhotoOutput()
            if session.canAddOutput(photoOutput) {
                session.addOutput(photoOutput)
                self.photoOutput = photoOutput
            } else {
                initializationCallback?(false, "无法添加照片输出")
                return
            }
            
            // 创建预览层
            let previewLayer = AVCaptureVideoPreviewLayer(session: session)
            previewLayer.videoGravity = .resizeAspectFill
            self.videoPreviewLayer = previewLayer
            
            self.captureSession = session
            
            // 启动会话
            DispatchQueue.global(qos: .userInitiated).async {
                session.startRunning()
                DispatchQueue.main.async {
                    self.isInitialized = true
                    self.initializationCallback?(true, nil)
                }
            }
            
        } catch {
            initializationCallback?(false, "初始化摄像头失败: \(error.localizedDescription)")
        }
    }
    
    private func getCameraDevice(for position: AVCaptureDevice.Position) -> AVCaptureDevice? {
        let discoverySession = AVCaptureDevice.DiscoverySession(
            deviceTypes: [.builtInWideAngleCamera],
            mediaType: .video,
            position: position
        )
        return discoverySession.devices.first
    }
    
    private func getDocumentsDirectory() -> URL {
        let paths = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
        return paths[0]
    }
}

// MARK: - AVCapturePhotoCaptureDelegate

extension NativeCameraManager: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        if let error = error {
            captureCallback?(nil, 0, "拍照失败: \(error.localizedDescription)")
            return
        }
        
        guard let imageData = photo.fileDataRepresentation() else {
            captureCallback?(nil, 0, "无法获取图片数据")
            return
        }
        
        // 保存图片到文档目录
        let filename = "photo_\(Int(Date().timeIntervalSince1970 * 1000)).jpg"
        let documentsPath = getDocumentsDirectory()
        let multiCameraPath = documentsPath.appendingPathComponent("multi_camera", isDirectory: true)
        
        // 创建目录
        try? FileManager.default.createDirectory(at: multiCameraPath, withIntermediateDirectories: true, attributes: nil)
        
        let fileURL = multiCameraPath.appendingPathComponent(filename)
        
        do {
            try imageData.write(to: fileURL)
            let fileSize = Int64(imageData.count)
            captureCallback?(fileURL.path, fileSize, nil)
        } catch {
            captureCallback?(nil, 0, "保存图片失败: \(error.localizedDescription)")
        }
    }
}

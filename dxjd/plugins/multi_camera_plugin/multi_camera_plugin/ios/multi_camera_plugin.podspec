#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint multi_camera_plugin.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'multi_camera_plugin'
  s.version          = '1.0.0'
  s.summary          = '一个支持多种摄像头实现方案的Flutter插件'
  s.description      = <<-DESC
一个支持多种摄像头实现方案的Flutter插件，提供最大的设备兼容性和自动降级功能。
支持CameraAwesome、Flutter Camera和原生实现。
                       DESC
  s.homepage         = 'https://github.com/dxjd/multi_camera_plugin'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'DXJD' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.dependency 'Flutter'
  s.platform = :ios, '12.0'

  # 添加AVFoundation框架依赖
  s.frameworks = 'AVFoundation', 'CoreMedia', 'CoreVideo'

  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
  s.swift_version = '5.0'

  # If your plugin requires a privacy manifest, for example if it uses any
  # required reason APIs, update the PrivacyInfo.xcprivacy file to describe your
  # plugin's privacy impact, and then uncomment this line. For more information,
  # see https://developer.apple.com/documentation/bundleresources/privacy_manifest_files
  # s.resource_bundles = {'multi_camera_plugin_privacy' => ['Resources/PrivacyInfo.xcprivacy']}
end

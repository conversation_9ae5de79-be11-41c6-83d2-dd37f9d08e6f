/// 多摄像头插件
///
/// 这是一个支持多种摄像头实现方案的Flutter插件，
/// 提供最大的设备兼容性和自动降级功能。
library multi_camera_plugin;

// 导出所有公共API
export 'src/generated/camera_api.g.dart';
export 'src/models/camera_models.dart';
export 'src/managers/multi_camera_manager.dart';

// 导出Widget
export 'src/widgets/multi_camera_preview.dart';

import 'src/managers/multi_camera_manager.dart';
import 'src/models/camera_models.dart';

/// 多摄像头插件主类
///
/// 提供简化的API接口，内部使用MultiCameraManager进行管理
class MultiCameraPlugin {
  static MultiCameraPlugin? _instance;
  static MultiCameraPlugin get instance => _instance ??= MultiCameraPlugin._();

  MultiCameraPlugin._();

  /// 获取管理器实例
  MultiCameraManager get manager => MultiCameraManager.instance;

  /// 初始化摄像头
  ///
  /// [config] 摄像头配置
  /// [priority] 实现方案优先级配置
  /// [preferredImplementation] 首选实现方案
  Future<void> initialize({
    required CameraConfig config,
    CameraImplementationPriority? priority,
    CameraImplementationType? preferredImplementation,
  }) async {
    return manager.initialize(
      config: config,
      priority: priority,
      preferredImplementation: preferredImplementation,
    );
  }

  /// 拍照
  Future<CaptureResult> takePicture() async {
    return manager.takePicture();
  }

  /// 切换摄像头方向
  Future<void> switchCamera(CameraLensDirection direction) async {
    return manager.switchCamera(direction);
  }

  /// 设置闪光灯模式
  Future<void> setFlashMode(FlashMode mode) async {
    return manager.setFlashMode(mode);
  }

  /// 手动切换实现方案
  Future<void> switchImplementation(
      CameraImplementationType implementationType) async {
    return manager.switchImplementation(implementationType);
  }

  /// 释放资源
  Future<void> dispose() async {
    return manager.dispose();
  }

  /// 当前状态
  CameraStatus get status => manager.status;

  /// 当前实现类型
  CameraImplementationType? get currentImplementation =>
      manager.currentImplementation;

  /// 可用的实现方案
  List<CameraImplementationType> get availableImplementations =>
      manager.availableImplementations;

  /// 事件流
  Stream<CameraEvent> get eventStream => manager.eventStream;

  /// 状态流
  Stream<CameraStatus> get statusStream => manager.statusStream;

  /// 错误流
  Stream<CameraError> get errorStream => manager.errorStream;
}

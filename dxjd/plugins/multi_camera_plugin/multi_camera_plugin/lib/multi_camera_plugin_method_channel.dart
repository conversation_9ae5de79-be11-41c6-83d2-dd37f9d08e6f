import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'multi_camera_plugin_platform_interface.dart';

/// An implementation of [MultiCameraPluginPlatform] that uses method channels.
class MethodChannelMultiCameraPlugin extends MultiCameraPluginPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('multi_camera_plugin');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}

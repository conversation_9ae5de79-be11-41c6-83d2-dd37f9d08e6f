import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'multi_camera_plugin_method_channel.dart';

abstract class MultiCameraPluginPlatform extends PlatformInterface {
  /// Constructs a MultiCameraPluginPlatform.
  MultiCameraPluginPlatform() : super(token: _token);

  static final Object _token = Object();

  static MultiCameraPluginPlatform _instance = MethodChannelMultiCameraPlugin();

  /// The default instance of [MultiCameraPluginPlatform] to use.
  ///
  /// Defaults to [MethodChannelMultiCameraPlugin].
  static MultiCameraPluginPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [MultiCameraPluginPlatform] when
  /// they register themselves.
  static set instance(MultiCameraPluginPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}

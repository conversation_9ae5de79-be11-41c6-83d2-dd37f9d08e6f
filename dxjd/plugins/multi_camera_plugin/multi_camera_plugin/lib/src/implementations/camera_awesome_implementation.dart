import 'dart:io';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../models/camera_models.dart';

/// CameraAwesome插件实现
/// 
/// 这是一个功能强大的第三方摄像头插件，提供了丰富的功能和良好的性能。
/// 适用于大多数iOS和Android设备，特别是在需要高级功能时表现优秀。
class CameraAwesomeImplementation {
  static CameraAwesomeBuilder? _cameraController;
  static CameraConfig? _config;
  static bool _isInitialized = false;
  static CameraLensDirection _currentDirection = CameraLensDirection.back;
  static FlashMode _currentFlashMode = FlashMode.off;

  /// 检查CameraAwesome是否可用
  static Future<bool> isAvailable() async {
    try {
      // 检查是否有可用的摄像头
      return true; // CameraAwesome通常都可用
    } catch (e) {
      return false;
    }
  }

  /// 初始化摄像头
  static Future<void> initialize(CameraConfig config) async {
    if (_isInitialized) {
      await dispose();
    }

    _config = config;
    _currentDirection = config.lensDirection;
    _isInitialized = true;
  }

  /// 创建摄像头预览Widget
  static Widget buildPreview({
    required CameraPreviewConfig previewConfig,
    VoidCallback? onCameraReady,
    Function(CameraError)? onError,
  }) {
    if (!_isInitialized || _config == null) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Text(
            '摄像头未初始化',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    return CameraAwesomeBuilder.awesome(
      saveConfig: SaveConfig.photoAndVideo(
        initialCaptureMode: CaptureMode.photo,
        photoPathBuilder: () async {
          final extDir = await getApplicationDocumentsDirectory();
          final testDir = await Directory('${extDir.path}/multi_camera/photos')
              .create(recursive: true);
          return '${testDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
        },
        videoPathBuilder: () async {
          final extDir = await getApplicationDocumentsDirectory();
          final testDir = await Directory('${extDir.path}/multi_camera/videos')
              .create(recursive: true);
          return '${testDir.path}/${DateTime.now().millisecondsSinceEpoch}.mp4';
        },
      ),
      sensorConfig: SensorConfig.single(
        sensor: _currentDirection == CameraLensDirection.front
            ? Sensor.front
            : Sensor.back,
        flashMode: _convertFlashMode(_currentFlashMode),
        aspectRatio: CameraAspectRatios.ratio_1_1,
      ),
      enableAudio: _config!.enableAudio,
      onMediaTap: (mediaCapture) {
        // 处理媒体点击事件
      },
      onMediaCaptureEvent: (event) {
        // 处理媒体捕获事件
        if (event.status == MediaCaptureStatus.success) {
          onCameraReady?.call();
        } else if (event.status == MediaCaptureStatus.failure) {
          onError?.call(CameraError(
            code: 'CAPTURE_FAILED',
            message: '拍照失败',
            details: event.exception?.toString(),
          ));
        }
      },
      builder: (context, state) {
        return _buildPreviewWithConfig(state, previewConfig);
      },
    );
  }

  /// 根据配置构建预览界面
  static Widget _buildPreviewWithConfig(
    CameraState state,
    CameraPreviewConfig config,
  ) {
    Widget preview = state.when(
      onPreparingCamera: (state) => Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      ),
      onPhotoMode: (state) => CameraPreview(state: state),
      onVideoMode: (state) => CameraPreview(state: state),
      onVideoRecordingMode: (state) => CameraPreview(state: state),
    );

    // 应用预览配置
    if (config.width != null || config.height != null) {
      preview = SizedBox(
        width: config.width,
        height: config.height,
        child: preview,
      );
    }

    if (config.isCircular) {
      preview = ClipOval(child: preview);
    } else if (config.borderRadius > 0) {
      preview = ClipRRect(
        borderRadius: BorderRadius.circular(config.borderRadius),
        child: preview,
      );
    }

    if (config.showBorder) {
      preview = Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: Color(config.borderColor ?? 0xFFFFFFFF),
            width: config.borderWidth,
          ),
          borderRadius: config.isCircular
              ? null
              : BorderRadius.circular(config.borderRadius),
          shape: config.isCircular ? BoxShape.circle : BoxShape.rectangle,
        ),
        child: preview,
      );
    }

    return preview;
  }

  /// 拍照
  static Future<CaptureResult> takePicture() async {
    if (!_isInitialized) {
      throw CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    try {
      // 这里需要通过CameraAwesome的状态来拍照
      // 由于CameraAwesome的API设计，我们需要在builder中处理拍照逻辑
      final extDir = await getApplicationDocumentsDirectory();
      final testDir = await Directory('${extDir.path}/multi_camera/photos')
          .create(recursive: true);
      final filePath = '${testDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
      
      // 模拟拍照结果，实际实现需要与CameraAwesome的状态管理集成
      final file = File(filePath);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      
      return CaptureResult(
        imagePath: filePath,
        timestamp: timestamp,
        fileSize: await file.length(),
      );
    } catch (e) {
      throw CameraError(
        code: 'CAPTURE_FAILED',
        message: '拍照失败: ${e.toString()}',
      );
    }
  }

  /// 切换摄像头方向
  static Future<void> switchCamera(CameraLensDirection direction) async {
    if (!_isInitialized) {
      throw CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    _currentDirection = direction;
    // 需要重新初始化摄像头以切换方向
    if (_config != null) {
      final newConfig = CameraConfig(
        lensDirection: direction,
        enableAudio: _config!.enableAudio,
        imageQuality: _config!.imageQuality,
        width: _config!.width,
        height: _config!.height,
        isCircular: _config!.isCircular,
        isSquare: _config!.isSquare,
      );
      await initialize(newConfig);
    }
  }

  /// 设置闪光灯模式
  static Future<void> setFlashMode(FlashMode mode) async {
    if (!_isInitialized) {
      throw CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    _currentFlashMode = mode;
    // CameraAwesome的闪光灯设置需要通过SensorConfig
  }

  /// 开始预览
  static Future<void> startPreview() async {
    // CameraAwesome自动处理预览
  }

  /// 停止预览
  static Future<void> stopPreview() async {
    // CameraAwesome自动处理预览
  }

  /// 释放资源
  static Future<void> dispose() async {
    _cameraController = null;
    _config = null;
    _isInitialized = false;
  }

  /// 转换闪光灯模式
  static FlashMode _convertFlashMode(FlashMode mode) {
    switch (mode) {
      case FlashMode.off:
        return FlashMode.off;
      case FlashMode.on:
        return FlashMode.on;
      case FlashMode.auto:
        return FlashMode.auto;
      case FlashMode.torch:
        return FlashMode.torch;
    }
  }

  /// 获取当前摄像头方向
  static CameraLensDirection get currentDirection => _currentDirection;

  /// 获取当前闪光灯模式
  static FlashMode get currentFlashMode => _currentFlashMode;

  /// 是否已初始化
  static bool get isInitialized => _isInitialized;
}

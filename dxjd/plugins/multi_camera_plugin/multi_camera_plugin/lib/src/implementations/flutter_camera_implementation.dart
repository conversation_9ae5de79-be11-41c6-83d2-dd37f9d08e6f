import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../models/camera_models.dart';

/// Flutter Camera插件实现
///
/// 这是Flutter官方提供的摄像头插件，具有良好的稳定性和性能。
/// 适用于大多数iOS和Android设备。
class FlutterCameraImplementation {
  static CameraController? _controller;
  static models.CameraConfig? _config;
  static List<CameraDescription>? _cameras;
  static bool _isInitialized = false;
  static models.CameraLensDirection _currentDirection =
      models.CameraLensDirection.back;
  static models.FlashMode _currentFlashMode = models.FlashMode.off;

  /// 检查Flutter Camera是否可用
  static Future<bool> isAvailable() async {
    try {
      final cameras = await availableCameras();
      return cameras.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 初始化摄像头
  static Future<void> initialize(models.CameraConfig config) async {
    if (_isInitialized) {
      await dispose();
    }

    try {
      _cameras = await availableCameras();
      if (_cameras == null || _cameras!.isEmpty) {
        throw models.CameraError(
          code: 'NO_CAMERAS',
          message: '未找到可用的摄像头',
        );
      }

      _config = config;
      _currentDirection = config.lensDirection;

      // 查找指定方向的摄像头
      final camera = _findCamera(config.lensDirection);
      if (camera == null) {
        throw models.CameraError(
          code: 'CAMERA_NOT_FOUND',
          message: '未找到指定方向的摄像头',
        );
      }

      // 创建摄像头控制器
      _controller = CameraController(
        camera,
        ResolutionPreset.high,
        enableAudio: config.enableAudio,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      // 初始化控制器
      await _controller!.initialize();

      // 设置闪光灯模式
      await setFlashMode(_currentFlashMode);

      _isInitialized = true;
    } catch (e) {
      throw models.CameraError(
        code: 'INITIALIZATION_FAILED',
        message: '摄像头初始化失败: ${e.toString()}',
      );
    }
  }

  /// 创建摄像头预览Widget
  static Widget buildPreview({
    required models.CameraPreviewConfig previewConfig,
    VoidCallback? onCameraReady,
    Function(models.CameraError)? onError,
  }) {
    if (!_isInitialized || _controller == null) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Text(
            '摄像头未初始化',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    if (!_controller!.value.isInitialized) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    Widget preview = CameraPreview(_controller!);

    // 应用预览配置
    if (previewConfig.width != null || previewConfig.height != null) {
      preview = SizedBox(
        width: previewConfig.width,
        height: previewConfig.height,
        child: preview,
      );
    }

    if (previewConfig.isCircular) {
      preview = ClipOval(child: preview);
    } else if (previewConfig.borderRadius > 0) {
      preview = ClipRRect(
        borderRadius: BorderRadius.circular(previewConfig.borderRadius),
        child: preview,
      );
    }

    if (previewConfig.showBorder) {
      preview = Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: Color(previewConfig.borderColor ?? 0xFFFFFFFF),
            width: previewConfig.borderWidth,
          ),
          borderRadius: previewConfig.isCircular
              ? null
              : BorderRadius.circular(previewConfig.borderRadius),
          shape:
              previewConfig.isCircular ? BoxShape.circle : BoxShape.rectangle,
        ),
        child: preview,
      );
    }

    // 监听摄像头状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_controller!.value.isInitialized) {
        onCameraReady?.call();
      }
      if (_controller!.value.hasError) {
        onError?.call(models.CameraError(
          code: 'CAMERA_ERROR',
          message: _controller!.value.errorDescription ?? '摄像头错误',
        ));
      }
    });

    return preview;
  }

  /// 拍照
  static Future<models.CaptureResult> takePicture() async {
    if (!_isInitialized || _controller == null) {
      throw models.CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    if (!_controller!.value.isInitialized) {
      throw models.CameraError(
        code: 'CAMERA_NOT_READY',
        message: '摄像头未准备就绪',
      );
    }

    try {
      // 设置图片质量
      if (_config?.imageQuality != null) {
        // Flutter Camera不直接支持图片质量设置，但可以通过其他方式处理
      }

      final XFile image = await _controller!.takePicture();
      final file = File(image.path);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileSize = await file.length();

      return models.CaptureResult(
        imagePath: image.path,
        timestamp: timestamp,
        fileSize: fileSize,
      );
    } catch (e) {
      throw models.CameraError(
        code: 'CAPTURE_FAILED',
        message: '拍照失败: ${e.toString()}',
      );
    }
  }

  /// 切换摄像头方向
  static Future<void> switchCamera(models.CameraLensDirection direction) async {
    if (!_isInitialized || _cameras == null) {
      throw models.CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    final camera = _findCamera(direction);
    if (camera == null) {
      throw models.CameraError(
        code: 'CAMERA_NOT_FOUND',
        message: '未找到指定方向的摄像头',
      );
    }

    try {
      // 释放当前控制器
      await _controller?.dispose();

      // 创建新的控制器
      _controller = CameraController(
        camera,
        ResolutionPreset.high,
        enableAudio: _config?.enableAudio ?? false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      // 初始化新控制器
      await _controller!.initialize();

      // 恢复闪光灯设置
      await setFlashMode(_currentFlashMode);

      _currentDirection = direction;
    } catch (e) {
      throw models.CameraError(
        code: 'SWITCH_FAILED',
        message: '切换摄像头失败: ${e.toString()}',
      );
    }
  }

  /// 设置闪光灯模式
  static Future<void> setFlashMode(models.FlashMode mode) async {
    if (!_isInitialized || _controller == null) {
      throw models.CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    try {
      final flashMode = _convertFlashMode(mode);
      await _controller!.setFlashMode(flashMode);
      _currentFlashMode = mode;
    } catch (e) {
      throw models.CameraError(
        code: 'FLASH_MODE_FAILED',
        message: '设置闪光灯模式失败: ${e.toString()}',
      );
    }
  }

  /// 开始预览
  static Future<void> startPreview() async {
    // Flutter Camera自动处理预览
  }

  /// 停止预览
  static Future<void> stopPreview() async {
    // Flutter Camera自动处理预览
  }

  /// 释放资源
  static Future<void> dispose() async {
    await _controller?.dispose();
    _controller = null;
    _config = null;
    _cameras = null;
    _isInitialized = false;
  }

  /// 查找指定方向的摄像头
  static CameraDescription? _findCamera(models.CameraLensDirection direction) {
    if (_cameras == null) return null;

    final lensDirection = _convertLensDirection(direction);
    for (final camera in _cameras!) {
      if (camera.lensDirection == lensDirection) {
        return camera;
      }
    }
    return null;
  }

  /// 转换摄像头方向
  static CameraLensDirection _convertLensDirection(
      models.CameraLensDirection direction) {
    switch (direction) {
      case models.CameraLensDirection.front:
        return CameraLensDirection.front;
      case models.CameraLensDirection.back:
        return CameraLensDirection.back;
      case models.CameraLensDirection.external:
        return CameraLensDirection.external;
    }
  }

  /// 转换闪光灯模式
  static FlashMode _convertFlashMode(models.FlashMode mode) {
    switch (mode) {
      case models.FlashMode.off:
        return FlashMode.off;
      case models.FlashMode.on:
        return FlashMode.always;
      case models.FlashMode.auto:
        return FlashMode.auto;
      case models.FlashMode.torch:
        return FlashMode.torch;
    }
  }

  /// 获取当前摄像头方向
  static models.CameraLensDirection get currentDirection => _currentDirection;

  /// 获取当前闪光灯模式
  static models.FlashMode get currentFlashMode => _currentFlashMode;

  /// 是否已初始化
  static bool get isInitialized => _isInitialized;

  /// 获取摄像头控制器（用于高级功能）
  static CameraController? get controller => _controller;
}

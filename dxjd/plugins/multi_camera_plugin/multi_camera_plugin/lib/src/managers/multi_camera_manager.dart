import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/camera_models.dart';
import '../implementations/camera_awesome_implementation.dart';
import '../implementations/flutter_camera_implementation.dart';
import '../implementations/native_implementation.dart';

/// 多摄像头管理器
///
/// 这是插件的核心类，负责管理多种摄像头实现方案，
/// 提供自动降级和手动切换功能，确保最大的设备兼容性。
class MultiCameraManager {
  static MultiCameraManager? _instance;
  static MultiCameraManager get instance =>
      _instance ??= MultiCameraManager._();

  MultiCameraManager._();

  // 状态管理
  CameraStatus _status = CameraStatus.uninitialized;
  CameraImplementationType? _currentImplementation;
  CameraConfig? _config;
  CameraImplementationPriority _priority = const CameraImplementationPriority();

  // 可用的实现方案
  final List<CameraImplementationType> _availableImplementations = [];

  // 事件流控制器
  final StreamController<CameraEvent> _eventController =
      StreamController<CameraEvent>.broadcast();
  final StreamController<CameraStatus> _statusController =
      StreamController<CameraStatus>.broadcast();
  final StreamController<CameraError> _errorController =
      StreamController<CameraError>.broadcast();

  // 重试计数
  int _retryCount = 0;

  /// 事件流
  Stream<CameraEvent> get eventStream => _eventController.stream;

  /// 状态流
  Stream<CameraStatus> get statusStream => _statusController.stream;

  /// 错误流
  Stream<CameraError> get errorStream => _errorController.stream;

  /// 当前状态
  CameraStatus get status => _status;

  /// 当前实现类型
  CameraImplementationType? get currentImplementation => _currentImplementation;

  /// 可用的实现方案
  List<CameraImplementationType> get availableImplementations =>
      List.unmodifiable(_availableImplementations);

  /// 初始化摄像头管理器
  Future<void> initialize({
    required CameraConfig config,
    CameraImplementationPriority? priority,
    CameraImplementationType? preferredImplementation,
  }) async {
    try {
      _updateStatus(CameraStatus.initializing);

      _config = config;
      if (priority != null) {
        _priority = priority;
      }

      // 检测可用的实现方案
      await _detectAvailableImplementations();

      if (_availableImplementations.isEmpty) {
        throw CameraError(
          code: 'NO_IMPLEMENTATIONS',
          message: '未找到可用的摄像头实现方案',
        );
      }

      // 选择实现方案
      CameraImplementationType? implementationType;
      if (preferredImplementation != null &&
          _availableImplementations.contains(preferredImplementation)) {
        implementationType = preferredImplementation;
      } else {
        implementationType = await _selectBestImplementation();
      }

      if (implementationType == null) {
        throw CameraError(
          code: 'NO_SUITABLE_IMPLEMENTATION',
          message: '未找到合适的摄像头实现方案',
        );
      }

      // 初始化选定的实现方案
      await _initializeImplementation(implementationType);

      _updateStatus(CameraStatus.initialized);

      // 保存用户偏好
      await _saveUserPreference(implementationType);
    } catch (e) {
      _handleError(CameraError(
        code: 'INITIALIZATION_FAILED',
        message: '摄像头初始化失败: ${e.toString()}',
      ));
    }
  }

  /// 创建摄像头预览Widget
  Widget buildPreview({
    required CameraPreviewConfig previewConfig,
    VoidCallback? onCameraReady,
    Function(CameraError)? onError,
  }) {
    if (_status != CameraStatus.initialized || _currentImplementation == null) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Text(
            '摄像头未初始化',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    switch (_currentImplementation!) {
      case CameraImplementationType.cameraAwesome:
        return CameraAwesomeImplementation.buildPreview(
          previewConfig: previewConfig,
          onCameraReady: onCameraReady,
          onError: (error) {
            _handleError(error);
            onError?.call(error);
          },
        );

      case CameraImplementationType.flutterCamera:
        return FlutterCameraImplementation.buildPreview(
          previewConfig: previewConfig,
          onCameraReady: onCameraReady,
          onError: (error) {
            _handleError(error);
            onError?.call(error);
          },
        );

      case CameraImplementationType.native:
        return NativeImplementation.buildPreview(
          previewConfig: previewConfig,
          onCameraReady: onCameraReady,
          onError: (error) {
            _handleError(error);
            onError?.call(error);
          },
        );
    }
  }

  /// 拍照
  Future<CaptureResult> takePicture() async {
    if (_status != CameraStatus.initialized || _currentImplementation == null) {
      throw CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    try {
      _updateStatus(CameraStatus.takingPicture);

      CaptureResult result;
      switch (_currentImplementation!) {
        case CameraImplementationType.cameraAwesome:
          result = await CameraAwesomeImplementation.takePicture();
          break;

        case CameraImplementationType.flutterCamera:
          result = await FlutterCameraImplementation.takePicture();
          break;

        case CameraImplementationType.native:
          result = await NativeImplementation.takePicture();
          break;
      }

      _updateStatus(CameraStatus.initialized);

      // 发送拍照完成事件
      _eventController
          .add(CameraCaptureCompletedEvent(result, _currentImplementation!));

      return result;
    } catch (e) {
      _updateStatus(CameraStatus.initialized);

      if (e is CameraError) {
        // 如果拍照失败且启用了自动降级，尝试切换实现方案
        if (_priority.enableAutoFallback &&
            _retryCount < _priority.maxRetryCount) {
          await _tryFallbackImplementation('拍照失败');
          return await takePicture(); // 递归重试
        }
        rethrow;
      } else {
        throw CameraError(
          code: 'CAPTURE_FAILED',
          message: '拍照失败: ${e.toString()}',
        );
      }
    }
  }

  /// 切换摄像头方向
  Future<void> switchCamera(CameraLensDirection direction) async {
    if (_status != CameraStatus.initialized || _currentImplementation == null) {
      throw CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    try {
      final oldDirection = _getCurrentDirection();

      switch (_currentImplementation!) {
        case CameraImplementationType.cameraAwesome:
          await CameraAwesomeImplementation.switchCamera(direction);
          break;

        case CameraImplementationType.flutterCamera:
          await FlutterCameraImplementation.switchCamera(direction);
          break;

        case CameraImplementationType.native:
          await NativeImplementation.switchCamera(direction);
          break;
      }

      // 发送摄像头方向切换事件
      _eventController
          .add(CameraDirectionSwitchedEvent(oldDirection, direction));
    } catch (e) {
      if (e is CameraError) {
        rethrow;
      } else {
        throw CameraError(
          code: 'SWITCH_CAMERA_FAILED',
          message: '切换摄像头失败: ${e.toString()}',
        );
      }
    }
  }

  /// 设置闪光灯模式
  Future<void> setFlashMode(FlashMode mode) async {
    if (_status != CameraStatus.initialized || _currentImplementation == null) {
      throw CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    try {
      final oldMode = _getCurrentFlashMode();

      switch (_currentImplementation!) {
        case CameraImplementationType.cameraAwesome:
          await CameraAwesomeImplementation.setFlashMode(mode);
          break;

        case CameraImplementationType.flutterCamera:
          await FlutterCameraImplementation.setFlashMode(mode);
          break;

        case CameraImplementationType.native:
          await NativeImplementation.setFlashMode(mode);
          break;
      }

      // 发送闪光灯模式变化事件
      _eventController.add(FlashModeChangedEvent(oldMode, mode));
    } catch (e) {
      if (e is CameraError) {
        rethrow;
      } else {
        throw CameraError(
          code: 'SET_FLASH_MODE_FAILED',
          message: '设置闪光灯模式失败: ${e.toString()}',
        );
      }
    }
  }

  /// 手动切换实现方案
  Future<void> switchImplementation(
      CameraImplementationType implementationType) async {
    if (!_availableImplementations.contains(implementationType)) {
      throw CameraError(
        code: 'IMPLEMENTATION_NOT_AVAILABLE',
        message: '指定的实现方案不可用',
      );
    }

    if (_currentImplementation == implementationType) {
      return; // 已经是当前实现方案
    }

    try {
      final oldImplementation = _currentImplementation;

      // 释放当前实现
      await _disposeCurrentImplementation();

      // 初始化新实现
      await _initializeImplementation(implementationType);

      // 发送实现方案切换事件
      if (oldImplementation != null) {
        _eventController.add(CameraImplementationSwitchedEvent(
          oldImplementation,
          implementationType,
          '用户手动切换',
        ));
      }

      // 保存用户偏好
      await _saveUserPreference(implementationType);
    } catch (e) {
      throw CameraError(
        code: 'SWITCH_IMPLEMENTATION_FAILED',
        message: '切换实现方案失败: ${e.toString()}',
      );
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    _updateStatus(CameraStatus.disposed);

    await _disposeCurrentImplementation();

    await _eventController.close();
    await _statusController.close();
    await _errorController.close();
  }

  // ========== 私有方法 ==========

  /// 更新状态
  void _updateStatus(CameraStatus status) {
    _status = status;
    _statusController.add(status);
    _eventController
        .add(CameraStatusChangedEvent(status, _currentImplementation));
  }

  /// 处理错误
  void _handleError(CameraError error) {
    _updateStatus(CameraStatus.error);
    _errorController.add(error);
    _eventController.add(CameraErrorEvent(error, _currentImplementation));
  }

  /// 检测可用的实现方案
  Future<void> _detectAvailableImplementations() async {
    _availableImplementations.clear();

    // 检测CameraAwesome
    if (await CameraAwesomeImplementation.isAvailable()) {
      _availableImplementations.add(CameraImplementationType.cameraAwesome);
    }

    // 检测Flutter Camera
    if (await FlutterCameraImplementation.isAvailable()) {
      _availableImplementations.add(CameraImplementationType.flutterCamera);
    }

    // 检测原生实现
    if (await NativeImplementation.isAvailable()) {
      _availableImplementations.add(CameraImplementationType.native);
    }
  }

  /// 选择最佳实现方案
  Future<CameraImplementationType?> _selectBestImplementation() async {
    // 首先尝试从用户偏好中获取
    final userPreference = await _getUserPreference();
    if (userPreference != null &&
        _availableImplementations.contains(userPreference)) {
      return userPreference;
    }

    // 根据平台和设备特性选择最佳方案
    if (Platform.isIOS) {
      // iOS平台优先级：原生 > CameraAwesome > Flutter Camera
      for (final impl in _priority.iosPriority) {
        if (_availableImplementations.contains(impl)) {
          return impl;
        }
      }
    } else if (Platform.isAndroid) {
      // Android平台优先级：CameraAwesome > Flutter Camera > 原生
      for (final impl in _priority.androidPriority) {
        if (_availableImplementations.contains(impl)) {
          return impl;
        }
      }
    }

    return _availableImplementations.isNotEmpty
        ? _availableImplementations.first
        : null;
  }

  /// 初始化指定的实现方案
  Future<void> _initializeImplementation(
      CameraImplementationType implementationType) async {
    if (_config == null) {
      throw CameraError(
        code: 'NO_CONFIG',
        message: '摄像头配置为空',
      );
    }

    switch (implementationType) {
      case CameraImplementationType.cameraAwesome:
        await CameraAwesomeImplementation.initialize(_config!);
        break;

      case CameraImplementationType.flutterCamera:
        await FlutterCameraImplementation.initialize(_config!);
        break;

      case CameraImplementationType.native:
        await NativeImplementation.initialize(_config!);
        break;
    }

    _currentImplementation = implementationType;
    _retryCount = 0; // 重置重试计数
  }

  /// 释放当前实现
  Future<void> _disposeCurrentImplementation() async {
    if (_currentImplementation == null) return;

    switch (_currentImplementation!) {
      case CameraImplementationType.cameraAwesome:
        await CameraAwesomeImplementation.dispose();
        break;

      case CameraImplementationType.flutterCamera:
        await FlutterCameraImplementation.dispose();
        break;

      case CameraImplementationType.native:
        await NativeImplementation.dispose();
        break;
    }

    _currentImplementation = null;
  }

  /// 保存用户偏好
  Future<void> _saveUserPreference(
      CameraImplementationType implementationType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          'multi_camera_preferred_implementation', implementationType.name);
    } catch (e) {
      // 忽略保存偏好失败的错误
    }
  }

  /// 获取用户偏好
  Future<CameraImplementationType?> _getUserPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final prefString =
          prefs.getString('multi_camera_preferred_implementation');
      if (prefString != null) {
        return CameraImplementationType.values.firstWhere(
          (e) => e.name == prefString,
          orElse: () => CameraImplementationType.cameraAwesome,
        );
      }
    } catch (e) {
      // 忽略获取偏好失败的错误
    }
    return null;
  }

  /// 获取当前摄像头方向
  CameraLensDirection _getCurrentDirection() {
    switch (_currentImplementation) {
      case CameraImplementationType.cameraAwesome:
        return CameraAwesomeImplementation.currentDirection;
      case CameraImplementationType.flutterCamera:
        return FlutterCameraImplementation.currentDirection;
      case CameraImplementationType.native:
        return NativeImplementation.currentDirection;
      case null:
        return CameraLensDirection.back;
    }
  }

  /// 获取当前闪光灯模式
  FlashMode _getCurrentFlashMode() {
    switch (_currentImplementation) {
      case CameraImplementationType.cameraAwesome:
        return CameraAwesomeImplementation.currentFlashMode;
      case CameraImplementationType.flutterCamera:
        return FlutterCameraImplementation.currentFlashMode;
      case CameraImplementationType.native:
        return NativeImplementation.currentFlashMode;
      case null:
        return FlashMode.off;
    }
  }

  /// 尝试降级到其他实现方案
  Future<void> _tryFallbackImplementation(String reason) async {
    if (!_priority.enableAutoFallback ||
        _retryCount >= _priority.maxRetryCount) {
      return;
    }

    _retryCount++;

    // 获取当前平台的优先级列表
    final priorityList =
        Platform.isIOS ? _priority.iosPriority : _priority.androidPriority;

    // 找到当前实现在优先级列表中的位置
    int currentIndex = -1;
    if (_currentImplementation != null) {
      currentIndex = priorityList.indexOf(_currentImplementation!);
    }

    // 尝试下一个可用的实现方案
    for (int i = currentIndex + 1; i < priorityList.length; i++) {
      final nextImpl = priorityList[i];
      if (_availableImplementations.contains(nextImpl)) {
        try {
          final oldImplementation = _currentImplementation;

          // 释放当前实现
          await _disposeCurrentImplementation();

          // 初始化新实现
          await _initializeImplementation(nextImpl);

          // 发送自动切换事件
          if (oldImplementation != null) {
            _eventController.add(CameraImplementationSwitchedEvent(
              oldImplementation,
              nextImpl,
              '自动降级: $reason',
            ));
          }

          return;
        } catch (e) {
          // 如果这个实现也失败了，继续尝试下一个
          continue;
        }
      }
    }

    // 如果所有实现都失败了，抛出错误
    throw CameraError(
      code: 'ALL_IMPLEMENTATIONS_FAILED',
      message: '所有摄像头实现方案都失败了',
    );
  }
}

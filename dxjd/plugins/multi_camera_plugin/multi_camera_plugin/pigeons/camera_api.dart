import 'package:pigeon/pigeon.dart';

/// 配置Pigeon生成选项
@ConfigurePigeon(PigeonOptions(
  dartOut: 'lib/src/generated/camera_api.g.dart',
  kotlinOut: 'android/src/main/kotlin/com/dxjd/multi_camera_plugin/CameraGeneratedApis.g.kt',
  kotlinOptions: KotlinOptions(
    errorClassName: "MultiCameraFlutterError",
  ),
  swiftOut: 'ios/Classes/CameraGeneratedApis.g.swift',
))

/// 摄像头实现类型枚举
enum CameraImplementationType {
  /// CameraAwesome插件实现
  cameraAwesome,
  /// Flutter Camera插件实现
  flutterCamera,
  /// 原生实现
  native,
}

/// 摄像头状态枚举
enum CameraStatus {
  /// 未初始化
  uninitialized,
  /// 初始化中
  initializing,
  /// 已初始化
  initialized,
  /// 拍照中
  takingPicture,
  /// 错误状态
  error,
  /// 已释放
  disposed,
}

/// 摄像头方向枚举
enum CameraLensDirection {
  /// 前置摄像头
  front,
  /// 后置摄像头
  back,
  /// 外部摄像头
  external,
}

/// 闪光灯模式枚举
enum FlashMode {
  /// 关闭
  off,
  /// 开启
  on,
  /// 自动
  auto,
  /// 常亮
  torch,
}

/// 摄像头配置
class CameraConfig {
  /// 摄像头方向
  final CameraLensDirection lensDirection;
  /// 是否启用音频
  final bool enableAudio;
  /// 图片质量 (0.0-1.0)
  final double imageQuality;
  /// 摄像头窗口宽度
  final double? width;
  /// 摄像头窗口高度
  final double? height;
  /// 是否为圆形窗口
  final bool isCircular;
  /// 是否为正方形窗口
  final bool isSquare;

  CameraConfig({
    required this.lensDirection,
    this.enableAudio = false,
    this.imageQuality = 0.8,
    this.width,
    this.height,
    this.isCircular = false,
    this.isSquare = false,
  });
}

/// 拍照结果
class CaptureResult {
  /// 图片文件路径
  final String imagePath;
  /// 拍照时间戳
  final int timestamp;
  /// 图片大小（字节）
  final int fileSize;

  CaptureResult({
    required this.imagePath,
    required this.timestamp,
    required this.fileSize,
  });
}

/// 摄像头错误信息
class CameraError {
  /// 错误代码
  final String code;
  /// 错误消息
  final String message;
  /// 错误详情
  final String? details;

  CameraError({
    required this.code,
    required this.message,
    this.details,
  });
}

/// 摄像头能力信息
class CameraCapabilities {
  /// 支持的实现类型列表
  final List<CameraImplementationType> supportedImplementations;
  /// 是否支持前置摄像头
  final bool hasFrontCamera;
  /// 是否支持后置摄像头
  final bool hasBackCamera;
  /// 是否支持闪光灯
  final bool hasFlash;
  /// 是否支持变焦
  final bool hasZoom;

  CameraCapabilities({
    required this.supportedImplementations,
    required this.hasFrontCamera,
    required this.hasBackCamera,
    required this.hasFlash,
    required this.hasZoom,
  });
}

/// 主机端API接口（Flutter调用原生）
@HostApi()
abstract class MultiCameraHostApi {
  /// 获取摄像头能力信息
  @async
  CameraCapabilities getCameraCapabilities();

  /// 初始化摄像头
  @async
  void initializeCamera(CameraConfig config, CameraImplementationType implementationType);

  /// 切换摄像头实现方案
  @async
  void switchImplementation(CameraImplementationType implementationType);

  /// 切换摄像头方向（前置/后置）
  @async
  void switchCamera(CameraLensDirection direction);

  /// 拍照
  @async
  CaptureResult takePicture();

  /// 设置闪光灯模式
  @async
  void setFlashMode(FlashMode mode);

  /// 开始预览
  @async
  void startPreview();

  /// 停止预览
  @async
  void stopPreview();

  /// 释放摄像头资源
  @async
  void dispose();

  /// 检查特定实现是否可用
  @async
  bool isImplementationAvailable(CameraImplementationType implementationType);
}

/// Flutter端API接口（原生调用Flutter）
@FlutterApi()
abstract class MultiCameraFlutterApi {
  /// 摄像头状态变化回调
  void onCameraStatusChanged(CameraStatus status);

  /// 摄像头错误回调
  void onCameraError(CameraError error);

  /// 实现方案自动切换回调
  void onImplementationSwitched(CameraImplementationType newImplementation, String reason);
}

import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'models/camera_models.dart';
import 'implementations/camera_awesome_implementation.dart';
import 'implementations/flutter_camera_implementation.dart';
import 'implementations/native_camera_implementation.dart';

/// 多摄像头管理器
/// 
/// 这个类负责管理多种摄像头实现方案，提供统一的API接口，
/// 并根据设备兼容性自动选择最佳的摄像头实现方案。
class MultiCameraManager {
  static MultiCameraManager? _instance;
  static MultiCameraManager get instance => _instance ??= MultiCameraManager._();
  
  MultiCameraManager._();

  /// 当前配置
  CameraConfig _config = const CameraConfig();
  
  /// 当前状态
  CameraStatus _status = CameraStatus.uninitialized;
  
  /// 当前使用的实现
  CameraImplementationType? _currentImplementation;
  
  /// 可用的实现列表（按优先级排序）
  List<CameraImplementationType> _availableImplementations = [];
  
  /// 状态变化流控制器
  final StreamController<CameraStatus> _statusController = StreamController<CameraStatus>.broadcast();
  
  /// 错误流控制器
  final StreamController<String> _errorController = StreamController<String>.broadcast();

  /// 状态流
  Stream<CameraStatus> get statusStream => _statusController.stream;
  
  /// 错误流
  Stream<String> get errorStream => _errorController.stream;
  
  /// 当前状态
  CameraStatus get status => _status;
  
  /// 当前配置
  CameraConfig get config => _config;
  
  /// 当前使用的实现类型
  CameraImplementationType? get currentImplementation => _currentImplementation;

  /// 初始化摄像头管理器
  /// 
  /// [config] 摄像头配置
  /// [forceImplementation] 强制使用指定的实现类型
  Future<bool> initialize({
    CameraConfig? config,
    CameraImplementationType? forceImplementation,
  }) async {
    try {
      _updateStatus(CameraStatus.initializing);
      
      if (config != null) {
        _config = config;
      }
      
      // 检测可用的实现方案
      await _detectAvailableImplementations();
      
      // 选择实现方案
      CameraImplementationType? selectedImplementation;
      if (forceImplementation != null && _availableImplementations.contains(forceImplementation)) {
        selectedImplementation = forceImplementation;
      } else if (_config.implementationType != CameraImplementationType.auto) {
        selectedImplementation = _config.implementationType;
      } else {
        selectedImplementation = await _selectBestImplementation();
      }
      
      if (selectedImplementation == null) {
        _emitError('没有可用的摄像头实现方案');
        _updateStatus(CameraStatus.error);
        return false;
      }
      
      // 初始化选定的实现
      final success = await _initializeImplementation(selectedImplementation);
      if (success) {
        _currentImplementation = selectedImplementation;
        _updateStatus(CameraStatus.ready);
        
        // 保存用户选择
        await _saveUserPreference(selectedImplementation);
      } else {
        _updateStatus(CameraStatus.error);
      }
      
      return success;
    } catch (e) {
      _emitError('初始化失败: $e');
      _updateStatus(CameraStatus.error);
      return false;
    }
  }

  /// 拍照
  Future<CameraResult> takePicture() async {
    if (_status != CameraStatus.ready) {
      return CameraResult.failure('摄像头未准备就绪');
    }
    
    if (_currentImplementation == null) {
      return CameraResult.failure('没有可用的摄像头实现');
    }
    
    try {
      _updateStatus(CameraStatus.takingPicture);
      
      final result = await _takePictureWithImplementation(_currentImplementation!);
      
      _updateStatus(CameraStatus.ready);
      return result;
    } catch (e) {
      _updateStatus(CameraStatus.ready);
      return CameraResult.failure('拍照失败: $e');
    }
  }

  /// 切换摄像头方向
  Future<bool> switchCamera() async {
    final newDirection = _config.lensDirection == CameraLensDirection.front 
        ? CameraLensDirection.back 
        : CameraLensDirection.front;
    
    return await updateConfig(_config.copyWith(lensDirection: newDirection));
  }

  /// 切换闪光灯模式
  Future<bool> toggleFlash() async {
    FlashMode newMode;
    switch (_config.flashMode) {
      case FlashMode.off:
        newMode = FlashMode.on;
        break;
      case FlashMode.on:
        newMode = FlashMode.auto;
        break;
      case FlashMode.auto:
        newMode = FlashMode.off;
        break;
      case FlashMode.torch:
        newMode = FlashMode.off;
        break;
    }
    
    return await updateConfig(_config.copyWith(flashMode: newMode));
  }

  /// 更新配置
  Future<bool> updateConfig(CameraConfig newConfig) async {
    try {
      _config = newConfig;
      
      if (_status == CameraStatus.ready && _currentImplementation != null) {
        // 重新初始化以应用新配置
        return await _initializeImplementation(_currentImplementation!);
      }
      
      return true;
    } catch (e) {
      _emitError('更新配置失败: $e');
      return false;
    }
  }

  /// 手动切换实现方案
  Future<bool> switchImplementation(CameraImplementationType implementation) async {
    if (!_availableImplementations.contains(implementation)) {
      _emitError('不支持的实现方案: $implementation');
      return false;
    }
    
    try {
      _updateStatus(CameraStatus.initializing);
      
      final success = await _initializeImplementation(implementation);
      if (success) {
        _currentImplementation = implementation;
        _updateStatus(CameraStatus.ready);
        
        // 保存用户选择
        await _saveUserPreference(implementation);
      } else {
        _updateStatus(CameraStatus.error);
      }
      
      return success;
    } catch (e) {
      _emitError('切换实现方案失败: $e');
      _updateStatus(CameraStatus.error);
      return false;
    }
  }

  /// 获取可用的实现方案列表
  List<CameraImplementationType> getAvailableImplementations() {
    return List.from(_availableImplementations);
  }

  /// 释放资源
  Future<void> dispose() async {
    _updateStatus(CameraStatus.disposed);
    await _statusController.close();
    await _errorController.close();
  }

  /// 检测可用的实现方案
  Future<void> _detectAvailableImplementations() async {
    _availableImplementations.clear();
    
    // 检测CameraAwesome
    if (await CameraAwesomeImplementation.isAvailable()) {
      _availableImplementations.add(CameraImplementationType.cameraAwesome);
    }
    
    // 检测Flutter Camera
    if (await FlutterCameraImplementation.isAvailable()) {
      _availableImplementations.add(CameraImplementationType.flutterCamera);
    }
    
    // 检测原生实现
    if (await NativeCameraImplementation.isAvailable()) {
      _availableImplementations.add(CameraImplementationType.native);
    }
  }

  /// 选择最佳实现方案
  Future<CameraImplementationType?> _selectBestImplementation() async {
    // 首先尝试获取用户之前的选择
    final userPreference = await _getUserPreference();
    if (userPreference != null && _availableImplementations.contains(userPreference)) {
      return userPreference;
    }
    
    // 根据平台和设备特性选择最佳方案
    if (Platform.isIOS) {
      // iOS平台优先级：原生 > CameraAwesome > Flutter Camera
      for (final impl in [
        CameraImplementationType.native,
        CameraImplementationType.cameraAwesome,
        CameraImplementationType.flutterCamera,
      ]) {
        if (_availableImplementations.contains(impl)) {
          return impl;
        }
      }
    } else if (Platform.isAndroid) {
      // Android平台优先级：CameraAwesome > Flutter Camera > 原生
      for (final impl in [
        CameraImplementationType.cameraAwesome,
        CameraImplementationType.flutterCamera,
        CameraImplementationType.native,
      ]) {
        if (_availableImplementations.contains(impl)) {
          return impl;
        }
      }
    }
    
    return _availableImplementations.isNotEmpty ? _availableImplementations.first : null;
  }

  /// 初始化指定的实现方案
  Future<bool> _initializeImplementation(CameraImplementationType implementation) async {
    switch (implementation) {
      case CameraImplementationType.cameraAwesome:
        return await CameraAwesomeImplementation.initialize(_config);
      case CameraImplementationType.flutterCamera:
        return await FlutterCameraImplementation.initialize(_config);
      case CameraImplementationType.native:
        return await NativeCameraImplementation.initialize(_config);
      case CameraImplementationType.auto:
        throw ArgumentError('Auto implementation should be resolved before initialization');
    }
  }

  /// 使用指定实现拍照
  Future<CameraResult> _takePictureWithImplementation(CameraImplementationType implementation) async {
    switch (implementation) {
      case CameraImplementationType.cameraAwesome:
        return await CameraAwesomeImplementation.takePicture();
      case CameraImplementationType.flutterCamera:
        return await FlutterCameraImplementation.takePicture();
      case CameraImplementationType.native:
        return await NativeCameraImplementation.takePicture();
      case CameraImplementationType.auto:
        throw ArgumentError('Auto implementation should be resolved before taking picture');
    }
  }

  /// 保存用户偏好
  Future<void> _saveUserPreference(CameraImplementationType implementation) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('camera_implementation_type', implementation.index);
  }

  /// 获取用户偏好
  Future<CameraImplementationType?> _getUserPreference() async {
    final prefs = await SharedPreferences.getInstance();
    final index = prefs.getInt('camera_implementation_type');
    if (index != null && index >= 0 && index < CameraImplementationType.values.length) {
      return CameraImplementationType.values[index];
    }
    return null;
  }

  /// 更新状态
  void _updateStatus(CameraStatus status) {
    _status = status;
    _statusController.add(status);
  }

  /// 发送错误
  void _emitError(String error) {
    _errorController.add(error);
  }
}

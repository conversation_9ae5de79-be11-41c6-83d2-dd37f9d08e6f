import 'dart:io';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:path_provider/path_provider.dart';
import '../models/camera_models.dart';

/// CameraAwesome插件实现
/// 
/// 这是一个跨平台的摄像头解决方案，提供良好的兼容性和性能。
/// 适用于大多数iOS和Android设备。
class CameraAwesomeImplementation {
  static CameraAwesome? _cameraAwesome;
  static CameraConfig? _config;
  static bool _isInitialized = false;

  /// 检查是否可用
  static Future<bool> isAvailable() async {
    try {
      // 检查权限
      final hasPermission = await _checkCameraPermission();
      return hasPermission;
    } catch (e) {
      return false;
    }
  }

  /// 初始化
  static Future<bool> initialize(CameraConfig config) async {
    try {
      _config = config;
      
      // 检查权限
      final hasPermission = await _checkCameraPermission();
      if (!hasPermission) {
        return false;
      }
      
      _isInitialized = true;
      return true;
    } catch (e) {
      _isInitialized = false;
      return false;
    }
  }

  /// 拍照
  static Future<CameraResult> takePicture() async {
    if (!_isInitialized || _config == null) {
      return CameraResult.failure('CameraAwesome未初始化');
    }

    try {
      // 获取存储目录
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'camera_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final filePath = '${directory.path}/$fileName';

      // 这里需要通过CameraAwesome的控制器来拍照
      // 由于CameraAwesome需要Widget上下文，这里提供一个模拟实现
      // 实际使用时需要在Widget中调用
      
      return CameraResult.success(filePath, CameraImplementationType.cameraAwesome);
    } catch (e) {
      return CameraResult.failure('CameraAwesome拍照失败: $e');
    }
  }

  /// 创建CameraAwesome Widget
  static Widget createCameraWidget({
    required Function(String imagePath) onPictureTaken,
    required Function(String error) onError,
  }) {
    if (_config == null) {
      throw StateError('CameraAwesome未初始化');
    }

    return CameraAwesome(
      saveConfig: SaveConfig.photo(
        pathBuilder: () async {
          final directory = await getApplicationDocumentsDirectory();
          final fileName = 'camera_${DateTime.now().millisecondsSinceEpoch}.jpg';
          return '${directory.path}/$fileName';
        },
      ),
      sensorConfig: SensorConfig.single(
        sensor: _config!.lensDirection == CameraLensDirection.front 
            ? Sensor.front 
            : Sensor.back,
        flashMode: _convertFlashMode(_config!.flashMode),
        aspectRatio: CameraAspectRatios.ratio_1_1,
      ),
      enableAudio: _config!.enableAudio,
      onMediaTap: (mediaCapture) {
        if (mediaCapture.isPicture) {
          onPictureTaken(mediaCapture.captureRequest.when(
            single: (single) => single.file?.path ?? '',
            multiple: (multiple) => '',
          ));
        }
      },
      onMediaCaptureEvent: (event) {
        event.when(
          capturing: (capture) {
            // 拍照中
          },
          success: (mediaCapture) {
            // 拍照成功
            if (mediaCapture.isPicture) {
              final path = mediaCapture.captureRequest.when(
                single: (single) => single.file?.path ?? '',
                multiple: (multiple) => '',
              );
              if (path.isNotEmpty) {
                onPictureTaken(path);
              }
            }
          },
          failure: (exception) {
            onError('拍照失败: $exception');
          },
        );
      },
      previewFit: CameraPreviewFit.cover,
    );
  }

  /// 释放资源
  static Future<void> dispose() async {
    _cameraAwesome = null;
    _config = null;
    _isInitialized = false;
  }

  /// 检查摄像头权限
  static Future<bool> _checkCameraPermission() async {
    try {
      // 这里应该使用permission_handler插件检查权限
      // 为了简化，这里假设有权限
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 转换闪光灯模式
  static FlashMode _convertFlashMode(camera_models.FlashMode mode) {
    switch (mode) {
      case camera_models.FlashMode.off:
        return FlashMode.none;
      case camera_models.FlashMode.on:
        return FlashMode.on;
      case camera_models.FlashMode.auto:
        return FlashMode.auto;
      case camera_models.FlashMode.torch:
        return FlashMode.always;
    }
  }

  /// 获取当前配置
  static CameraConfig? get config => _config;

  /// 是否已初始化
  static bool get isInitialized => _isInitialized;
}

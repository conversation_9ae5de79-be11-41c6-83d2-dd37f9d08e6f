import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../models/camera_models.dart' as models;

/// Flutter Camera插件实现
/// 
/// 这是Flutter官方提供的摄像头插件，具有良好的稳定性和性能。
/// 适用于大多数iOS和Android设备。
class FlutterCameraImplementation {
  static CameraController? _controller;
  static models.CameraConfig? _config;
  static List<CameraDescription>? _cameras;
  static bool _isInitialized = false;

  /// 检查是否可用
  static Future<bool> isAvailable() async {
    try {
      final cameras = await availableCameras();
      return cameras.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 初始化
  static Future<bool> initialize(models.CameraConfig config) async {
    try {
      _config = config;
      
      // 获取可用摄像头
      _cameras = await availableCameras();
      if (_cameras == null || _cameras!.isEmpty) {
        return false;
      }

      // 选择摄像头
      CameraDescription? selectedCamera;
      for (final camera in _cameras!) {
        if (config.lensDirection == models.CameraLensDirection.front &&
            camera.lensDirection == CameraLensDirection.front) {
          selectedCamera = camera;
          break;
        } else if (config.lensDirection == models.CameraLensDirection.back &&
                   camera.lensDirection == CameraLensDirection.back) {
          selectedCamera = camera;
          break;
        }
      }

      selectedCamera ??= _cameras!.first;

      // 创建控制器
      _controller = CameraController(
        selectedCamera,
        ResolutionPreset.high,
        enableAudio: config.enableAudio,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      // 初始化控制器
      await _controller!.initialize();
      
      // 设置闪光灯模式
      await _controller!.setFlashMode(_convertFlashMode(config.flashMode));

      _isInitialized = true;
      return true;
    } catch (e) {
      _isInitialized = false;
      await _controller?.dispose();
      _controller = null;
      return false;
    }
  }

  /// 拍照
  static Future<models.CameraResult> takePicture() async {
    if (!_isInitialized || _controller == null || !_controller!.value.isInitialized) {
      return models.CameraResult.failure('Flutter Camera未初始化');
    }

    try {
      // 拍照
      final XFile image = await _controller!.takePicture();
      
      // 移动到应用文档目录
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'camera_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final newPath = '${directory.path}/$fileName';
      
      await File(image.path).copy(newPath);
      
      return models.CameraResult.success(newPath, models.CameraImplementationType.flutterCamera);
    } catch (e) {
      return models.CameraResult.failure('Flutter Camera拍照失败: $e');
    }
  }

  /// 创建Camera预览Widget
  static Widget createCameraPreview() {
    if (_controller == null || !_controller!.value.isInitialized) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return CameraPreview(_controller!);
  }

  /// 切换摄像头
  static Future<bool> switchCamera() async {
    if (_cameras == null || _cameras!.length < 2 || _config == null) {
      return false;
    }

    try {
      // 找到另一个摄像头
      final currentDirection = _controller?.description.lensDirection;
      CameraDescription? newCamera;
      
      for (final camera in _cameras!) {
        if (camera.lensDirection != currentDirection) {
          newCamera = camera;
          break;
        }
      }

      if (newCamera == null) {
        return false;
      }

      // 释放当前控制器
      await _controller?.dispose();

      // 创建新控制器
      _controller = CameraController(
        newCamera,
        ResolutionPreset.high,
        enableAudio: _config!.enableAudio,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      // 初始化新控制器
      await _controller!.initialize();
      
      // 设置闪光灯模式
      await _controller!.setFlashMode(_convertFlashMode(_config!.flashMode));

      // 更新配置
      _config = _config!.copyWith(
        lensDirection: newCamera.lensDirection == CameraLensDirection.front 
            ? models.CameraLensDirection.front 
            : models.CameraLensDirection.back,
      );

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 设置闪光灯模式
  static Future<bool> setFlashMode(models.FlashMode flashMode) async {
    if (_controller == null || !_controller!.value.isInitialized) {
      return false;
    }

    try {
      await _controller!.setFlashMode(_convertFlashMode(flashMode));
      
      if (_config != null) {
        _config = _config!.copyWith(flashMode: flashMode);
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 设置缩放
  static Future<bool> setZoomLevel(double zoom) async {
    if (_controller == null || !_controller!.value.isInitialized) {
      return false;
    }

    try {
      final maxZoom = await _controller!.getMaxZoomLevel();
      final minZoom = await _controller!.getMinZoomLevel();
      
      final clampedZoom = zoom.clamp(minZoom, maxZoom);
      await _controller!.setZoomLevel(clampedZoom);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 释放资源
  static Future<void> dispose() async {
    await _controller?.dispose();
    _controller = null;
    _config = null;
    _cameras = null;
    _isInitialized = false;
  }

  /// 转换闪光灯模式
  static FlashMode _convertFlashMode(models.FlashMode mode) {
    switch (mode) {
      case models.FlashMode.off:
        return FlashMode.off;
      case models.FlashMode.on:
        return FlashMode.always;
      case models.FlashMode.auto:
        return FlashMode.auto;
      case models.FlashMode.torch:
        return FlashMode.torch;
    }
  }

  /// 获取当前控制器
  static CameraController? get controller => _controller;

  /// 获取当前配置
  static models.CameraConfig? get config => _config;

  /// 是否已初始化
  static bool get isInitialized => _isInitialized;

  /// 获取可用摄像头列表
  static List<CameraDescription>? get cameras => _cameras;
}

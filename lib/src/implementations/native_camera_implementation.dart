import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import '../models/camera_models.dart';

/// 原生摄像头实现
/// 
/// 这个实现使用平台特定的原生代码来提供摄像头功能。
/// 在iOS上使用AVFoundation，在Android上使用Camera2 API。
/// 提供最佳的性能和平台特定的功能。
class NativeCameraImplementation {
  static const MethodChannel _channel = MethodChannel('multi_camera_plugin/native');
  static CameraConfig? _config;
  static bool _isInitialized = false;
  static int? _viewId;

  /// 检查是否可用
  static Future<bool> isAvailable() async {
    try {
      final result = await _channel.invokeMethod<bool>('isAvailable');
      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  /// 初始化
  static Future<bool> initialize(CameraConfig config) async {
    try {
      _config = config;
      
      final result = await _channel.invokeMethod<bool>('initialize', {
        'lensDirection': config.lensDirection.index,
        'flashMode': config.flashMode.index,
        'width': config.width,
        'height': config.height,
        'enableAudio': config.enableAudio,
        'autoFocus': config.autoFocus,
        'imageQuality': config.imageQuality,
      });

      _isInitialized = result ?? false;
      return _isInitialized;
    } catch (e) {
      _isInitialized = false;
      return false;
    }
  }

  /// 拍照
  static Future<CameraResult> takePicture() async {
    if (!_isInitialized) {
      return CameraResult.failure('原生摄像头未初始化');
    }

    try {
      final result = await _channel.invokeMethod<Map<dynamic, dynamic>>('takePicture');
      
      if (result != null) {
        final success = result['success'] as bool? ?? false;
        if (success) {
          final imagePath = result['imagePath'] as String?;
          if (imagePath != null) {
            return CameraResult.success(imagePath, CameraImplementationType.native);
          }
        }
        
        final error = result['error'] as String? ?? '未知错误';
        return CameraResult.failure(error);
      }
      
      return CameraResult.failure('拍照失败：无响应');
    } catch (e) {
      return CameraResult.failure('原生摄像头拍照失败: $e');
    }
  }

  /// 创建原生摄像头预览Widget
  static Widget createCameraPreview({
    required Function(String imagePath) onPictureTaken,
    required Function(String error) onError,
  }) {
    if (_config == null) {
      throw StateError('原生摄像头未初始化');
    }

    if (Platform.isIOS) {
      return UiKitView(
        viewType: 'multi_camera_plugin/native_camera_preview',
        creationParams: {
          'width': _config!.width,
          'height': _config!.height,
          'lensDirection': _config!.lensDirection.index,
          'flashMode': _config!.flashMode.index,
        },
        creationParamsCodec: const StandardMessageCodec(),
        onPlatformViewCreated: (id) {
          _viewId = id;
          _setupMethodChannel(id, onPictureTaken, onError);
        },
      );
    } else if (Platform.isAndroid) {
      return AndroidView(
        viewType: 'multi_camera_plugin/native_camera_preview',
        creationParams: {
          'width': _config!.width,
          'height': _config!.height,
          'lensDirection': _config!.lensDirection.index,
          'flashMode': _config!.flashMode.index,
        },
        creationParamsCodec: const StandardMessageCodec(),
        onPlatformViewCreated: (id) {
          _viewId = id;
          _setupMethodChannel(id, onPictureTaken, onError);
        },
      );
    }

    return Container(
      width: _config!.width,
      height: _config!.height,
      color: Colors.black,
      child: const Center(
        child: Text(
          '不支持的平台',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  /// 切换摄像头
  static Future<bool> switchCamera() async {
    if (!_isInitialized) {
      return false;
    }

    try {
      final result = await _channel.invokeMethod<bool>('switchCamera');
      
      if (result == true && _config != null) {
        // 更新配置
        _config = _config!.copyWith(
          lensDirection: _config!.lensDirection == CameraLensDirection.front 
              ? CameraLensDirection.back 
              : CameraLensDirection.front,
        );
      }
      
      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  /// 设置闪光灯模式
  static Future<bool> setFlashMode(FlashMode flashMode) async {
    if (!_isInitialized) {
      return false;
    }

    try {
      final result = await _channel.invokeMethod<bool>('setFlashMode', {
        'flashMode': flashMode.index,
      });

      if (result == true && _config != null) {
        _config = _config!.copyWith(flashMode: flashMode);
      }

      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  /// 开始预览
  static Future<bool> startPreview() async {
    if (!_isInitialized) {
      return false;
    }

    try {
      final result = await _channel.invokeMethod<bool>('startPreview');
      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  /// 停止预览
  static Future<bool> stopPreview() async {
    if (!_isInitialized) {
      return false;
    }

    try {
      final result = await _channel.invokeMethod<bool>('stopPreview');
      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  /// 释放资源
  static Future<void> dispose() async {
    try {
      await _channel.invokeMethod('dispose');
    } catch (e) {
      // 忽略释放时的错误
    }
    
    _config = null;
    _isInitialized = false;
    _viewId = null;
  }

  /// 设置方法通道
  static void _setupMethodChannel(
    int viewId,
    Function(String imagePath) onPictureTaken,
    Function(String error) onError,
  ) {
    final channel = MethodChannel('multi_camera_plugin/native_camera_$viewId');
    
    channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onPictureTaken':
          final imagePath = call.arguments['imagePath'] as String?;
          if (imagePath != null) {
            onPictureTaken(imagePath);
          }
          break;
        case 'onError':
          final error = call.arguments['error'] as String? ?? '未知错误';
          onError(error);
          break;
      }
    });
  }

  /// 获取当前配置
  static CameraConfig? get config => _config;

  /// 是否已初始化
  static bool get isInitialized => _isInitialized;

  /// 获取当前视图ID
  static int? get viewId => _viewId;
}

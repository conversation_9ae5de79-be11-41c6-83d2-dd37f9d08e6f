/// 摄像头实现类型枚举
enum CameraImplementationType {
  /// CameraAwesome插件 - 通用跨平台方案
  cameraAwesome,
  
  /// Flutter Camera插件 - 官方方案
  flutterCamera,
  
  /// 原生实现 - iOS/Android原生方案
  native,
  
  /// 自动选择 - 根据设备自动选择最佳方案
  auto,
}

/// 摄像头方向枚举
enum CameraLensDirection {
  /// 前置摄像头
  front,
  
  /// 后置摄像头
  back,
}

/// 闪光灯模式枚举
enum FlashMode {
  /// 关闭闪光灯
  off,
  
  /// 开启闪光灯
  on,
  
  /// 自动闪光灯
  auto,
  
  /// 手电筒模式
  torch,
}

/// 摄像头窗口形状枚举
enum CameraShape {
  /// 矩形
  rectangle,
  
  /// 正方形
  square,
  
  /// 圆形
  circle,
  
  /// 椭圆形
  oval,
}

/// 摄像头配置类
class CameraConfig {
  /// 摄像头实现类型
  final CameraImplementationType implementationType;
  
  /// 摄像头方向
  final CameraLensDirection lensDirection;
  
  /// 闪光灯模式
  final FlashMode flashMode;
  
  /// 摄像头窗口宽度
  final double width;
  
  /// 摄像头窗口高度
  final double height;
  
  /// 摄像头窗口形状
  final CameraShape shape;
  
  /// 是否启用音频
  final bool enableAudio;
  
  /// 是否自动对焦
  final bool autoFocus;
  
  /// 图片质量 (0.0 - 1.0)
  final double imageQuality;

  const CameraConfig({
    this.implementationType = CameraImplementationType.auto,
    this.lensDirection = CameraLensDirection.front,
    this.flashMode = FlashMode.off,
    this.width = 300.0,
    this.height = 300.0,
    this.shape = CameraShape.circle,
    this.enableAudio = false,
    this.autoFocus = true,
    this.imageQuality = 0.8,
  });

  CameraConfig copyWith({
    CameraImplementationType? implementationType,
    CameraLensDirection? lensDirection,
    FlashMode? flashMode,
    double? width,
    double? height,
    CameraShape? shape,
    bool? enableAudio,
    bool? autoFocus,
    double? imageQuality,
  }) {
    return CameraConfig(
      implementationType: implementationType ?? this.implementationType,
      lensDirection: lensDirection ?? this.lensDirection,
      flashMode: flashMode ?? this.flashMode,
      width: width ?? this.width,
      height: height ?? this.height,
      shape: shape ?? this.shape,
      enableAudio: enableAudio ?? this.enableAudio,
      autoFocus: autoFocus ?? this.autoFocus,
      imageQuality: imageQuality ?? this.imageQuality,
    );
  }
}

/// 拍照结果类
class CameraResult {
  /// 是否成功
  final bool success;
  
  /// 图片文件路径
  final String? imagePath;
  
  /// 错误信息
  final String? error;
  
  /// 使用的摄像头实现类型
  final CameraImplementationType? usedImplementation;

  const CameraResult({
    required this.success,
    this.imagePath,
    this.error,
    this.usedImplementation,
  });

  factory CameraResult.success(String imagePath, CameraImplementationType implementation) {
    return CameraResult(
      success: true,
      imagePath: imagePath,
      usedImplementation: implementation,
    );
  }

  factory CameraResult.failure(String error) {
    return CameraResult(
      success: false,
      error: error,
    );
  }
}

/// 摄像头状态枚举
enum CameraStatus {
  /// 未初始化
  uninitialized,
  
  /// 初始化中
  initializing,
  
  /// 已准备就绪
  ready,
  
  /// 拍照中
  takingPicture,
  
  /// 错误状态
  error,
  
  /// 已释放
  disposed,
}

/// 摄像头错误类型枚举
enum CameraErrorType {
  /// 权限被拒绝
  permissionDenied,
  
  /// 摄像头不可用
  cameraNotAvailable,
  
  /// 初始化失败
  initializationFailed,
  
  /// 拍照失败
  captureFailed,
  
  /// 未知错误
  unknown,
}

import 'package:flutter/material.dart';
import '../camera_manager.dart';
import '../models/camera_models.dart';
import '../implementations/camera_awesome_implementation.dart';
import '../implementations/flutter_camera_implementation.dart';
import '../implementations/native_camera_implementation.dart';

/// 多摄像头预览Widget
/// 
/// 这个Widget提供了一个统一的摄像头预览界面，
/// 自动根据当前使用的实现方案显示相应的预览内容。
class MultiCameraPreview extends StatefulWidget {
  /// 摄像头配置
  final CameraConfig config;
  
  /// 拍照成功回调
  final Function(String imagePath)? onPictureTaken;
  
  /// 错误回调
  final Function(String error)? onError;
  
  /// 状态变化回调
  final Function(CameraStatus status)? onStatusChanged;
  
  /// 是否显示控制按钮
  final bool showControls;
  
  /// 是否显示切换实现按钮
  final bool showSwitchImplementation;

  const MultiCameraPreview({
    Key? key,
    this.config = const CameraConfig(),
    this.onPictureTaken,
    this.onError,
    this.onStatusChanged,
    this.showControls = true,
    this.showSwitchImplementation = false,
  }) : super(key: key);

  @override
  State<MultiCameraPreview> createState() => _MultiCameraPreviewState();
}

class _MultiCameraPreviewState extends State<MultiCameraPreview> {
  final MultiCameraManager _cameraManager = MultiCameraManager.instance;
  CameraStatus _status = CameraStatus.uninitialized;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
    _setupListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// 初始化摄像头
  Future<void> _initializeCamera() async {
    final success = await _cameraManager.initialize(config: widget.config);
    if (!success) {
      setState(() {
        _error = '摄像头初始化失败';
      });
    }
  }

  /// 设置监听器
  void _setupListeners() {
    _cameraManager.statusStream.listen((status) {
      setState(() {
        _status = status;
      });
      widget.onStatusChanged?.call(status);
    });

    _cameraManager.errorStream.listen((error) {
      setState(() {
        _error = error;
      });
      widget.onError?.call(error);
    });
  }

  /// 拍照
  Future<void> _takePicture() async {
    final result = await _cameraManager.takePicture();
    if (result.success && result.imagePath != null) {
      widget.onPictureTaken?.call(result.imagePath!);
    } else {
      widget.onError?.call(result.error ?? '拍照失败');
    }
  }

  /// 切换摄像头
  Future<void> _switchCamera() async {
    await _cameraManager.switchCamera();
  }

  /// 切换闪光灯
  Future<void> _toggleFlash() async {
    await _cameraManager.toggleFlash();
  }

  /// 切换实现方案
  Future<void> _switchImplementation() async {
    final availableImplementations = _cameraManager.getAvailableImplementations();
    if (availableImplementations.length <= 1) {
      return;
    }

    final currentImplementation = _cameraManager.currentImplementation;
    CameraImplementationType? nextImplementation;

    final currentIndex = availableImplementations.indexOf(currentImplementation!);
    if (currentIndex != -1 && currentIndex < availableImplementations.length - 1) {
      nextImplementation = availableImplementations[currentIndex + 1];
    } else {
      nextImplementation = availableImplementations.first;
    }

    await _cameraManager.switchImplementation(nextImplementation);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.config.width,
      height: widget.config.height,
      decoration: BoxDecoration(
        color: Colors.black,
        shape: widget.config.shape == CameraShape.circle 
            ? BoxShape.circle 
            : BoxShape.rectangle,
        borderRadius: widget.config.shape == CameraShape.rectangle 
            ? BorderRadius.circular(8.0) 
            : null,
      ),
      child: ClipRRect(
        borderRadius: widget.config.shape == CameraShape.circle 
            ? BorderRadius.circular(widget.config.width / 2)
            : widget.config.shape == CameraShape.rectangle 
                ? BorderRadius.circular(8.0)
                : BorderRadius.zero,
        child: Stack(
          children: [
            // 摄像头预览
            _buildCameraPreview(),
            
            // 状态覆盖层
            if (_status != CameraStatus.ready) _buildStatusOverlay(),
            
            // 控制按钮
            if (widget.showControls && _status == CameraStatus.ready) 
              _buildControlButtons(),
          ],
        ),
      ),
    );
  }

  /// 构建摄像头预览
  Widget _buildCameraPreview() {
    if (_status != CameraStatus.ready) {
      return Container();
    }

    final implementation = _cameraManager.currentImplementation;
    if (implementation == null) {
      return Container();
    }

    switch (implementation) {
      case CameraImplementationType.cameraAwesome:
        return CameraAwesomeImplementation.createCameraWidget(
          onPictureTaken: (imagePath) {
            widget.onPictureTaken?.call(imagePath);
          },
          onError: (error) {
            widget.onError?.call(error);
          },
        );
      
      case CameraImplementationType.flutterCamera:
        return FlutterCameraImplementation.createCameraPreview();
      
      case CameraImplementationType.native:
        return NativeCameraImplementation.createCameraPreview(
          onPictureTaken: (imagePath) {
            widget.onPictureTaken?.call(imagePath);
          },
          onError: (error) {
            widget.onError?.call(error);
          },
        );
      
      case CameraImplementationType.auto:
        return Container();
    }
  }

  /// 构建状态覆盖层
  Widget _buildStatusOverlay() {
    String message;
    Widget? child;

    switch (_status) {
      case CameraStatus.uninitialized:
        message = '未初始化';
        break;
      case CameraStatus.initializing:
        message = '初始化中...';
        child = const CircularProgressIndicator(color: Colors.white);
        break;
      case CameraStatus.takingPicture:
        message = '拍照中...';
        child = const CircularProgressIndicator(color: Colors.white);
        break;
      case CameraStatus.error:
        message = _error ?? '发生错误';
        break;
      case CameraStatus.disposed:
        message = '已释放';
        break;
      default:
        return Container();
    }

    return Container(
      color: Colors.black54,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (child != null) ...[
              child,
              const SizedBox(height: 16),
            ],
            Text(
              message,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControlButtons() {
    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 切换摄像头按钮
          _buildControlButton(
            icon: Icons.flip_camera_ios,
            onPressed: _switchCamera,
            tooltip: '切换摄像头',
          ),
          
          // 拍照按钮
          _buildControlButton(
            icon: Icons.camera_alt,
            onPressed: _takePicture,
            tooltip: '拍照',
            size: 56,
          ),
          
          // 闪光灯按钮
          _buildControlButton(
            icon: _getFlashIcon(),
            onPressed: _toggleFlash,
            tooltip: '闪光灯',
          ),
          
          // 切换实现按钮
          if (widget.showSwitchImplementation)
            _buildControlButton(
              icon: Icons.settings,
              onPressed: _switchImplementation,
              tooltip: '切换实现',
            ),
        ],
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    double size = 48,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        width: size,
        height: size,
        decoration: const BoxDecoration(
          color: Colors.black54,
          shape: BoxShape.circle,
        ),
        child: IconButton(
          icon: Icon(icon, color: Colors.white),
          onPressed: onPressed,
        ),
      ),
    );
  }

  /// 获取闪光灯图标
  IconData _getFlashIcon() {
    switch (_cameraManager.config.flashMode) {
      case FlashMode.off:
        return Icons.flash_off;
      case FlashMode.on:
        return Icons.flash_on;
      case FlashMode.auto:
        return Icons.flash_auto;
      case FlashMode.torch:
        return Icons.flashlight_on;
    }
  }
}

name: multi_camera_plugin
description: "一个支持多种摄像头实现方案的Flutter插件，提供最大设备兼容性"
version: 1.0.0
homepage: https://github.com/example/multi_camera_plugin

environment:
  sdk: ^3.5.3
  flutter: '>=3.3.0'

dependencies:
  flutter:
    sdk: flutter
  plugin_platform_interface: ^2.0.2
  camera: ^0.10.5+9
  camerawesome: ^2.0.2
  path_provider: ^2.1.1
  shared_preferences: ^2.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  plugin:
    platforms:
      android:
        package: com.example.multi_camera_plugin
        pluginClass: MultiCameraPlugin
      ios:
        pluginClass: MultiCameraPlugin
